import { Request, Response } from 'express';
import { Account, Job, Chat, Bid } from '../database/schemas';
import { getAuthenticatedUser } from '../utils/auth';
import bcrypt from 'bcrypt';

/**
 * Utility function to escape regex special characters to prevent ReDoS attacks
 * @param string - The input string to escape
 * @returns Escaped string safe for use in regex patterns
 */
function escapeRegex(string: string): string {
  // Escape all regex special characters: . * + ? ^ $ { } ( ) | [ ] \
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Interface for account creation data
interface CreateAccountData {
  email: string;
  password: string;
  name: string;
  surname?: string;
  username?: string;
  phone?: string;
  address?: string;
  isVerified?: boolean;
}

// Strongly-typed structure for the Account document we construct and persist
interface AccountData {
  email: string;
  password: string; // hashed password
  user: {
    name: string;
    surname: string;
    username: string;
  type: 'user' | 'operator';
  };
  contacts: {
    phone: string;
    address: string;
  };
  booleans: {
    isVerified: boolean;
    isAdmin: boolean;
    isOperator: boolean;
  };
  currentRole?: string; // optional, set to 'operator' when applicable
}

/**
 * Helper function to create an account (user or operator)
 */
async function createAccount(data: CreateAccountData, userType: 'user' | 'operator') {
  const {
    email,
    password,
    name,
    surname = '',
    username = '',
    phone = '',
    address = '',
    isVerified = false
  } = data;

  // Validate required fields
  if (!email || !password || !name) {
    throw new Error('Email, password, and name are required');
  }

  // Validate password complexity
  if (password.length < 8) {
    throw new Error('Password must be at least 8 characters long');
  }
  
  // Check for password complexity
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
    throw new Error('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');
  }
  // Check if user already exists
  const existingUser = await Account.findOne({ email });
  if (existingUser) {
    throw new Error('User with this email already exists');
  }

  // Check username uniqueness if provided
  if (username) {
    const existingUsername = await Account.findOne({ 'user.username': username });
    if (existingUsername) {
      throw new Error('Username is already taken');
    }
  }
  // Hash password
  const hashedPassword = await bcrypt.hash(password, 10);

  // Create account based on type
  const accountData: AccountData = {
    email,
    password: hashedPassword,
    user: {
      name,
      surname,
      username,
      type: userType
    },
    contacts: {
      phone,
      address
    },
    booleans: {
      isVerified,
      isAdmin: false,
      isOperator: userType === 'operator'
    }
  };

  // Add currentRole for operators
  if (userType === 'operator') {
    accountData.currentRole = 'operator';
  }

  const newAccount = new Account(accountData);
  await newAccount.save();

  // Remove sensitive data from response
  const accountResponse: any = newAccount.toObject();
  accountResponse.password = undefined;
  accountResponse.tokens = undefined;

  return accountResponse;
}

/**
 * Get system statistics
 */
export const getSystemStats = async (req: Request, res: Response) => {
  try {
    const [totalUsers, totalOperators, totalJobs, totalChats, totalBids] = await Promise.all([
      Account.countDocuments({ 'user.type': 'user' }),
      Account.countDocuments({ 'user.type': 'operator' }),
      Job.countDocuments(),
      Chat.countDocuments(),
      Bid.countDocuments()
    ]);

    const [activeJobs, completedJobs] = await Promise.all([
      Job.countDocuments({ status: { $in: ['pending', 'accepted', 'in_progress'] } }),
      Job.countDocuments({ status: 'completed' })
    ]);

    res.json({
      success: true,
      stats: {
        users: totalUsers,
        operators: totalOperators,
        jobs: {
          total: totalJobs,
          active: activeJobs,
          completed: completedJobs
        },
        chats: totalChats,
        bids: totalBids
      }
    });
  } catch (error) {
    console.error('Error getting system stats:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

/**
 * Get all users with pagination
 */
export const getAllUsers = async (req: Request, res: Response) => {
  try {
    const { page = 1, limit = 20, search = '' } = req.query;
    const pageNum = Number(page);
    const limitNum = Number(limit);

    // Validate and cap pagination parameters
    if (pageNum < 1 || isNaN(pageNum)) {
      return res.status(400).json({ success: false, error: 'Invalid page number' });
    }
    if (limitNum < 1 || limitNum > 100 || isNaN(limitNum)) {
      return res.status(400).json({ success: false, error: 'Limit must be between 1 and 100' });
    }
    let query: any = { 'user.type': 'user' };
    
    if (search) {
      const escapedSearch = escapeRegex(search as string);
      query = {
        ...query,
        $or: [
          { email: { $regex: escapedSearch, $options: 'i' } },
          { 'user.name': { $regex: escapedSearch, $options: 'i' } },
          { 'user.surname': { $regex: escapedSearch, $options: 'i' } },
          { 'user.username': { $regex: escapedSearch, $options: 'i' } }
        ]
      };
    }

    const users = await Account.find(query)
      .select('-password -tokens')
      .sort({ createdAt: -1 })
      .limit(limitNum)
      .skip((pageNum - 1) * limitNum);

    const total = await Account.countDocuments(query);

    res.json({
      success: true,
      users,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    });
  } catch (error) {
    console.error('Error getting users:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

/**
 * Create new user
 */
export const createUser = async (req: Request, res: Response) => {
  try {
    const accountData: CreateAccountData = req.body;
    const newUser = await createAccount(accountData, 'user');

    res.status(201).json({
      success: true,
      user: newUser
    });
  } catch (error) {
    console.error('Error creating user:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Email, password, and name are required' ||
          error.message === 'Password must be at least 8 characters long' ||
          error.message === 'Invalid email format') {
        return res.status(400).json({ success: false, error: error.message });
      }
      if (error.message === 'User with this email already exists' ||
          error.message === 'Username is already taken') {
        return res.status(409).json({ success: false, error: error.message });
      }
    }    
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

/**
 * Update user
 */
export const updateUser = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    const { userId } = req.params;
    const updateData = req.body;

    // Define allowed fields for update
    const allowedFields = ['email', 'password', 'user', 'contacts', 'booleans'];
    const sanitizedData: any = {};

    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        // For booleans field, validate specific properties
        if (field === 'booleans' && updateData[field]) {
          (sanitizedData as any)[field] = {
            isVerified: updateData[field].isVerified
          };
        } else {
          (sanitizedData as any)[field] = updateData[field];
        }
      }
    }

    // Prevent updating admin status unless the current user is admin (check raw payload to detect attempts)
    if (
      updateData?.booleans &&
      Object.prototype.hasOwnProperty.call(updateData.booleans, 'isAdmin') &&
      user?.role !== 'admin'
    ) {
      return res.status(403).json({ success: false, error: 'Cannot modify admin status' });
    }

    // Build update object with dot notation for nested fields
    const updateObj: any = {};
    if (sanitizedData.email) updateObj.email = sanitizedData.email;
    if (sanitizedData.password) {
      updateObj.password = await bcrypt.hash(sanitizedData.password, 10);
    }    
    if (sanitizedData.user) {
      Object.keys(sanitizedData.user).forEach(key => {
        updateObj[`user.${key}`] = sanitizedData.user[key];
      });
    }
    if (sanitizedData.contacts) {
      Object.keys(sanitizedData.contacts).forEach(key => {
        updateObj[`contacts.${key}`] = sanitizedData.contacts[key];
      });
    }
    if (sanitizedData.booleans) {
      updateObj['booleans.isVerified'] = sanitizedData.booleans.isVerified;
    }

    const updatedUser = await Account.findByIdAndUpdate(
      userId,
      updateObj,
      { new: true, runValidators: true }
    ).select('-password -tokens');
    if (!updatedUser) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }

    res.json({
      success: true,
      user: updatedUser
    });
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

/**
 * Delete user (hard delete)
 */
export const deleteUser = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (user?.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Only admins can delete users'
      });
    }

    const { userId } = req.params;

    // Fetch the user first
    const targetUser = await Account.findById(userId);
    if (!targetUser) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }

    if (targetUser.booleans.isAdmin) {
      return res.status(403).json({ 
        success: false, 
        error: 'Cannot delete admin users' 
      });
    }

    // Hard delete the user account
    await Account.findByIdAndDelete(userId);

    res.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

/**
 * Get all operators
 */
export const getAllOperators = async (req: Request, res: Response) => {
  try {
    const { page = 1, limit = 20, search = '' } = req.query;
    const pageNum = Number(page);
    const limitNum = Number(limit);
    // Validate and cap pagination parameters
    if (pageNum < 1 || isNaN(pageNum)) {
      return res.status(400).json({ success: false, error: 'Invalid page number' });
    }
    if (limitNum < 1 || limitNum > 100 || isNaN(limitNum)) {
      return res.status(400).json({ success: false, error: 'Limit must be between 1 and 100' });
    }

    const baseOperator: any = {
      $or: [
        { 'user.type': 'operator' },
        { 'booleans.isOperator': true }
      ]
    };

    let query: any = baseOperator;
    if (search) {
      const escapedSearch = escapeRegex(search as string);
      query = {
        $and: [
          baseOperator,
          {
            $or: [
              { email: { $regex: escapedSearch, $options: 'i' } },
              { 'user.name': { $regex: escapedSearch, $options: 'i' } },
              { 'user.surname': { $regex: escapedSearch, $options: 'i' } },
              { 'user.username': { $regex: escapedSearch, $options: 'i' } }
            ]
          }
        ]
      };
    }

    const operators = await Account.find(query)
      .select('-password -tokens')
      .sort({ createdAt: -1 })
      .limit(limitNum)
      .skip((pageNum - 1) * limitNum);

    const total = await Account.countDocuments(query);

    res.json({
      success: true,
      operators,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    });
  } catch (error) {
    console.error('Error getting operators:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

/**
 * Create new operator
 */
export const createOperator = async (req: Request, res: Response) => {
  try {
    const accountData: CreateAccountData = req.body;
    const newOperator = await createAccount(accountData, 'operator');

    res.status(201).json({
      success: true,
      operator: newOperator
    });
  } catch (error) {
    console.error('Error creating operator:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Email, password, and name are required' ||
          error.message === 'Password must be at least 8 characters long' ||
          error.message === 'Invalid email format') {
        return res.status(400).json({ success: false, error: error.message });
      }
      if (error.message === 'User with this email already exists' ||
          error.message === 'Username is already taken') {
        return res.status(409).json({ success: false, error: error.message });
      }
    }
    
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

/**
 * Update operator
 */
export const updateOperator = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (user?.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Only admins can update operators'
      });
    }

    const { operatorId } = req.params;
    const updateData = req.body;

    // Define allowed fields for update
    const allowedFields = ['email', 'user', 'contacts', 'booleans'];
    interface UpdateData {
      email?: string;
      password?: string;
      user?: any;
      contacts?: any;
      booleans?: any;
    }
    const sanitizedData: UpdateData = {};

    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        // Restrict booleans updates similar to updateUser
        if (field === 'booleans' && updateData[field]) {
          (sanitizedData as any)[field] = {
            isVerified: updateData[field].isVerified
          };
        } else {
          (sanitizedData as any)[field] = updateData[field];
        }
      }
    }

    // Hash password separately if provided
    if (updateData.password) {
      sanitizedData.password = await bcrypt.hash(updateData.password, 10);
    }

    // Build update object with dot notation for nested fields to avoid overwriting entire subdocuments
    const updateObj: any = {};
    const flatten = (obj: any, basePath = ''): Record<string, any> => {
      const out: Record<string, any> = {};
      Object.entries(obj || {}).forEach(([k, v]) => {
        if (v === undefined) return;
        const path = basePath ? `${basePath}.${k}` : k;
        if (v && typeof v === 'object' && !Array.isArray(v)) {
          Object.assign(out, flatten(v, path));
        } else {
          out[path] = v;
        }
      });
      return out;
    };
    if (sanitizedData.email !== undefined) updateObj.email = sanitizedData.email;
    if (sanitizedData.password !== undefined) updateObj.password = sanitizedData.password;
    if (sanitizedData.user) Object.assign(updateObj, flatten(sanitizedData.user, 'user'));
    if (sanitizedData.contacts) Object.assign(updateObj, flatten(sanitizedData.contacts, 'contacts'));
    if (sanitizedData.booleans && 'isVerified' in sanitizedData.booleans) {
      updateObj['booleans.isVerified'] = sanitizedData.booleans.isVerified;
    }

    const updatedOperator = await Account.findByIdAndUpdate(
      operatorId,
      updateObj,
      { new: true, runValidators: true }
    ).select('-password -tokens');

    if (!updatedOperator) {
      return res.status(404).json({ success: false, error: 'Operator not found' });
    }

    res.json({
      success: true,
      operator: updatedOperator
    });

  } catch (error) {
    console.error('Error updating operator:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

/**
 * Delete operator (hard delete)
 */
export const deleteOperator = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (user?.role !== 'admin') {
      return res.status(403).json({ 
        success: false, 
        error: 'Only admins can delete operators' 
      });
    }

    const { operatorId } = req.params;
    // Fetch the operator first
    const operator = await Account.findById(operatorId);
    if (!operator) {
      return res.status(404).json({ success: false, error: 'Operator not found' });
    }

    // Hard delete the operator account
    await Account.findByIdAndDelete(operatorId);

    res.json({
      success: true,
      message: 'Operator deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting operator:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

