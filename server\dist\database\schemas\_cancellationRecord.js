"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const cancellationRecordSchema = new mongoose_1.default.Schema({
    jobId: {
        type: String,
        required: true,
        ref: 'Job',
        index: true
    },
    bidId: {
        type: String,
        ref: 'Bid',
        index: true
    },
    initiatedBy: {
        type: String,
        required: true,
        ref: 'Account',
        index: true
    },
    initiatorRole: {
        type: String,
        required: true,
        enum: ['client', 'operator', 'admin', 'system'],
        index: true
    },
    type: {
        type: String,
        required: true,
        enum: [
            'operator_before_start',
            'client_less_than_24h',
            'client_after_start',
            'operator_after_arrival',
            'system_no_show'
        ],
        index: true
    },
    status: {
        type: String,
        required: true,
        enum: ['pending', 'processing', 'completed', 'failed', 'disputed'],
        default: 'pending',
        index: true
    },
    scheduledStartTime: {
        type: Date,
        required: true,
        index: true
    },
    cancellationTime: {
        type: Date,
        required: true,
        default: Date.now,
        index: true
    },
    hoursBeforeStart: {
        type: Number,
        required: true
    },
    originalAmount: {
        type: Number,
        required: true,
        min: 0
    },
    penaltyAmount: {
        type: Number,
        required: true,
        default: 0,
        min: 0
    },
    refundAmount: {
        type: Number,
        required: true,
        default: 0,
        min: 0
    },
    adminShare: {
        type: Number,
        required: true,
        default: 0,
        min: 0
    },
    operatorShare: {
        type: Number,
        required: true,
        default: 0,
        min: 0
    },
    currency: {
        type: String,
        required: true,
        enum: ['DOP', 'USD'],
        default: 'DOP'
    },
    reason: {
        type: String,
        required: true,
        maxlength: 500
    },
    explanation: {
        type: String,
        maxlength: 2000
    },
    evidenceUrls: [{
            type: String // S3 URLs
        }],
    paymentTransactionIds: [{
            type: String,
            ref: 'PaymentTransaction'
        }],
    creditTransactionIds: [{
            type: String,
            ref: 'CreditTransaction'
        }],
    operatorPenaltyId: {
        type: String,
        ref: 'OperatorPenalty'
    },
    processedAt: {
        type: Date,
        index: true
    },
    processedBy: {
        type: String,
        ref: 'Account'
    },
    processingNotes: {
        type: String,
        maxlength: 1000
    },
    disputeReason: {
        type: String,
        maxlength: 1000
    },
    disputedAt: {
        type: Date
    },
    disputedBy: {
        type: String,
        ref: 'Account'
    },
    disputeResolution: {
        type: String,
        maxlength: 1000
    },
    disputeResolvedAt: {
        type: Date
    },
    metadata: {
        clientNotified: { type: Boolean, default: false },
        operatorNotified: { type: Boolean, default: false },
        ratingImpactApplied: { type: Boolean, default: false },
        jobReposted: { type: Boolean, default: false },
        rescheduleOffered: { type: Boolean, default: false },
        originalJobData: { type: mongoose_1.default.Schema.Types.Mixed },
        systemFlags: [{ type: String }]
    }
}, {
    timestamps: true
});
// Compound indexes for efficient querying
cancellationRecordSchema.index({ jobId: 1, type: 1 });
cancellationRecordSchema.index({ initiatedBy: 1, createdAt: -1 });
cancellationRecordSchema.index({ type: 1, status: 1 });
cancellationRecordSchema.index({ scheduledStartTime: 1, cancellationTime: 1 });
exports.default = mongoose_1.default.model('CancellationRecord', cancellationRecordSchema);
