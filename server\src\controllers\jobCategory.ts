import { Request, Response } from 'express';
import { getAuthenticatedUser } from '../utils/auth';
import jobCategoryService from '../services/jobCategoryService';
import { DOCUMENT_REQUIREMENT_CATEGORIES, DOCUMENT_REQUIREMENT_LABELS, isDocumentRequirementCategory } from '../types/documentRequirements';
import { JobCategory } from '../database/schemas';

// Helper function to safely compare MongoDB IDs and strings
function isSameId(id1?: string | null | any, id2?: string | null): boolean {
  if (!id1 || !id2) return false;
  // Convert both to string and trim to ensure consistent comparison
  return String(id1).trim() === String(id2).trim();
}

/**
 * Get active job categories (public endpoint for users)
 */
export const getActiveCategories = async (req: Request, res: Response) => {
  try {
    const categories = await jobCategoryService.getActiveCategories();

    res.json({
      success: true,
      categories: categories.map((cat: any) => ({
        name: cat.name,
        displayName: cat.displayName,
        description: cat.description,
        order: cat.order,
        documents_required: cat.documents_required || ['none'],
      })),
    });
  } catch (error) {
    console.error('Error getting active categories:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

/**
 * Public: get master list of supported document requirement categories
 */
export const getDocumentRequirementCategories = async (_req: Request, res: Response) => {
  res.json({
    success: true,
    categories: DOCUMENT_REQUIREMENT_CATEGORIES,
    labels: DOCUMENT_REQUIREMENT_LABELS,
  });
};

/**
 * Get all job categories (admin only)
 */
export const getAllCategories = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (user?.role !== 'admin') {
      return res.status(403).json({ success: false, error: 'Admin access required' });
    }
    const categories = await jobCategoryService.getAllCategories();

    res.json({ success: true, categories });
  } catch (error) {
    console.error('Error getting all categories:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

/**
 * Create new job category (admin only)
 */
export const createCategory = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ success: false, error: 'Admin access required' });
    }

  const { name, displayName, description, order } = req.body;
  let documents_required = req.body?.documents_required as unknown;

    if (!name || !displayName) {
      return res.status(400).json({ success: false, error: 'Name and displayName are required' });
    }

    // Check if category already exists
    const existingCategory = await jobCategoryService.getCategoryByName(String(name));
    if (existingCategory) {
      return res.status(409).json({ success: false, error: 'Category with this name already exists' });
    }

    // Sanitize documents_required
    if (Array.isArray(documents_required)) {
      documents_required = (documents_required as unknown[])
        .map((v) => (typeof v === 'string' ? v.trim().toLowerCase() : v))
        .filter(isDocumentRequirementCategory);
    } else {
      documents_required = undefined;
    }

    const category = await jobCategoryService.createCategory({
      name,
      displayName,
      description,
      order,
      documents_required: documents_required as any,
    });

    res.status(201).json({ success: true, category });
  } catch (error: any) {
    if (error?.code === 11000) {
      return res.status(409).json({ success: false, error: 'Category with this name already exists' });
    }
    console.error('Error creating category:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

/**
 * Update job category (admin only)
 */
export const updateCategory = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ success: false, error: 'Admin access required' });
    }

    const { categoryId } = req.params;
  const allowedFields = ['name', 'displayName', 'description', 'order', 'isActive', 'documents_required'] as const;
    const raw = req.body as Record<string, unknown>;
    const updateData = Object.fromEntries(
      Object.entries(raw).filter(([k]) => (allowedFields as readonly string[]).includes(k))
    ) as Record<string, unknown>;
    if (typeof updateData.name === 'string') {
      updateData.name = updateData.name.trim().toLowerCase();
    }
    if (typeof updateData.displayName === 'string') {
      updateData.displayName = (updateData.displayName as string).trim();
    }
    if (updateData.order != null) {
      updateData.order = Number(updateData.order);
    }

    // documents_required validation and normalization
    if (Array.isArray((raw as any).documents_required)) {
      const incoming = (raw as any).documents_required as unknown[];
      const sanitized = incoming
        .map((v) => (typeof v === 'string' ? v.trim().toLowerCase() : v))
        .filter(isDocumentRequirementCategory);
      (updateData as any).documents_required = sanitized.length ? sanitized : ['none'];
    }

    // Don't allow updating the name if it would conflict with existing category
    if (typeof updateData.name === 'string' && updateData.name) {
      const existingCategory = await jobCategoryService.getCategoryByName(updateData.name as string);
      if (existingCategory && !isSameId((existingCategory as any)._id, categoryId)) {
        return res.status(409).json({ success: false, error: 'Category with this name already exists' });
      }
    }

    const category = await jobCategoryService.updateCategory(categoryId, updateData);

    if (!category) {
      return res.status(404).json({ success: false, error: 'Category not found' });
    }

    res.json({ success: true, category });
  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

/**
 * Delete job category (admin only)
 */
export const deleteCategory = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ success: false, error: 'Admin access required' });
    }

    const { categoryId } = req.params;
    const success = await jobCategoryService.deleteCategory(categoryId);

    if (!success) {
      return res.status(404).json({ success: false, error: 'Category not found' });
    }

    res.json({ success: true, message: 'Category deleted successfully' });
  } catch (error) {
    console.error('Error deleting category:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

// Admin: get document requirements for a specific job category
export const getCategoryDocumentRequirements = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ success: false, error: 'Admin access required' });
    }
    const { categoryId } = req.params;
    const category = await JobCategory.findById(categoryId);
    if (!category) return res.status(404).json({ success: false, error: 'Category not found' });
    res.json({ success: true, name: category.name, documents_required: category.documents_required || ['none'] });
  } catch (error) {
    console.error('Error getting category document requirements:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

// Admin: update document requirements for a specific job category
export const updateCategoryDocumentRequirements = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ success: false, error: 'Admin access required' });
    }
    const { categoryId } = req.params;
    let { documents_required } = req.body as { documents_required?: unknown };
    if (!Array.isArray(documents_required)) {
      return res.status(400).json({ success: false, error: 'documents_required must be an array' });
    }
    const sanitized = (documents_required as unknown[])
      .map((v) => (typeof v === 'string' ? v.trim().toLowerCase() : v))
      .filter(isDocumentRequirementCategory);
    const updated = await jobCategoryService.updateCategory(categoryId, { documents_required: sanitized as any } as any);
    if (!updated) return res.status(404).json({ success: false, error: 'Category not found' });
    res.json({ success: true, category: updated });
  } catch (error) {
    console.error('Error updating category document requirements:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};
