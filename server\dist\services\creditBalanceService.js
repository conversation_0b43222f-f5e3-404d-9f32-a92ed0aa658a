"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreditBalanceService = void 0;
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const schemas_1 = require("../database/schemas");
const paymentProcessorService_1 = require("./paymentProcessorService");
const logger_1 = require("../utils/logger");
class CreditBalanceService {
    constructor() {
        this.paymentProcessor = paymentProcessorService_1.PaymentProcessorService.getInstance();
    }
    static getInstance() {
        if (!CreditBalanceService.instance) {
            CreditBalanceService.instance = new CreditBalanceService();
        }
        return CreditBalanceService.instance;
    }
    /**
     * Get or create credit balance for a user
     */
    getOrCreateCreditBalance(options) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const { userId, currency = 'DOP' } = options;
            let creditBalance = yield schemas_1.CreditBalance.findOne({ userId });
            if (!creditBalance) {
                creditBalance = yield schemas_1.CreditBalance.create({
                    userId,
                    currency,
                    balance: 0,
                    totalCredits: 0,
                    totalWithdrawals: 0,
                    pendingWithdrawals: 0,
                    lastTransactionAt: null
                });
                logger_1.logger.info(`Created new credit balance for user ${userId}`);
            }
            return creditBalance;
        });
    }
    /**
     * Get credit balance information for a user
     */
    getCreditBalanceInfo(userId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const creditBalance = yield schemas_1.CreditBalance.findOne({ userId });
            if (!creditBalance) {
                return null;
            }
            const availableForWithdrawal = Math.max(0, creditBalance.balance - creditBalance.pendingWithdrawals);
            return {
                balance: creditBalance.balance,
                currency: creditBalance.currency,
                totalCredits: creditBalance.totalCredits,
                totalWithdrawals: creditBalance.totalWithdrawals,
                pendingWithdrawals: creditBalance.pendingWithdrawals,
                availableForWithdrawal,
                lastTransactionAt: creditBalance.lastTransactionAt
            };
        });
    }
    /**
     * Add credit to user's balance
     */
    addCredit(options) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            if (options.type !== 'credit' && options.type !== 'refund' && options.type !== 'penalty_compensation') {
                throw new Error('Invalid transaction type for adding credit');
            }
            if (options.amount <= 0) {
                throw new Error('Credit amount must be positive');
            }
            const session = yield mongoose_1.default.startSession();
            try {
                return yield session.withTransaction(() => tslib_1.__awaiter(this, void 0, void 0, function* () {
                    // Get or create credit balance
                    const creditBalance = yield this.getOrCreateCreditBalance({
                        userId: options.userId,
                        currency: options.currency
                    });
                    // Create credit transaction
                    const transaction = yield schemas_1.CreditTransaction.create([{
                            userId: options.userId,
                            creditBalanceId: String(creditBalance._id),
                            type: options.type,
                            amount: options.amount,
                            currency: options.currency || 'DOP',
                            status: 'completed',
                            description: options.description,
                            metadata: options.metadata || {},
                            processedAt: new Date()
                        }], { session });
                    // Update credit balance
                    yield schemas_1.CreditBalance.findByIdAndUpdate(creditBalance._id, {
                        $inc: {
                            balance: options.amount,
                            totalCredits: options.amount
                        },
                        $set: {
                            lastTransactionAt: new Date()
                        }
                    }, { session });
                    logger_1.logger.info(`Added ${options.amount} credit to user ${options.userId}`);
                    return transaction[0];
                }));
            }
            finally {
                yield session.endSession();
            }
        });
    }
    /**
     * Deduct from user's balance (for internal use)
     */
    deductBalance(options) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            if (options.type !== 'debit' && options.type !== 'withdrawal') {
                throw new Error('Invalid transaction type for deducting balance');
            }
            if (options.amount <= 0) {
                throw new Error('Deduction amount must be positive');
            }
            const session = yield mongoose_1.default.startSession();
            try {
                return yield session.withTransaction(() => tslib_1.__awaiter(this, void 0, void 0, function* () {
                    const creditBalance = yield schemas_1.CreditBalance.findOne({ userId: options.userId }).session(session);
                    if (!creditBalance) {
                        throw new Error('Credit balance not found');
                    }
                    // Check if sufficient balance
                    const availableBalance = creditBalance.balance - creditBalance.pendingWithdrawals;
                    if (availableBalance < options.amount) {
                        throw new Error('Insufficient credit balance');
                    }
                    // Create debit transaction
                    const transaction = yield schemas_1.CreditTransaction.create([{
                            userId: options.userId,
                            creditBalanceId: String(creditBalance._id),
                            type: options.type,
                            amount: -options.amount, // Negative for debit
                            currency: options.currency || creditBalance.currency,
                            status: 'completed',
                            description: options.description,
                            metadata: options.metadata || {},
                            processedAt: new Date()
                        }], { session });
                    // Update credit balance
                    const updateFields = {
                        $inc: {
                            balance: -options.amount
                        },
                        $set: {
                            lastTransactionAt: new Date()
                        }
                    };
                    if (options.type === 'withdrawal') {
                        updateFields.$inc.totalWithdrawals = options.amount;
                    }
                    yield schemas_1.CreditBalance.findByIdAndUpdate(creditBalance._id, updateFields, { session });
                    logger_1.logger.info(`Deducted ${options.amount} from user ${options.userId} balance`);
                    return transaction[0];
                }));
            }
            finally {
                yield session.endSession();
            }
        });
    }
    /**
     * Process withdrawal to original payment method
     */
    processWithdrawal(options) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            if (options.amount <= 0) {
                throw new Error('Withdrawal amount must be positive');
            }
            const session = yield mongoose_1.default.startSession();
            try {
                return yield session.withTransaction(() => tslib_1.__awaiter(this, void 0, void 0, function* () {
                    const creditBalance = yield schemas_1.CreditBalance.findOne({ userId: options.userId }).session(session);
                    if (!creditBalance) {
                        throw new Error('Credit balance not found');
                    }
                    // Check available balance
                    const availableBalance = creditBalance.balance - creditBalance.pendingWithdrawals;
                    if (availableBalance < options.amount) {
                        throw new Error('Insufficient available balance for withdrawal');
                    }
                    // Create pending withdrawal transaction
                    const transaction = yield schemas_1.CreditTransaction.create([{
                            userId: options.userId,
                            creditBalanceId: String(creditBalance._id),
                            type: 'withdrawal',
                            amount: -options.amount,
                            currency: options.currency || creditBalance.currency,
                            status: 'pending',
                            description: `Withdrawal to ${options.withdrawalMethod}`,
                            metadata: {
                                withdrawalMethod: options.withdrawalMethod,
                                originalPaymentMethodId: options.originalPaymentMethodId,
                                processingFee: 0 // TODO: Calculate processing fee based on method
                            }
                        }], { session });
                    // Update pending withdrawals
                    yield schemas_1.CreditBalance.findByIdAndUpdate(creditBalance._id, {
                        $inc: {
                            pendingWithdrawals: options.amount
                        },
                        $set: {
                            lastTransactionAt: new Date()
                        }
                    }, { session });
                    // Process withdrawal through payment processor
                    try {
                        // TODO: Implement actual withdrawal processing based on method
                        if (options.withdrawalMethod === 'credit_card' && options.originalPaymentMethodId) {
                            // Process refund to original payment method
                            // This would integrate with the existing payment processor
                            logger_1.logger.info(`Processing withdrawal to credit card for user ${options.userId}`);
                        }
                        else if (options.withdrawalMethod === 'bank_transfer') {
                            // Process bank transfer
                            logger_1.logger.info(`Processing bank transfer withdrawal for user ${options.userId}`);
                        }
                        // For now, mark as completed (in real implementation, this would be async)
                        yield schemas_1.CreditTransaction.findByIdAndUpdate(transaction[0]._id, {
                            status: 'completed',
                            processedAt: new Date()
                        }, { session });
                        // Update balances
                        yield schemas_1.CreditBalance.findByIdAndUpdate(creditBalance._id, {
                            $inc: {
                                balance: -options.amount,
                                totalWithdrawals: options.amount,
                                pendingWithdrawals: -options.amount
                            }
                        }, { session });
                        logger_1.logger.info(`Completed withdrawal of ${options.amount} for user ${options.userId}`);
                        return transaction[0];
                    }
                    catch (processingError) {
                        // Mark transaction as failed and restore pending balance
                        yield schemas_1.CreditTransaction.findByIdAndUpdate(transaction[0]._id, {
                            status: 'failed',
                            processedAt: new Date()
                        }, { session });
                        yield schemas_1.CreditBalance.findByIdAndUpdate(creditBalance._id, {
                            $inc: {
                                pendingWithdrawals: -options.amount
                            }
                        }, { session });
                        throw new Error(`Withdrawal processing failed: ${processingError instanceof Error ? processingError.message : String(processingError)}`);
                    }
                }));
            }
            finally {
                yield session.endSession();
            }
        });
    }
    /**
     * Get transaction history for a user
     */
    getTransactionHistory(userId_1) {
        return tslib_1.__awaiter(this, arguments, void 0, function* (userId, limit = 50, offset = 0) {
            return yield schemas_1.CreditTransaction.find({ userId })
                .sort({ createdAt: -1 })
                .limit(limit)
                .skip(offset)
                .lean();
        });
    }
    /**
     * Get pending withdrawals for a user
     */
    getPendingWithdrawals(userId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            return yield schemas_1.CreditTransaction.find({
                userId,
                type: 'withdrawal',
                status: 'pending'
            })
                .sort({ createdAt: -1 })
                .lean();
        });
    }
}
exports.CreditBalanceService = CreditBalanceService;
