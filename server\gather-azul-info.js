const fs = require('fs');
const path = require('path');
const axios = require('axios');

async function gatherInfoForAzul() {
  console.log('='.repeat(60));
  console.log('📋 INFORMATION TO SEND TO AZUL SUPPORT');
  console.log('='.repeat(60));
  console.log('');
  
  // Get current IP
  try {
    const response = await axios.get('https://ifconfig.me/ip', { timeout: 5000 });
    console.log('🌐 Your current IP address:', response.data.trim());
  } catch (error) {
    console.log('❌ Could not determine IP address');
  }
  
  console.log('');
  
  // Certificate info
  const certsPath = path.resolve(__dirname, 'certs/azul');
  const clientCertPath = path.join(certsPath, 'local.pem');
  
  if (fs.existsSync(clientCertPath)) {
    const certContent = fs.readFileSync(clientCertPath, 'utf8');
    console.log('📜 CLIENT CERTIFICATE TO PROVIDE TO AZUL:');
    console.log('='.repeat(50));
    console.log(certContent);
    console.log('='.repeat(50));
    console.log('');
  }
  
  console.log('📧 EMAIL TEMPLATE FOR AZUL SUPPORT:');
  console.log('='.repeat(50));
  console.log(`Subject: mTLS Certificate Registration - Merchant ID 39038540035

Dear Azul Support Team,

I am setting up mTLS authentication for the Manito payment integration using Merchant ID 39038540035.

I am experiencing Incapsula blocking (HTTP 403) when making requests to:
https://pruebas.azul.com.do/WebServices/JSON/Default.aspx

Request Details:
- Merchant ID: 39038540035
- Environment: Sandbox (pruebas.azul.com.do)
- Current IP: [SEE ABOVE]
- mTLS Client Certificate: [SEE ABOVE]

Recent Incapsula Incident IDs:
- 1775000460178769149-24878718437493673

Could you please:
1. Register/whitelist the provided client certificate for mTLS authentication
2. Whitelist the provided IP address if required
3. Confirm the certificate is properly activated for the test environment

Thank you for your assistance.

Best regards,
Manito Development Team`);
  console.log('='.repeat(50));
  console.log('');
  
  console.log('🔧 TEMPORARY WORKAROUND:');
  console.log('While waiting for Azul support, you can use the AzulMockService');
  console.log('Set AZUL_MOCK_MODE=true in your environment variables');
  console.log('');
}

gatherInfoForAzul();
