import { Image, StyleSheet, Text, View } from 'react-native'
import React, { useState, useEffect, useRef } from 'react'
import { Animated } from 'react-native'
import { ChatMessage } from '@/types/chat'
import { Ingredient } from '@/types/ingredient'
import ButtonGlobal from '@/components/atoms/buttons/ButtonGlobal'
import InputDate from '@/components/atoms/inputs/InputDate'
import { useChat } from '@/context/ChatContext'
import InputTime from '@/components/atoms/inputs/InputTime'
import CheckLoader from '@/components/atoms/loaders/Check'

interface ChatAgentBubbles {

    isLastMessage: boolean,
    message: ChatMessage,
    userIngredients: Ingredient[]
}

export const ChatAgentBubbles = ({ message, userIngredients, isLastMessage }: ChatAgentBubbles) => {
    const { handleSendMessage } = useChat();

    const [dateSelected, setDateSelected] = useState(false)
    const [timeSelected, setTimeSelected] = useState(false)



    const fadeAnim = useRef(new Animated.Value(0)).current
    const translateYAnim = useRef(new Animated.Value(10)).current // start 10px lower

    useEffect(() => {
        if (isLastMessage) {
            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 1,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.timing(translateYAnim, {
                    toValue: 0,
                    duration: 500,
                    useNativeDriver: true,
                }),
            ]).start()
        } else {
            // Immediately set to visible for other messages
            fadeAnim.setValue(1)
            translateYAnim.setValue(0)
        }
    }, [isLastMessage])



    const renderMessage = (message: ChatMessage) => {
        return (
            <View key={message.id} style={styles.messageContainer}>
                {
                    message.action == 'done' &&
                    <View style={{ width: '100%', marginTop: 10, marginBottom: 20 }}>
                        <CheckLoader />
                    </View>
                }
                <Animated.View
                    style={[
                        styles.messageBubble,
                        message.type === "bot" ? styles.botBubble : styles.userBubble,
                        {
                            padding: 12,
                            opacity: fadeAnim,
                            transform: [{ translateY: translateYAnim }]
                        }
                    ]}

                >
                    <Text style={message.type === "bot" ? styles.botText : styles.userText}>{message.content}</Text>
                </Animated.View>


                {
                    message.action == 'date' &&
                    <View style={{ width: '90%' }}>
                        <InputDate
                            value={''}
                            onChangeText={(date) => {
                                handleSendMessage(date)
                                setDateSelected(true)
                            }}
                            disabled={dateSelected}
                        />
                    </View>
                }

                {
                    message.action == 'time' &&
                    <View style={{ width: '90%' }}>
                        <InputTime
                            value=''
                            onChangeText={(time) => {
                                handleSendMessage(time)
                                setTimeSelected(true)
                            }}
                            disabled={timeSelected}
                        />
                    </View>
                }


                {
                    message.action != 'done' &&
                    <Text style={[styles.timestamp, { alignSelf: message.type == 'bot' ? "flex-start" : "flex-end" }]}>
                        {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                    </Text>
                }


                {
                    message.action == 'done' &&
                    <ButtonGlobal
                        text='Nueva Conversacion'
                        onPress={() => { }}
                        style={{
                            marginTop: 10,
                            marginBottom: 30,
                            backgroundColor: 'black'
                        }}
                    />
                }


            </View>
        )
    }
    const renderImage = (image: string) => {
        return (
            <View key={message.id} style={styles.messageContainer}>
                <Animated.View
                    style={[
                        styles.messageBubble,
                        styles.userBubble,
                        {
                            padding: 0,
                            opacity: fadeAnim,
                            transform: [{ translateY: translateYAnim }]
                        }
                    ]}

                >
                    <Image
                        source={{ uri: image }}
                        style={{
                            width: 200,
                            height: 200,
                            borderRadius: 8,
                            backgroundColor: 'black'
                        }}
                    />

                </Animated.View>
                <Text style={[styles.timestamp, { alignSelf: message.type == 'bot' ? "flex-start" : "flex-end" }]}>
                    {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                </Text>
            </View>

        )
    }

    if (message.image) return renderImage(message.image)
    return renderMessage(message)
}


const styles = StyleSheet.create({
    messageContainer: {
        marginBottom: 5,
    },
    messageBubble: {
        maxWidth: "90%",
        borderRadius: 16,
        marginBottom: 4,
    },
    botBubble: {
        backgroundColor: "#7d817d20",
        alignSelf: "flex-start",
        borderBottomLeftRadius: 4,
    },
    userBubble: {
        backgroundColor: "#337836",
        alignSelf: "flex-end",
        borderBottomRightRadius: 4,
    },
    botText: {
        fontSize: 16,
        color: "#333",
        lineHeight: 22,
    },
    userText: {
        fontSize: 16,
        color: "#fff",
        lineHeight: 22,
    },
    timestamp: {
        fontSize: 12,
        color: "#999",

    },
    ingredientsGrid: {
        flexDirection: "row",
        flexWrap: "wrap",
        gap: 12,
        marginTop: 12,
    },
    ingredientCard: {
        backgroundColor: "#f8f9fa",
        borderRadius: 12,
        padding: 12,
        width: "47%",
        alignItems: "center",
        borderWidth: 2,
        borderColor: "transparent",
        position: "relative",
    },
    selectedIngredientCard: {
        borderColor: "#337836",
        backgroundColor: "#f0f9f0",
    },
    ingredientEmoji: {
        fontSize: 24,
        marginBottom: 4,
    },
    ingredientName: {
        fontSize: 14,
        fontWeight: "600",
        color: "#333",
        marginBottom: 8,
        textAlign: "center",
    },
    freshnessContainer: {
        width: "100%",
        height: 6,
        backgroundColor: "#e9ecef",
        borderRadius: 3,
        marginBottom: 4,
        position: "relative",
    },
    freshnessBar: {
        height: "100%",
        borderRadius: 3,
    },
    freshnessText: {
        position: "absolute",
        right: 0,
        top: -20,
        fontSize: 10,
        fontWeight: "600",
    },
    freshnessLabel: {
        fontSize: 10,
        fontWeight: "500",
        textAlign: "center",
    },
    selectedIndicator: {
        position: "absolute",
        top: 8,
        right: 8,
    },
    findRecipesButton: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#337836",
        paddingVertical: 12,
        paddingHorizontal: 16,
        borderRadius: 8,
        marginTop: 16,
        gap: 6,
    },
    findRecipesText: {
        color: "#fff",
        fontSize: 14,
        fontWeight: "600",
    },
    suggestionGrid: {
        flexDirection: "row",
        flexWrap: "wrap",
        gap: 8,
        marginTop: 12,
    },
    suggestionCard: {
        backgroundColor: "#f8f9fa",
        borderRadius: 8,
        padding: 8,
        alignItems: "center",
        minWidth: 80,
    },
    suggestionEmoji: {
        fontSize: 20,
        marginBottom: 4,
    },
    suggestionName: {
        fontSize: 12,
        color: "#333",
        textAlign: "center",
    },
})