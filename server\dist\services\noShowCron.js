"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.processOperatorNoShows = processOperatorNoShows;
const tslib_1 = require("tslib");
const schemas_1 = require("../database/schemas");
const paymentProcessorService_1 = require("./paymentProcessorService");
const notifications_1 = require("../controllers/notifications");
const parseJobDateTime_1 = require("../utils/parseJobDateTime");
const environment = process.env.NODE_ENV === 'production' ? 'production' : 'sandbox';
const paymentProcessorService = new paymentProcessorService_1.PaymentProcessorService(environment);
/**
 * Nightly cron to detect operator no-shows and issue refunds.
 * For simplicity, this function can be invoked on server start or by an external scheduler.
 */
function processOperatorNoShows() {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        var _a, e_1, _b, _c;
        let cursor = undefined;
        try {
            const now = new Date();
            // Query for jobs that are accepted and whose scheduledAt is <= now (indexed field) to avoid scanning all jobs.
            // Use a cursor to stream documents and avoid loading everything into memory.
            const query = { status: 'accepted', scheduledAt: { $lte: now } };
            cursor = schemas_1.Job.find(query).lean().cursor();
            const THRESHOLD_MS = 60 * 60 * 1000;
            try {
                for (var _d = true, cursor_1 = tslib_1.__asyncValues(cursor), cursor_1_1; cursor_1_1 = yield cursor_1.next(), _a = cursor_1_1.done, !_a; _d = true) {
                    _c = cursor_1_1.value;
                    _d = false;
                    const job = _c;
                    try {
                        // If scheduledAt is missing for some documents, fall back to parsing date/hour and skip if not due yet.
                        let scheduledTs = null;
                        if (job.scheduledAt) {
                            scheduledTs = new Date(job.scheduledAt).getTime();
                        }
                        else if (job.date && job.hour) {
                            const scheduled = (0, parseJobDateTime_1.parseJobDateTime)(job.date, job.hour, 'Europe/Paris');
                            const ts = scheduled.getTime();
                            if (!Number.isNaN(ts))
                                scheduledTs = ts;
                        }
                        if (!scheduledTs) {
                            console.warn('Skipping job with missing or invalid scheduled time', { jobId: String(job._id), date: job === null || job === void 0 ? void 0 : job.date, hour: job === null || job === void 0 ? void 0 : job.hour });
                            continue;
                        }
                        if (scheduledTs + THRESHOLD_MS > now.getTime())
                            continue;
                        // Do a conditional two-phase transition to avoid races:
                        // 1) Attempt to claim the job for refund processing by setting status -> 'pending_refund' only if status hasn't changed.
                        const pendingReason = 'operator_no_show';
                        const pendingAt = new Date();
                        const claimResult = yield schemas_1.Job.updateOne({ _id: job._id, status: job.status }, { $set: { status: 'pending_refund', pendingRefundAt: pendingAt, pendingRefundReason: pendingReason } });
                        // If nobody matched (status changed concurrently), skip this job.
                        if (!claimResult.matchedCount) {
                            console.info('Skipping job because status changed before claiming for refund', { jobId: String(job._id) });
                            continue;
                        }
                        // Prefer refunding a captured/direct Sale if present; otherwise void the latest Hold/authorization.
                        // Note: schema uses type 'Sale' for direct charges and 'Hold' for authorizations/escrow.
                        const capture = yield schemas_1.PaymentTransaction
                            .findOne({ jobId: String(job._id), type: 'Sale', status: 'successful' })
                            .sort({ createdAt: -1 })
                            .lean();
                        if (capture) {
                            // Refund the captured sale/charge
                            yield paymentProcessorService.refundTransaction(String(job.ownerId), String(capture._id), capture.amount, 'Operator no-show');
                        }
                        else {
                            // Fall back to voiding the latest hold/authorization
                            const hold = yield schemas_1.PaymentTransaction
                                .findOne({ jobId: String(job._id), type: 'Hold', status: { $in: ['escrow', 'initiated', 'successful'] } })
                                .sort({ createdAt: -1 })
                                .lean();
                            if (!hold) {
                                console.warn('No hold transaction found for job, reverting status', { jobId: String(job._id) });
                                yield schemas_1.Job.updateOne({ _id: job._id, status: 'pending_refund' }, {
                                    $set: {
                                        status: job.status,
                                        pendingRefundAt: null,
                                        pendingRefundReason: null
                                    }
                                });
                                continue;
                            }
                            yield paymentProcessorService.voidTransaction(String(job.ownerId), String(hold._id));
                        }
                        // Mark job as cancelled with reason/timestamp
                        const cancelledAt = new Date();
                        yield schemas_1.Job.updateOne({ _id: job._id, status: 'pending_refund' }, { $set: { status: 'cancelled', cancelledAt, cancelReason: 'operator_no_show' } });
                        // Notify customer: refund processed due to no-show
                        const customerToken = yield (0, notifications_1.getUserNotificationToken)(String(job.ownerId));
                        if (customerToken.success && customerToken.notificationToken) {
                            yield (0, notifications_1.sendCustomNotification)(customerToken.notificationToken, {
                                title: 'Operator No-show: Refund Issued',
                                message: 'The operator did not show up. A full refund has been issued.',
                                data: { type: 'refund_processed', jobId: String(job._id) },
                                soundOn: true,
                                badgeCount: 1,
                            });
                        }
                        // Notify operator about no-show incident (if assignedOperatorId exists on Job model)
                        if (job.assignedOperatorId) {
                            const operatorToken = yield (0, notifications_1.getUserNotificationToken)(String(job.assignedOperatorId));
                            if (operatorToken.success && operatorToken.notificationToken) {
                                yield (0, notifications_1.sendCustomNotification)(operatorToken.notificationToken, {
                                    title: 'No-show Recorded',
                                    message: 'A no-show was recorded for a scheduled job. No payout will be issued.',
                                    data: { type: 'operator_no_show', jobId: String(job._id) },
                                    soundOn: true,
                                    badgeCount: 1,
                                });
                            }
                        }
                    }
                    catch (err) {
                        console.error('No-show processing failed for job', job._id, err);
                    }
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (!_d && !_a && (_b = cursor_1.return)) yield _b.call(cursor_1);
                }
                finally { if (e_1) throw e_1.error; }
            }
        }
        catch (err) {
            console.error('processOperatorNoShows failed', err);
        }
        finally {
            if (cursor) {
                try {
                    yield cursor.close();
                }
                catch (closeErr) {
                    console.warn('Failed to close job cursor', closeErr);
                }
            }
        }
    });
}
