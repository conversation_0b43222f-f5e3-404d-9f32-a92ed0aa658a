import express from 'express';
import multer from 'multer';
import { AuthenticateTokenOAuth } from '../middleware/authentication';
import { tryCatch } from '../middleware';
import {
  startChat,
  sendMessage,
  uploadImage,
  getChat,
  getUserChats
} from '../controllers/chat';

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow only image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Start a new chat
router.post('/start', AuthenticateTokenOAuth, tryCatch(startChat));

// Send a message in a chat
router.post('/message', AuthenticateTokenOAuth, tryCatch(sendMessage));

// The error handling middleware is now directly integrated in the upload route

// Upload an image to a chat
router.post('/upload', AuthenticateTokenOAuth, (req, res, next) => {
  upload.single('image')(req, res, (err) => {
    if (err) {
      if (err instanceof multer.MulterError) {
        // A multer error occurred when uploading
        console.error('Multer error:', err);
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(413).json({ success: false, error: 'File too large (max 10MB)' });
        }
        if (err.code === 'LIMIT_UNEXPECTED_FILE') {
          return res.status(400).json({ 
            success: false, 
            error: `Unexpected field name. Use 'image' as the form field name`,
            details: err.message 
          });
        }
        return res.status(400).json({ success: false, error: err.message });
      } else {
        // An unknown error occurred when uploading
        console.error('Upload error:', err);
        if (err.message === 'Only image files are allowed') {
          return res.status(400).json({ success: false, error: 'Only image files are allowed' });
        }
        return res.status(500).json({ success: false, error: err.message });
      }
    }
    next();
  });
}, tryCatch(uploadImage));

// Get a specific chat
router.get('/:chatId', AuthenticateTokenOAuth, tryCatch(getChat));

// Get user's chats
router.get('/', AuthenticateTokenOAuth, tryCatch(getUserChats));

export default router;
