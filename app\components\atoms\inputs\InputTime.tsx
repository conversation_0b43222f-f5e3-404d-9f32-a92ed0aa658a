import { View, Text, StyleSheet, Platform, Pressable } from 'react-native';
import React, { useEffect, useState } from 'react';
import RNDateTimePicker from '@react-native-community/datetimepicker';
import ButtonGlobal from '../buttons/ButtonGlobal';

interface InputProps {
  value: string;
  onChangeText: (val: string) => void;
  placeholder?: string;
  label?: string;
  icon?: any;
  height?: number;
  style?: any;
  styleInput?: any;
  disabled?: boolean
}

const InputTime = ({
  label,
  placeholder,
  onChangeText,
  value,
  icon,
  height,
  style,
  styleInput,
  disabled
}: InputProps) => {
  const [showPicker, setShowPicker] = React.useState(false);
  const [tempDate, setTempDate] = React.useState<Date | undefined>(
    value ? new Date(value) : undefined
  );
  const [displayValue, setDisplayValue] = useState(value || placeholder || 'Seleccionar hora');

  const handleDateChange = (event: any, selectedDate?: Date) => {
    // For iOS, only proceed if the event type is 'set' (user confirmed the date)
    // For Android, the event is only triggered on confirmation, so no additional check is needed
    if (Platform.OS === 'ios' && event.type !== 'set') {
      setShowPicker(false);
      return;
    }

    //setShowPicker(false);

    if (selectedDate) {
      //onChangeText(selectedDate);
      setTempDate(selectedDate);
    }
  };
  const handleSaveDate = (date: string) => {

    onChangeText(date)
    setShowPicker(false)

  }

  useEffect(() => {
    if (!tempDate) return;
    setDisplayValue(
      new Date(tempDate).toLocaleTimeString('it-IT', { hour: '2-digit', minute: '2-digit' }) ||
      'Seleccionar hora'
    );
  }, [tempDate]);

  useEffect(() => {
    setTempDate(value ? new Date(value) : undefined);
  }, [value]);

  return (
    <View style={[styles.inputEmail, style]}>
      {label && <Text style={styles.inputLabel}>{label}</Text>}

      {
        !showPicker &&
        <Pressable
          onPress={() => !disabled && setShowPicker(true)}
          style={[
            styles.inputContainer,
            styleInput,
            { height: height ?? 50 },
            { borderRadius: showPicker ? 0 : 15 },
          ]}
        >
          <Text style={[styles.dateText, { paddingLeft: icon ? 10 : 0 }]}>
            {displayValue}
          </Text>
        </Pressable>
      }


      {showPicker && (
        <View
          style={{
            backgroundColor: '#c2c2c260',
            width: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            borderBottomLeftRadius: 15,
            borderBottomRightRadius: 15,

            borderTopRightRadius: showPicker ? 15 : 0,
            borderTopLeftRadius: showPicker ? 15 : 0
          }}
        >
          <RNDateTimePicker
            value={tempDate || new Date()}
            mode="time"
            display={Platform.OS === 'ios' ? 'inline' : 'default'}
            onChange={handleDateChange}
            textColor="#000000ff"
            themeVariant="light"
            locale='es'
          />

          <ButtonGlobal
            text='Seleccionar'
            textStyle={{ color: "black" }}
            style={{
              width: '100%'
            }}
            onPress={() => { displayValue && handleSaveDate(displayValue) }}
          />
        </View>
      )}
    </View>
  );
};

export default InputTime;

const styles = StyleSheet.create({
  inputLabel: {
    height: 25,
    color: '#000000ff',
    fontSize: 15,
    fontFamily: 'Poppins',
  },
  inputEmail: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  inputContainer: {
    height: 60,
    width: '100%',
    color: '#00000080',
    borderColor: '#ffffff80',
    paddingLeft: 15,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#c2c2c260',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
  },
  input: {
    height: '100%',
    width: '100%',
    padding: 10,
    fontSize: 15,
    color: '#ffffff',
    borderColor: '#000000',
    borderRadius: 30,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 0 },
    fontFamily: 'Poppins',
  },
  dateText: {
    color: '#000000ff',
    fontSize: 15,
    fontFamily: 'Poppins',
  },
});