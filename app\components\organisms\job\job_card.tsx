import React, { useEffect } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { useSharedValue, useAnimatedStyle, withTiming } from 'react-native-reanimated';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

interface InterfaceJobCard {
    job: {
        category: string;
        description: string;
        price: number;
        date: string;
        hour: string;
        status: string //'pending' | 'completed';
        images: string[];
        _id: string
    };
}

const { width } = Dimensions.get('window');

const JobCard = ({ job }: InterfaceJobCard) => {
    const router = useRouter()
    const { category, description, price, date, hour, status, images, _id } = job;

    // Reanimated animation
    const translateY = useSharedValue(50);
    const opacity = useSharedValue(0);

    const animatedStyle = useAnimatedStyle(() => ({
        transform: [{ translateY: translateY.value }],
        opacity: opacity.value,
    }));

    useEffect(() => {
        translateY.value = withTiming(0, { duration: 400 });
        opacity.value = withTiming(1, { duration: 400 });
    }, [translateY, opacity]);

    return (
        <Animated.View style={[styles.card, animatedStyle]}>
            <View style={styles.gradient}>

                {/* Content Section */}
                <View style={styles.content}>
                    {/* Category and Status */}
                    <View style={styles.header}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                            <View>
                                <Ionicons name='albums-outline' size={23} color={'#5a5a5aff'} />
                            </View>
                            <Text style={styles.category}>{category}</Text>

                        </View>
                        <Text
                            style={[
                                styles.status,
                                status === 'pending' ? styles.statusPending : styles.statusCompleted,
                            ]}
                        >
                            {'Bidding'}
                        </Text>
                    </View>

                    {/* Description */}
                    <View>
                        <Text style={{ fontFamily: "Montserrat", fontSize: 14, fontWeight: 600, color: 'white' }}>Detalles</Text>
                        <Text style={styles.description} numberOfLines={2} ellipsizeMode="tail">
                            {description}
                        </Text>
                    </View>



                    {/* Date and Time */}
                    <View>
                        <Text style={{ fontFamily: "Montserrat", fontSize: 14, fontWeight: 600, color: 'white' }}>Fecha</Text>
                        <View style={styles.dateContainer}>
                            <Text style={styles.date}>
                                {date} a las {hour}
                            </Text>
                        </View>
                    </View>



                    {/* Action Button */}
                    <TouchableOpacity
                        style={styles.button}
                        onPress={() => {
                            router.push({
                                pathname: '/jobs/job',
                                params: {
                                    jobId: job._id
                                }
                            })
                        }}
                    >
                        <View style={styles.buttonGradient}>
                            <Text style={styles.buttonText}>Ver Detalles</Text>
                        </View>
                    </TouchableOpacity>


                </View>
            </View>
        </Animated.View>
    );
};

const styles = StyleSheet.create({
    card: {
        width: width * 0.95,
        marginVertical: 12,
        borderRadius: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.4,
        shadowRadius: 8,
        elevation: 8,
        overflow: 'hidden',
        borderBottomRightRadius: 30,
    },
    gradient: {
        borderRadius: 10,
        overflow: 'hidden',
        backgroundColor: '#b4b4b4ca',
        borderBottomRightRadius: 30,
    },
    image: {
        width: '100%',
        height: '100%',
        backgroundColor: '#2c5b2c73'
    },
    content: {
        padding: 16,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    category: {
        fontSize: 14,
        fontWeight: '700',
        color: '#5a5a5aff',
        textTransform: 'uppercase',
        letterSpacing: 1,
    },
    status: {
        fontSize: 12,
        fontWeight: '600',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 12,
    },
    statusPending: {
        backgroundColor: '#fed9c7ff',
        color: '#d97706',
    },
    statusCompleted: {
        backgroundColor: '#d1fae5',
        color: '#059669',
    },
    description: {
        fontSize: 12,
        fontFamily: "Montserrat",
        fontWeight: '500',
        color: '#f3f4f6',
        marginBottom: 12,
    },

    dateContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    date: {
        fontSize: 12,
        color: '#d1d5db',
    },
    button: {
        borderRadius: 30,
        overflow: 'hidden',
    },
    buttonGradient: {
        paddingVertical: 12,
        paddingHorizontal: 24,
        alignItems: 'center',
        backgroundColor: 'white'
    },
    buttonText: {
        color: '#000',
        fontSize: 13,
        fontFamily: 'Montserrat',
        fontWeight: '600',
    },
});

export default JobCard;