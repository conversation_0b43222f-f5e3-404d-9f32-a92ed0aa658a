import { Animated, StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native'
import React, { useEffect, useRef } from 'react'
import Chevron from '../icons/Chevron';
import { triggerImpactLightHaptic } from '@/utils/Haptics';

interface ButtonMenuProps {
  text: string
  value: boolean;
  onClick: () => void


  icon?: React.ReactNode;
  iconStyle?: {
    backgroundColor?: string,
    borderColor?: string
  }
}

const ButtonSettingsToggle = ({ text, value, onClick, icon, iconStyle }: ButtonMenuProps) => {

  // Animated value for the dot position
  const animatedDotPosition = useRef(new Animated.Value(value ? 20 : 0)).current

  // Animated value for the background color
  const animatedBackgroundColor = useRef(new Animated.Value(value ? 1 : 0)).current

  // Update the dot position animation whenever the `value` changes
  useEffect(() => {
    Animated.timing(animatedDotPosition, {
      toValue: value ? 20 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start()

    // Update the background color animation whenever the `value` changes
    Animated.timing(animatedBackgroundColor, {
      toValue: value ? 1 : 0, // Animate between 0 (inactive) and 1 (active)
      duration: 300,
      useNativeDriver: false, // Color transitions require non-native driver
    }).start()
  }, [value])

  // Interpolate the background color based on the animated value
  const backgroundColor = animatedBackgroundColor.interpolate({
    inputRange: [0, 1],
    outputRange: ['#00000020', '#337836'], // Inactive color to active color
  })




  return (
    <View style={[styles.container]}>
      <View style={{ flexDirection: 'row', gap: 10, alignItems: 'center' }}>
        <View style={{ height: 30, width: 30, borderRadius: 10, backgroundColor: iconStyle?.backgroundColor ?? "#00000020", borderColor: iconStyle?.borderColor ?? "#00000015", borderWidth: 0.5, alignItems: 'center', justifyContent: 'center' }}>
          {
            icon
          }
        </View>
        <Text style={[styles.text]}>{text}</Text>
      </View>

      <TouchableWithoutFeedback onPress={() => {
        triggerImpactLightHaptic()
        onClick()
      }}>
        <Animated.View
          style={[
            styles.dotContainer,
            { backgroundColor }, // Apply animated background color
          ]}
        >
          <Animated.View
            style={[
              styles.dot,
              {
                transform: [{ translateX: animatedDotPosition }],
              },
            ]}
          />
        </Animated.View>
      </TouchableWithoutFeedback>
    </View>
  )
}

export default ButtonSettingsToggle

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 50,


    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'


  },
  text: {
    color: 'black',
    fontSize: 16,
    fontFamily: 'Montserrat'
  },
  dotContainer: {
    width: 45,
    height: 26,
    borderRadius: 30,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingHorizontal: 3,
  },
  dot: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'white',
  },
})