{"name": "app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"16": "xcrun simctl boot A196C22B-B8EE-4323-9360-6D25EAB2E0F7", "start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "devclient": "npx expo start --dev-client", "bootsim": "xcrun simctl boot 7D070117-0A69-4472-A14B-99C9386200E8", "16pro": "xcrun simctl boot 7D070117-0A69-4472-A14B-99C9386200E8", "bootsim2": "xcrun simctl boot A196C22B-B8EE-4323-9360-6D25EAB2E0F7", "buildAndroidLocal": "eas build --platform android --profile development --local"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.8", "@react-native-community/datetimepicker": "8.3.0", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/drawer": "^7.4.1", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "expo": "~53.0.9", "expo-apple-authentication": "~7.2.4", "expo-audio": "~0.4.7", "expo-auth-session": "~6.1.5", "expo-av": "~15.1.6", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.3", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-location": "~18.1.6", "expo-media-library": "~17.1.7", "expo-notifications": "~0.31.2", "expo-router": "~5.0.6", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "i18n-js": "^4.5.1", "lottie-react-native": "7.2.2", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "1.20.1", "react-native-purchases": "^8.10.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-toast-message": "^2.3.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}