// Central definition of document requirement categories used across the system, clients can render friendly labels from the map below. Keep this list small and stable.

export const DOCUMENT_REQUIREMENT_CATEGORIES = [
  'none',
  'criminal',
  'qualification',
  'identity', // e.g., national ID, passport, driver license, etc.
  'address', // e.g., proof of address / utility bill / bank statement
] as const;

export type DocumentRequirementCategory = typeof DOCUMENT_REQUIREMENT_CATEGORIES[number];

export const DOCUMENT_REQUIREMENT_LABELS: Record<DocumentRequirementCategory, string> = {
  none: 'Ninguno',
  criminal: 'Antecedentes penales',
  qualification: 'Certificaciones y calificaciones',
  identity: 'Documento de identidad',
  address: 'Comprobante de domicilio',
};

export function isDocumentRequirementCategory(value: unknown): value is DocumentRequirementCategory {
  return typeof value === 'string' && (DOCUMENT_REQUIREMENT_CATEGORIES as readonly string[]).includes(value);
}
