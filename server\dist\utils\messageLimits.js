"use strict";
/**
 * Message limit utilities for chat system
 * Handles 15 messages per hour limit per user
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkMessageLimit = checkMessageLimit;
exports.formatTimeUntilReset = formatTimeUntilReset;
/**
 * Check if user can send more messages based on 15 messages/hour limit
 */
function checkMessageLimit(messages) {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    // Count all messages (user + assistant + system) in the last hour
    const recentMessages = messages.filter((msg) => {
        const msgTime = new Date(msg.timestamp);
        return msgTime >= oneHourAgo;
    });
    const totalUsed = recentMessages.length;
    const limit = 15;
    const remaining = Math.max(0, limit - totalUsed);
    // Calculate time until oldest message expires (in minutes)
    let timeUntilReset = 0;
    if (recentMessages.length > 0) {
        const oldestMessage = recentMessages.reduce((oldest, msg) => {
            return new Date(msg.timestamp) < new Date(oldest.timestamp) ? msg : oldest;
        });
        const oldestTime = new Date(oldestMessage.timestamp);
        const resetTime = new Date(oldestTime.getTime() + 60 * 60 * 1000);
        timeUntilReset = Math.max(0, Math.ceil((resetTime.getTime() - now.getTime()) / (60 * 1000)));
    }
    return {
        allowed: remaining > 0,
        remainingMessages: remaining,
        timeUntilReset,
        totalUsed
    };
}
/**
 * Format time until reset in a user-friendly way
 */
function formatTimeUntilReset(minutes) {
    if (minutes <= 0)
        return "ahora mismo";
    if (minutes === 1)
        return "1 minuto";
    if (minutes < 60)
        return `${minutes} minutos`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (hours === 1 && remainingMinutes === 0)
        return "1 hora";
    if (hours === 1)
        return `1 hora y ${remainingMinutes} minutos`;
    if (remainingMinutes === 0)
        return `${hours} horas`;
    return `${hours} horas y ${remainingMinutes} minutos`;
}
