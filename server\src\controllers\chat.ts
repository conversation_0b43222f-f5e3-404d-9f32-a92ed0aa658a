import { Request, Response } from 'express';
import { Chat, Job, Account } from '../database/schemas';
import { IMessage } from '../database/schemas/_chat';
import { uploadFileToS3 } from '../utils/upload';
import { getAuthenticatedUser } from '../utils/auth';
import { parseJobDateTime } from '../utils/parseJobDateTime';
import OpenAI from 'openai';
import { v4 as uuidv4 } from 'uuid';
import { fileTypeFromBuffer } from 'file-type';
import path from 'path';

import cacheService from '../services/cache';
import jobCategoryService from '../services/jobCategoryService';
import { validateDateTime, extractDateTimeFromMessage, formatDateTime, parseDateTimeFromInput } from '../utils/dateValidation';
import { checkMessageLimit, formatTimeUntilReset } from '../utils/messageLimits';

// Helper function to safely compare MongoDB IDs and strings
function isSameId(id1: any, id2: any): boolean {
  return String(id1) === String(id2);
}

/**
 * Create a job from a completed chat
 */
async function createJobFromChat(chat: any, userId: string): Promise<any> {

  try {
    // Validate that all required fields are present
    if (!chat.category || !chat.description || !chat.date || !chat.hour) {
      throw new Error('Missing required fields for job creation');
    }

    // Validate category against active categories
    const isValidCategory = await jobCategoryService.isCategoryValid(chat.category);
    if (!isValidCategory) {
      console.warn(`Invalid category "${chat.category}" used in chat ${chat._id}`);
      // Don't throw error, just log warning - fallback to "other" if available
      const categories = await jobCategoryService.getActiveCategories();
      const otherCategory = categories.find(
        (cat) => (cat.name || '').toLowerCase() === 'other'
      );
      if (otherCategory) {
        chat.category = otherCategory.name;
      } else {
        throw new Error('Invalid category and no fallback available');
      }
    }

    // Images are optional - if no images, use an empty array
    const images = chat.images && chat.images.length > 0 ? chat.images : [];

    // Get user's timezone for scheduledAt calculation
    const user = await Account.findById(userId).select('settings.timezone').lean();
    const timezone = user?.settings?.timezone || 'Europe/Paris';

    // Calculate scheduledAt from date/hour in user's timezone
    let scheduledAt: Date | null = null;
    try {
      scheduledAt = parseJobDateTime(chat.date, chat.hour, timezone);
    } catch (error) {
      console.warn(`Failed to parse scheduledAt for job from chat ${chat._id}:`, error);
      // Continue job creation without scheduledAt - it can be computed later
    }

    // Set bidding deadline (e.g., 7 days from now)
    const biddingDeadline = new Date();
    biddingDeadline.setDate(biddingDeadline.getDate() + 7);

    // Create new job
    const newJob = new Job({
      ownerId: userId,
      chatId: (chat._id as any).toString(),
      category: chat.category, 
      description: chat.description,
      images: images,
      price: chat.price,
      date: chat.date,
      hour: chat.hour,
      scheduledAt,
      biddingDeadline,
      status: 'pending',
      createdBy: 'chat'
    });

    await newJob.save();

    console.log(`Job created successfully for chat ${chat._id}: Job ID ${newJob._id} (${images.length} images)`);

    // Trigger background notification to operators
    const { notifyOperatorsAboutNewJob } = await import('../services/jobNotificationService');
    const jobData = {
      jobId: (newJob._id as any).toString(),
      category: newJob.category,
      description: newJob.description,
      price: newJob.price,
      ownerId: newJob.ownerId
    };

    // Run notification in background (don't await to avoid blocking)
    setImmediate(() => {
      notifyOperatorsAboutNewJob(jobData).catch(error => {
        console.error('Background notification failed for job:', newJob._id, error);
      });
    });
    
    return newJob;
  } catch (error) {
    console.error('Error creating job from chat:', error);
    throw error;
  }
}

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Generate dynamic system prompt with current job categories
 */
async function generateSystemPrompt(): Promise<string> {
  let categories: string;
  
  try {
    categories = await jobCategoryService.getCategoryNamesForPrompt();
  } catch (error) {
    console.error('Error fetching job categories for prompt:', error);
    // Fallback to default categories if service fails
    categories = 'plumbing, electrical, carpentry, painting, cleaning, appliance repair, general maintenance, other';
  }
  
  return `
# Handyman Finder Assistant

## Role & Objective
You are a professional handyman service coordinator. Your goal is to efficiently collect complete service request information through natural conversation. CRITICAL: Complete each chat within 5 AI messages maximum to reduce costs.

## Required Information Collection (in order of priority)
**category**: Select from → ${categories}
**description**: Comprehensive problem summary 
**price**: Single price estimate (provide realistic market rate as number)
**date**: Appointment date (DD/MM/YYYY format, today or future)
**hour**: Appointment time (HH:MM format, 24-hour)

## Response Format
CRITICAL: Always respond with valid JSON only. No additional text before or after.

{
  "action": "string",
  "message": "string",
  "collected_data": {
    "category": "string|null",
    "description": "string|null",
    "price": "number|null",
    "date": "string|null",
    "hour": "string|null"
  }
}

## Action Types IMPORTANT
- **"none"**: Standard conversation, no special action
- **"image"**: User must upload/take a photo for assessment (max 3 images per chat)
- **"date"**: User needs to provide appointment date only using date picker
- **"time"**: User needs to provide appointment date only using time picker
- **"done"**: All information collected, ready to submit request

## Behavioral Guidelines

### Language & Cultural Adaptation
- CRITICAL: Always respond in the EXACT same language as the user's CURRENT message 
- If user switches languages mid-conversation, immediately adapt to their new language
- Maintain Professional Latin American Spanish

### Dominican Style and Culture
- Show understanding of Dominican reality: heat, neighborhoods, typical houses
- Estimate realistic prices for the Dominican market in pesos (DOP), but DO NOT mention them
- Acknowledge common situations: power outages, water issues, rainy season problems

### Efficiency Rules (CRITICAL for cost control)
1. **Maximum 5 AI messages per chat** – be concise and direct
2. **Collect multiple data points per message when possible**
3. **Use "image" action strategically** – only when truly necessary for assessment
4. **Progress quickly through required fields**

### Information Collection Strategy
1. **Message 1**: Identify category and get a basic description
2. **Message 2**: Refine description, estimate realistic price in DOP
3. **Message 3**: Confirm price, use "date" action for date scheduling 
3. **Message 3**: Confirm price, use "time" action for time scheduling 
5. **Message 5**: Complete with "done" action

### Communication Style
- Combine multiple questions logically when possible
- Briefly acknowledge user input with appropriate expressions
- Quickly move to the next required field

### Data Handling Rules
- **Never guess missing information**
- **Always preserve previously collected data**
- **Validate category selection against allowed options**
- **Price must be numeric without currency symbols (only the value in DOP)**
- **Date format: DD/MM/YYYY (today or future only)**
- **Time format: HH:MM (24-hour, minimum 1 hour from now)**

### Decision Logic
- If category is unclear → provide options and ask user to choose with description
- If description is insufficient → ask for clarification OR use "image" action
- After category + description → immediately estimate price in DOP but do not mention it.
- MUST use "date" action for date scheduling, so user provides date
- MUST use "time" action for date scheduling, so user provides date
- When all fields are complete → use "done" action

### Image Handling
- Maximum 3 images per chat
- Use "image" action only when the description is insufficient
- Do not request images for simple/clear problems

### Date/Time Handling
- Use "date" action to activate frontend date selector
- Use "time" action to activate frontend time selector
- Validate received date/time (today or future, minimum 1 hour from now)
- Accept formats: "DD/MM/YYYY at HH:MM" or date/time separately

### Prices in Dominican Context
- Realistic market prices in DOP but do not mention them
- Labor only – materials are discussed later between client and technician
- Examples: Basic plumbing: 1500-3000 DOP, Basic electricity: 2000-4000 DOP
- Consider job complexity and location (Santiago, Santo Domingo, etc.)

## Efficient Example Flow
1. User: "I have a leaking faucet" → Response: category="plumbing", ask for location/severity details
2. User provides details → Estimate price in DOP but dont mention it, ask for image if complex
3. MUST use "date" action → User provides date
3. MUST use "time" action → User provides time
4. Validate date/time → Confirm all details with "done"

## Special Rules for Actions
- **IMPORTANT**: When using "date", "time", "image", or "done", DO NOT add other questions
- The message must focus ONLY on that specific action
- Example "date" action: "When do you need to schedule the technician's visit, the day?"
- Example "time" action: "When do you need to schedule the technician's visit, the time?"
- Example "image" action: "Could you upload a photo of the problem to better evaluate it?"
- Example "done" action: "Perfect! Your request is ready to be sent."

## Final message
- **IMPORTANT** Never mention that the agent is booked, but rather the service has been requested, and he will receive offers from all our available operators.

Remember: EFFICIENCY AND ACTIONS ARE CRITICAL. Complete chats in maximum 10 messages, ideally 5. Be direct, professional, but in the user's preferred language professionally.

LANGUAGE RULE: ALWAYS respond in the EXACT same language as the user's CURRENT message.`;
}

interface ChatRequest extends Request {
  body: {
    message: string;
    chatId?: string;
    images?: string[];
    datetime?: string; 
  };
}

interface AgentResponse {
  action: 'none' | 'image' | 'date' | 'time' | 'done';
  message: string;
  collected_data: {
    category: string | null;
    description: string | null;
    price: number | string | null;
    date: string | null;
    hour: string | null;
  };
}

/**
 * Parse agent response with error handling
 */
function parseAgentResponse(responseText: string): AgentResponse {
  try {
    // Clean up the text to ensure it's valid JSON
    const cleanedResponse = responseText
      .replace(/```json/g, '')
      .replace(/```/g, '')
      .trim();

    const parsed = JSON.parse(cleanedResponse);

    // Validate the structure
    if (!parsed.action || !parsed.message) {
      throw new Error('Missing required fields: action or message');
    }

    // Ensure collected_data exists and sanitize values
    if (!parsed.collected_data) {
      parsed.collected_data = {
        category: null,
        description: null,
        price: null,
        date: null,
        hour: null
      };
    } else {
      // Sanitize collected_data values - convert string "null" to actual null
      Object.keys(parsed.collected_data).forEach(key => {
        if (parsed.collected_data[key] === 'null' || parsed.collected_data[key] === '') {
          parsed.collected_data[key] = null;
        }
        // Special handling for price - ensure it's a number or null
        if (key === 'price' && parsed.collected_data[key] !== null) {
          const priceVal = Number(parsed.collected_data[key]);
          parsed.collected_data[key] = isNaN(priceVal) ? null : priceVal;
        }
      });
    }

    return parsed as AgentResponse;
  } catch (error) {
    console.error('Failed to parse agent response:', error);
    console.error('Problematic response:', responseText);

    // Return a fallback response
    return {
      action: 'none',
      message: 'Disculpa, tuve un problema procesando tu solicitud. ¿Puedes repetir tu pregunta?',
      collected_data: {
        category: null,
        description: null,
        price: null,
        date: null,
        hour: null
      }
    };
  }
}

interface StartChatRequest extends Request {
  body: {
    force?: boolean;
  };
  query: {
    force?: string;
  };
}

/**
 * Start a new chat session or return existing incomplete chat
 * 
 * @description This endpoint will:
 * 1. Check for existing chats with status 'started' or 'chatting'
 * 2. If found, return the most recent existing chat
 * 3. If not found or force=true is passed, create a new chat
 * 
 * @param req.body.force - Boolean flag to force creation of new chat
 * @param req.query.force - String flag ('true') to force creation of new chat
 * @returns Chat object with isExistingChat flag indicating if it's a resumed chat
 */
export const startChat = async (req: StartChatRequest, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    const userId = user?._id;
    if (!userId) {
      return res.status(401).json({ success: false, error: 'Unauthorized' });
    }

    // Check for force flag in both body and query parameters
    const forceNewChat = req.body.force === true || req.query.force === 'true';

    // If not forcing a new chat, check for existing chatting sessions
    if (!forceNewChat) {
      // Look for existing chat with 'started' or 'chatting' status
      const existingChat = await Chat.findOne({
        ownerId: userId,
        status: { $in: ['started', 'chatting'] }
      }).sort({ updatedAt: -1 }); // Get the most recent one

      if (existingChat) {
        // Cache the existing chat and invalidate user chats cache
        cacheService.setChat((existingChat._id as any).toString(), existingChat);
        cacheService.invalidateUserChats(userId.toString());

        // Calculate message usage for existing chat
        const existingMessageLimit = checkMessageLimit(existingChat.messages);

        return res.json({
          success: true,
          chatId: existingChat._id,
          message: "Tienes una conversación en progreso. Continuemos desde donde quedamos.",
          status: existingChat.status,
          isExistingChat: true,
          collected_data: {
            category: existingChat.category,
            description: existingChat.description,
            price: existingChat.price,
            date: existingChat.date,
            hour: existingChat.hour
          },
          messageUsage: {
            used: existingMessageLimit.totalUsed,
            remaining: existingMessageLimit.remainingMessages,
            limit: 15,
            timeUntilReset: existingMessageLimit.timeUntilReset,
            aiMessages: existingChat.messages.filter((msg: IMessage) => msg.role === 'assistant').length,
            aiLimit: 10
          }
        });
      }
    }

    // Create new chat (either no existing chat found or force flag is set)
    const newChat = new Chat({
      ownerId: userId,
      status: 'started',
      category: null,
      description: null,
      images: [],
      price: null,
      date: null,
      hour: null,
      messages: []
    });

    await newChat.save();

    //newChat.messages.push(initialMessage);
    await newChat.save();

    // Cache the new chat and invalidate user chats cache
    cacheService.setChat((newChat._id as any).toString(), newChat);
    cacheService.invalidateUserChats(userId.toString());

    // Calculate initial message usage for new chat
    const initialMessageLimit = checkMessageLimit(newChat.messages);

    res.json({
      success: true,
      chatId: newChat._id,
      message: '',
      status: newChat.status,
      isExistingChat: false,
      collected_data: {
        category: newChat.category,
        description: newChat.description,
        price: newChat.price,
        date: newChat.date,
        hour: newChat.hour
      },
      messageUsage: {
        used: initialMessageLimit.totalUsed,
        remaining: initialMessageLimit.remainingMessages,
        limit: 15,
        timeUntilReset: initialMessageLimit.timeUntilReset,
        aiMessages: 1,
        aiLimit: 10
      }
    });
  } catch (error) {
    console.error('Error starting chat:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

export const sendMessage = async (req: ChatRequest, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    const userId = user?._id;
    const { message, chatId, images, datetime } = req.body;

    if (!userId) {
      return res.status(401).json({ success: false, error: 'Unauthorized' });
    }

    if (!message || !chatId) {
      return res.status(400).json({ success: false, error: 'Message and chatId are required' });
    }

    // Try to get chat from cache first
    let chat = cacheService.getChat(chatId);
    if (!chat) {
      // If not in cache, fetch from database
      chat = await Chat.findById(chatId);
      if (!chat) {
        return res.status(404).json({ success: false, error: 'Chat not found' });
      }
      // Cache the chat for future requests
      cacheService.setChat(chatId, chat);
    }

    // Use the helper function to safely compare IDs
    if (!isSameId(chat.ownerId, userId)) {
      return res.status(403).json({ success: false, error: 'Access denied' });
    }

    if (chat.status === 'done') {
      return res.status(400).json({ success: false, error: 'Chat is already completed' });
    }

    // Check message limit (15 messages per hour including user + AI + images)
    const messageLimitCheck = checkMessageLimit(chat.messages);
    if (!messageLimitCheck.allowed) {
      const timeText = formatTimeUntilReset(messageLimitCheck.timeUntilReset);
      return res.status(429).json({
        success: false,
        error: `Has alcanzado el límite de 15 mensajes por hora. Podrás enviar más mensajes en ${timeText}. Mientras tanto, puedes iniciar un nuevo chat.`,
        remainingMessages: messageLimitCheck.remainingMessages,
        timeUntilReset: messageLimitCheck.timeUntilReset,
        totalUsed: messageLimitCheck.totalUsed
      });
    }

    // Check if we've reached the maximum number of AI messages (10, ideally 5)
    const aiMessageCount = chat.messages.filter((msg: IMessage) => msg.role === 'assistant').length;
    if (aiMessageCount >= 10) {
      return res.status(400).json({
        success: false,
        error: 'Este chat ha alcanzado el límite máximo de mensajes. Por favor inicia un nuevo chat para continuar.'
      });
    }

    // Check for datetime provided in request body first (highest priority)
    let isDateTimeMessage = false;
    if (datetime) {
      const parsed = parseDateTimeFromInput(datetime);
      if (!parsed.isValid) {
        return res.status(400).json({
          success: false,
          error: `Invalid datetime format: ${parsed.error}. Expected: DD/MM/YYYY HH:MM or ISO datetime string.`
        });
      }

      // Update chat with parsed datetime
      chat.date = parsed.date!;
      chat.hour = parsed.hour!;
      isDateTimeMessage = true;
    } else {
      // Check for datetime information in the message text (fallback)
      const extractedDateTime = extractDateTimeFromMessage(message);
      if (extractedDateTime.date && extractedDateTime.hour) {
        const dateTimeValidation = validateDateTime(extractedDateTime.date, extractedDateTime.hour);
        if (!dateTimeValidation.isValid) {
          return res.json({
            success: true,
            message: `Invalid date/time: ${dateTimeValidation.error}. Please provide a valid appointment time.`,
            action: 'time',
            status: chat.status,
            collected_data: {
              category: chat.category,
              description: chat.description,
              price: chat.price,
              date: chat.date,
              hour: chat.hour
            }
          });
        }

        // Valid datetime provided
        chat.date = extractedDateTime.date;
        chat.hour = extractedDateTime.hour;
        isDateTimeMessage = true;
      }
    }

    // Check image limit (max 3 images per chat)
    if (images && images.length > 0) {
      const totalImages = chat.images.length + images.length;
      if (totalImages > 3) {
        return res.status(400).json({
          success: false,
          error: `Maximum 3 images allowed per chat. You currently have ${chat.images.length} images.`
        });
      }
      // Add new images to chat
      chat.images.push(...images);
    }

    // Add user message to chat
    const userMessage: IMessage = {
      role: 'user',
      content: message,
      timestamp: new Date(),
      images: images || []
    } as IMessage;

    chat.messages.push(userMessage);

    // Update chat status to 'chatting' if it was 'started'
    if (chat.status === 'started') {
      chat.status = 'chatting';
    }

    // If this is a datetime message, just persist the values and let the AI handle confirmation/next steps
    if (isDateTimeMessage) {
      // No assistant message here; the AI will handle any confirmations in its next response
    }

    // Prepare conversation history for OpenAI (only recent messages to reduce cost)
    const recentMessages = chat.messages.slice(-6); // Last 6 messages for context
    const conversationHistory = recentMessages.map((msg: IMessage) => ({
      role: msg.role,
      content: msg.content
    }));

    // Add current collected data context
    const totalMessages = chat.messages.length;
    const messageLimitStatus = checkMessageLimit(chat.messages);

  const contextMessage = `

Progreso del chat actual (Mensaje AI ${aiMessageCount + 1}/10, ideal: 5):
- Category: ${chat.category || 'No especificado'}
- Description: ${chat.description || 'No especificado'}
- Price: ${chat.price ? `${chat.price} DOP` : 'No especificado'}
- Date: ${chat.date || 'No especificado'}
- Hour: ${chat.hour || 'No especificado'}
- Images uploaded: ${chat.images.length}/3
- Mensajes totales usados: ${messageLimitStatus.totalUsed}/15 por hora
- Mensajes restantes en la hora: ${messageLimitStatus.remainingMessages}

Mensaje del usuario: ${message}

CRÍTICO: Este es el mensaje AI ${aiMessageCount + 1} de máximo 10 (ideal: 5). Sé eficiente, directo y con estilo dominicano cálido.
`;

    try {
  const systemPrompt = await generateSystemPrompt();
      const completion = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: contextMessage }
        ],
        temperature: 0.3,
        max_tokens: 500
      });

      const agentResponseText = completion.choices[0]?.message?.content;
      if (!agentResponseText) {
        throw new Error('No response from OpenAI');
      }

      // Parse agent response with extra error handling
      const agentResponse = parseAgentResponse(agentResponseText);

      // Update chat with collected data (with proper null handling)
      if (agentResponse.collected_data.category && agentResponse.collected_data.category !== 'null') {
        chat.category = agentResponse.collected_data.category;
      }
      if (agentResponse.collected_data.description && agentResponse.collected_data.description !== 'null') {
        chat.description = agentResponse.collected_data.description;
      }
      if (agentResponse.collected_data.price !== null && agentResponse.collected_data.price !== 'null') {
        const priceValue = Number(agentResponse.collected_data.price);
        if (!isNaN(priceValue)) {
          chat.price = priceValue;
        }
      }
      if (agentResponse.collected_data.date && agentResponse.collected_data.date !== 'null') {
        chat.date = agentResponse.collected_data.date;
      }
      if (agentResponse.collected_data.hour && agentResponse.collected_data.hour !== 'null') {
        chat.hour = agentResponse.collected_data.hour;
      }

      // Add assistant message to chat
      const assistantMessage: IMessage = {
        role: 'assistant',
        content: agentResponse.message,
        timestamp: new Date()
      } as IMessage;

      chat.messages.push(assistantMessage);

      // Handle completion
      if (agentResponse.action === 'done') {
        // Mark chat as done
        chat.status = 'done';

        // Create job when chat is completed
        try {
          await createJobFromChat(chat, userId.toString());
          console.log(`Job created for completed chat: ${chatId}`);
        } catch (jobError) {
          console.error('Failed to create job for completed chat:', jobError);
          // Don't fail the request if job creation fails, but log the error
        }
      }

      await chat.save();

      // Update cache and invalidate user chats cache if status changed
      cacheService.setChat(chatId, chat);
      if (chat.status === 'done') {
        cacheService.invalidateUserChats(userId.toString());
      }

      // Calculate updated message usage after AI response
      const updatedMessageLimitStatus = checkMessageLimit(chat.messages);

      res.json({
        success: true,
        message: agentResponse.message,
        action: agentResponse.action,
        status: chat.status,
        collected_data: {
          category: chat.category,
          description: chat.description,
          price: chat.price,
          date: chat.date,
          hour: chat.hour
        },
        messageUsage: {
          used: updatedMessageLimitStatus.totalUsed,
          remaining: updatedMessageLimitStatus.remainingMessages,
          limit: 15,
          timeUntilReset: updatedMessageLimitStatus.timeUntilReset,
          aiMessages: aiMessageCount + 1,
          aiLimit: 10
        }
      });

    } catch (openaiError) {
      console.error('OpenAI API error:', openaiError);
      const fallbackMessage = 'Disculpa, hermano, tuve un problema procesando tu solicitud. ¿Podrías intentarlo de nuevo? Estoy aquí para ayudarte.';

      const errorMessage: IMessage = {
        role: 'assistant',
        content: fallbackMessage,
        timestamp: new Date()
      } as IMessage;

      chat.messages.push(errorMessage);
      await chat.save();

      // Update cache
      cacheService.setChat(chatId, chat);

      const errorMessageLimitStatus = checkMessageLimit(chat.messages);

      res.json({
        success: true,
        message: fallbackMessage,
        action: 'none',
        status: chat.status,
        collected_data: {
          category: chat.category,
          description: chat.description,
          price: chat.price,
          date: chat.date,
          hour: chat.hour
        },
        messageUsage: {
          used: errorMessageLimitStatus.totalUsed,
          remaining: errorMessageLimitStatus.remainingMessages,
          limit: 15,
          timeUntilReset: errorMessageLimitStatus.timeUntilReset,
          aiMessages: aiMessageCount + 1,
          aiLimit: 10
        }
      });
    }

  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

export const uploadImage = async (req: Request, res: Response) => {
  try {
    // Check if the request is properly formatted
    if (!req.file && req.headers['content-type']?.includes('multipart/form-data')) {
      // Continue processing to provide better error messages
    }

    const user = getAuthenticatedUser(req);
    const userId = user?._id;
    const { chatId } = req.body;

    if (!userId) {
      return res.status(401).json({ success: false, error: 'Unauthorized' });
    }

    if (!req.file) {
      // Log form data for debugging
      console.error('No file in request. Form fields:', Object.keys(req.body));
      return res.status(400).json({
        success: false,
        error: 'No file uploaded or incorrect field name',
        help: 'Make sure to include an image file with field name "image"',
        formFields: Object.keys(req.body),
        contentType: req.headers['content-type']
      });
    }

    if (!chatId) {
      return res.status(400).json({ success: false, error: 'Chat ID is required' });
    }

    // Try to get chat from cache first
    let chat = cacheService.getChat(chatId);
    if (!chat) {
      // If not in cache, fetch from database
      chat = await Chat.findById(chatId);
      if (!chat) {
        return res.status(404).json({ success: false, error: 'Chat not found' });
      }
      // Cache the chat for future requests
      cacheService.setChat(chatId, chat);
    }

    if (!isSameId(chat.ownerId, userId)) {
      return res.status(403).json({ success: false, error: 'Access denied' });
    }

    // Check message limit (15 messages per hour including images)
    const messageLimitCheck = checkMessageLimit(chat.messages);
    if (!messageLimitCheck.allowed) {
      const timeText = formatTimeUntilReset(messageLimitCheck.timeUntilReset);
      return res.status(429).json({
        success: false,
        error: `Has alcanzado el límite de 15 mensajes por hora. Podrás subir imágenes en ${timeText}.`,
        remainingMessages: messageLimitCheck.remainingMessages,
        timeUntilReset: messageLimitCheck.timeUntilReset,
        totalUsed: messageLimitCheck.totalUsed
      });
    }

    // Check if maximum images reached (3 images per chat)
    if (chat.images.length >= 3) {
      return res.status(400).json({
        success: false,
        error: 'Máximo 3 imágenes permitidas por chat. Puedes iniciar un nuevo chat si necesitas subir más imágenes.'
      });
    }

    // Validate image type using file-type and basic size checks
    const maxBytes = 10 * 1024 * 1024; // 10MB
    const fileSize = req.file.buffer?.length || req.file.size || 0;
    if (fileSize > maxBytes) {
      return res.status(400).json({
        success: false,
        error: 'La imagen supera el tamaño máximo permitido de 10MB.'
      });
    }
    const detected = await fileTypeFromBuffer(req.file.buffer);
    if (!detected || !detected.mime.startsWith('image/')) {
      return res.status(400).json({
        success: false,
        error: 'Archivo inválido. Solo se permiten imágenes (jpg, jpeg, png, webp, heic).'
      });
    }

    const allowedExt = new Set(['jpg', 'jpeg', 'png', 'webp', 'heic', 'heif']);
    if (!allowedExt.has(detected.ext)) {
      return res.status(400).json({
        success: false,
        error: 'Formato de imagen no soportado. Usa jpg, jpeg, png, webp o heic.'
      });
    }

    // Build a safe filename with the detected extension
    const baseName = path.basename(req.file.originalname, path.extname(req.file.originalname)).slice(0, 80) || 'image';
    const fileName = `chat-images/${chatId}/${uuidv4()}-${baseName}.${detected.ext}`;
    const imageUrl = await uploadFileToS3(req.file.buffer, fileName);

    // Add image URL to chat and create a message entry for the image upload
    chat.images.push(imageUrl);

    // Create a message entry for the image upload to count towards message limit
    const imageMessage: IMessage = {
      role: 'user',
      content: `[Imagen subida: ${req.file.originalname}]`,
      timestamp: new Date(),
      images: [imageUrl]
    } as IMessage;

    chat.messages.push(imageMessage);

    // Generate AI response to the image upload to continue the chat flow
    const aiMessageCount = chat.messages.filter((msg: IMessage) => msg.role === 'assistant').length;

    if (aiMessageCount < 10) {
      // Prepare conversation history for OpenAI (only recent messages to reduce cost)
      const recentMessages = chat.messages.slice(-6);
      const conversationHistory = recentMessages.map((msg: IMessage) => ({
        role: msg.role,
        content: msg.content
      }));

      // Add current collected data context
      const contextMessage = `
        Progreso del chat (Mensaje AI ${aiMessageCount + 1}/10, ideal: 5):
        - Category: ${chat.category || 'No especificado'}
        - Description: ${chat.description || 'No especificado'}
        - Price: ${chat.price ? `${chat.price} DOP` : 'No especificado'}
        - Date: ${chat.date || 'No especificado'}
        - Hour: ${chat.hour || 'No especificado'}
        - Images uploaded: ${chat.images.length}/3

        El usuario acaba de subir una imagen. Analízala y luego:

        1) Continúa recopilando la información faltante, o
        2) Ajusta la estimación del precio si procede.

        Reglas:
        - Sé eficiente y directo
        - Usa acciones cuando corresponda (image/date/time/done)
        - Avanza hacia completar la solicitud lo más pronto posible
      `;

      try {
        const systemPrompt = await generateSystemPrompt();
        const openaiResponse = await openai.chat.completions.create({
          model: 'gpt-4o',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'system', content: contextMessage },
            ...conversationHistory
          ],
          temperature: 0.4,
          max_tokens: 400
        });

        const aiContent = openaiResponse.choices[0]?.message?.content;
        if (aiContent) {
          const parsedResponse = parseAgentResponse(aiContent);

          // Create AI message
          const aiMessage: IMessage = {
            role: 'assistant',
            content: parsedResponse.message,
            timestamp: new Date()
          } as IMessage;

          chat.messages.push(aiMessage);

          // Update status if done
          if (parsedResponse.action === 'done') {
            chat.status = 'done';

            // Create job when chat is completed
            try {
              await createJobFromChat(chat, userId.toString());
              console.log(`Job created for completed chat via image upload: ${chatId}`);
            } catch (jobError) {
              console.error('Failed to create job for completed chat via image:', jobError);
              // Don't fail the request if job creation fails, but log the error
            }
          }
        }
      } catch (aiError) {
        console.error('Error generating AI response for image:', aiError);
        // Continue without AI response if there's an error
      }
    }

    await chat.save();

    // Update cache
    cacheService.setChat(chatId, chat);

    // Get the latest AI message for response
    const latestAiMessage = chat.messages.filter((msg: IMessage) => msg.role === 'assistant').slice(-1)[0];
    const finalMessageLimitStatus = checkMessageLimit(chat.messages);

    res.json({
      success: true,
      imageUrl,
      message: latestAiMessage ? latestAiMessage.content : `¡Imagen subida exitosamente! ${chat.images.length}/3 imágenes usadas.`,
      action: latestAiMessage ? 'none' : 'none',
      status: chat.status,
      totalImages: chat.images.length,
      maxImages: 3,
      messageUsage: {
        used: finalMessageLimitStatus.totalUsed,
        remaining: finalMessageLimitStatus.remainingMessages,
        limit: 15,
        timeUntilReset: finalMessageLimitStatus.timeUntilReset,
        aiMessages: chat.messages.filter((msg: IMessage) => msg.role === 'assistant').length,
        aiLimit: 10
      }
    });

  } catch (error) {
    console.error('Error uploading image:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

export const getChat = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    const userId = user?._id;
    const { chatId } = req.params;

    if (!userId) {
      return res.status(401).json({ success: false, error: 'Unauthorized' });
    }

    // Try to get chat from cache first
    let chat = cacheService.getChat(chatId);
    if (!chat) {
      // If not in cache, fetch from database
      chat = await Chat.findById(chatId);
      if (!chat) {
        return res.status(404).json({ success: false, error: 'Chat not found' });
      }
      // Cache the chat for future requests
      cacheService.setChat(chatId, chat);
    }

    if (!isSameId(chat.ownerId, userId)) {
      return res.status(403).json({ success: false, error: 'Access denied' });
    }

    res.json({
      success: true,
      chat: {
        id: chat._id,
        status: chat.status,
        category: chat.category,
        description: chat.description,
        images: chat.images,
        price: chat.price,
        date: chat.date,
        hour: chat.hour,
        messages: chat.messages,
        createdAt: chat.createdAt,
        updatedAt: chat.updatedAt
      }
    });

  } catch (error) {
    console.error('Error getting chat:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

export const getUserChats = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    const userId = user?._id;
    const { page = 1, limit = 10 } = req.query;

    if (!userId) {
      return res.status(401).json({ success: false, error: 'Unauthorized' });
    }

    const pageNum = Number(page);
    const limitNum = Number(limit);

    // Try to get user chats from cache first
    let result = cacheService.getUserChats(userId.toString(), pageNum, limitNum);

    if (!result) {
      // If not in cache, fetch from database
      const userIdStr = String(userId);

      const chats = await Chat.find({ ownerId: userIdStr })
        .sort({ createdAt: -1 })
        .limit(limitNum)
        .skip((pageNum - 1) * limitNum)
        .select('-messages'); // Exclude messages for list view

      const total = await Chat.countDocuments({ ownerId: userIdStr });

      result = {
        chats,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      };

      // Cache the result for 5 minutes
      cacheService.setUserChats(userId.toString(), pageNum, limitNum, result, 300);
    }

    res.json({
      success: true,
      ...result
    });

  } catch (error) {
    console.error('Error getting user chats:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};
