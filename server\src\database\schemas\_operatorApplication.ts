import mongoose, { Document, Schema, Types } from 'mongoose';
import { DOCUMENT_REQUIREMENT_CATEGORIES, DocumentRequirementCategory } from '../../types/documentRequirements';

export interface IOperatorDocument {
  name: string;
  url: string;
  type: string; // mime type
  category?: DocumentRequirementCategory; // mapped to requirement categories
}

export type OperatorApplicationStatus = 'pending' | 'approved' | 'rejected';

export interface IOperatorApplication extends Document {
  applicantId: Types.ObjectId; // Account ID of the user applying
  authorizedCategories: string[];
  description?: string;
  skills?: string[];
  documents: IOperatorDocument[];
  documents_required: DocumentRequirementCategory[]; // required categories for this application
  status: OperatorApplicationStatus;
  reviewedBy?: Types.ObjectId; // Admin account ID
  reviewedAt?: Date;
  reviewNote?: string;
  createdAt: Date;
  updatedAt: Date;
}

const operatorDocumentSchema = new Schema<IOperatorDocument>({
  name: { type: String, required: true, trim: true, minlength: 1, maxlength: 256 },
  url: {
    type: String,
    required: true,
    trim: true,
    match: [/^https?:\/\/\S+$/i, 'Invalid URL format'],
  },
  type: {
    type: String,
    required: true,
    trim: true,
    enum: [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ],
    message: 'Document type not allowed'
  },
  category: {
    type: String,
    enum: DOCUMENT_REQUIREMENT_CATEGORIES,
    required: false,
  },
}, { _id: false });

const operatorApplicationSchema = new Schema<IOperatorApplication>({
  applicantId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'Account',
    index: true,
  },
  authorizedCategories: [
    {
      type: String,
      trim: true,
      lowercase: true,
    }
  ],
  description: { type: String, maxlength: 2000 }, // optional
  skills: [{ type: String, maxlength: 100 }], // optional
  documents: { type: [operatorDocumentSchema], default: [] },
  documents_required: {
    type: [String],
    default: ['none'],
    validate: {
      validator: function (arr: string[]) {
        if (!Array.isArray(arr)) return false;
        // All values must be in allowed set
        return arr.every((v) => (DOCUMENT_REQUIREMENT_CATEGORIES as readonly string[]).includes(String(v)));
      },
      message: 'Invalid document requirement category provided',
    },
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending',
    index: true,
  },
  reviewedBy: { type: Schema.Types.ObjectId, ref: 'Account' },
  reviewedAt: { type: Date },
  reviewNote: { type: String },
}, { timestamps: true });

// Ensure at least one authorized category is provided
operatorApplicationSchema.path('authorizedCategories').validate(function (value: string[]) {
  return Array.isArray(value) && value.length > 0;
}, 'At least one authorized category is required');

// Validate that, if documents are required (non-'none'), each required category has at least one document in that category
operatorApplicationSchema.pre('validate', function (next) {
  const app = this as unknown as IOperatorApplication;
  const required = (app.documents_required || []).filter((c) => c !== 'none');
  if (required.length === 0) return next();

  const docs = Array.isArray(app.documents) ? app.documents : [];
  const categoriesWithDocs = new Set(
    docs
      .map((d: any) => d?.category)
      .filter((c: any) => typeof c === 'string')
  );

  const missing = required.filter((c) => !categoriesWithDocs.has(c));
  if (missing.length > 0) {
    return next(new Error(`Missing required documents for categories: ${missing.join(', ')}`));
  }
  return next();
});

operatorApplicationSchema.index({ applicantId: 1, status: 1, createdAt: -1 });

export default mongoose.model<IOperatorApplication>('OperatorApplication', operatorApplicationSchema);

