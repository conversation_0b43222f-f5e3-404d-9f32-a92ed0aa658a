import { Request, Response } from 'express';
import { PaymentMethodService } from '../services/paymentMethodService';
import { PaymentProcessorService } from '../services/paymentProcessorService';
import { getAuthenticatedUser } from '../utils/auth';
import { logger } from '../utils/logger';
import {
  CreatePaymentMethodRequest,
  ChargePaymentMethodRequest,
  MethodNotificationData
} from '../types/azul';
import { z } from 'zod';
import mongoose from 'mongoose';
import { PaymentTransaction } from '../database/schemas';
import PaymentError, { NotFoundPaymentError, InvalidPaymentDataError, IdempotencyConflictError } from '../errors/paymentErrors';

// Factory to create services per-request so tests can inject mocks and resources
// are managed per request. Tests can call getServices(undefined, 'sandbox') or
// mock this exported function.
export const getServices = (req?: Request, envOverride?: 'production' | 'sandbox') => {
  const environment = envOverride ?? (process.env.NODE_ENV === 'production' ? 'production' : 'sandbox');
  const paymentMethodService = new PaymentMethodService(environment);
  const paymentProcessorService = new PaymentProcessorService(environment);
  return { paymentMethodService, paymentProcessorService };
};

// Validation schemas
const _createPaymentMethodBase = z.object({
  // Option 1: Client-provided token
  azulToken: z.string().optional(),

  // Option 2: Card details
  cardNumber: z.string().optional(),
  cvc: z.string().optional(),
  expiryMonth: z.number().min(1).max(12).optional(),
  expiryYear: z.number()
    .min(new Date().getFullYear()) // Cannot be in the past
    .optional(),
  cardHolderName: z.string().optional(),

  // Options
  setAsFavorite: z.boolean().optional()
});

type CreatePaymentMethodBody = z.infer<typeof _createPaymentMethodBase>;

const createPaymentMethodSchema = _createPaymentMethodBase.refine(
  (data: CreatePaymentMethodBody) => Boolean(data.azulToken) || (Boolean(data.cardNumber) && Boolean(data.expiryMonth) && Boolean(data.expiryYear)),
  {
    message: "Either azulToken or card details (cardNumber, expiryMonth, expiryYear) must be provided"
  }
);

const chargePaymentMethodSchema = z.object({
  amount: z.number().min(1),
  currency: z.enum(['DOP', 'USD']),
  // Use TitleCase to match Azul TrxType values ('Sale' | 'Hold')
  // Backwards-compat: clients should send 'Sale' or 'Hold'
  type: z.enum(['Sale', 'Hold']),
  jobId: z.string().optional(),
  bidId: z.string().optional(),
  idempotencyKey: z.string().uuid(),

  browserInfo: z.object({
    acceptHeader: z.string(),
    userAgent: z.string(),
    javaEnabled: z.boolean(),
    language: z.string(),
    colorDepth: z.number(),
    screenHeight: z.number(),
    screenWidth: z.number(),
    timeZoneOffset: z.number(),
    javascriptEnabled: z.boolean()
  }).optional(),

  customerInfo: z.object({
    email: z.string().email().optional(),
    phone: z.string().optional(),
    billingAddress: z.object({
      line1: z.string().optional(),
      line2: z.string().optional(),
      city: z.string().optional(),
      state: z.string().optional(),
      postalCode: z.string().optional(),
      countryCode: z.string().optional()
    }).optional()
  }).optional()
});

/**
 * POST /payment-methods
 * Create a new payment method
 */
export const createPaymentMethod = async (req: Request, res: Response) => {
  try {
  const { paymentMethodService } = getServices(req);
    const user = getAuthenticatedUser(req);
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Validate request body
    const validation = createPaymentMethodSchema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({
        success: false,
        error: 'Invalid request data',
        details: validation.error.errors
      });
    }

    const request = validation.data as CreatePaymentMethodRequest;

  // Create payment method
  const paymentMethod = await paymentMethodService.createPaymentMethod(user._id, request);

    res.status(201).json({
      success: true,
      data: paymentMethod
    });

  } catch (error) {
    console.error('Error creating payment method:', error);

    if (error instanceof PaymentError) {
      return res.status(error.statusCode).json({ success: false, error: error.message, code: error.code });
    }

    const errorMessage = error instanceof Error ? error.message : 'Failed to create payment method';
    res.status(500).json({ success: false, error: errorMessage });
  }
};

/**
 * GET /payment-methods
 * Get all payment methods for the authenticated user
 */
export const getPaymentMethods = async (req: Request, res: Response) => {
  try {
  const { paymentMethodService } = getServices(req);
    const user = getAuthenticatedUser(req);
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

  const paymentMethods = await paymentMethodService.getPaymentMethods(user._id);

    res.json({
      success: true,
      data: paymentMethods
    });

  } catch (error) {
    console.error('Error fetching payment methods:', error);

    res.status(500).json({
      success: false,
      error: 'Failed to fetch payment methods'
    });
  }
};

/**
 * PATCH /payment-methods/:id/favorite
 * Set a payment method as favorite
 */
export const setFavoritePaymentMethod = async (req: Request, res: Response) => {
  try {
  const { paymentMethodService } = getServices(req);
    const user = getAuthenticatedUser(req);
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const paymentMethodId = req.params.id;
    if (!paymentMethodId) {
      return res.status(400).json({
        success: false,
        error: 'Payment method ID is required'
      });
    }

    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(paymentMethodId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid payment method ID format'
      });
    }

  const paymentMethod = await paymentMethodService.setFavoritePaymentMethod(user._id, paymentMethodId);

    res.json({
      success: true,
      data: paymentMethod
    });

  } catch (error) {
    console.error('Error setting favorite payment method:', error);

    if (error instanceof PaymentError) {
      return res.status(error.statusCode).json({ success: false, error: error.message, code: error.code });
    }

    const errorMessage = error instanceof Error ? error.message : 'Failed to set favorite payment method';
    res.status(500).json({ success: false, error: errorMessage });
  }
};

/**
 * DELETE /payment-methods/:id
 * Delete a payment method
 */
export const deletePaymentMethod = async (req: Request, res: Response) => {
  try {
  const { paymentMethodService } = getServices(req);
    const user = getAuthenticatedUser(req);
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const paymentMethodId = req.params.id;
    if (!paymentMethodId) {
      return res.status(400).json({
        success: false,
        error: 'Payment method ID is required'
      });
    }

    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(paymentMethodId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid payment method ID format'
      });
    }

  await paymentMethodService.deletePaymentMethod(user._id, paymentMethodId);

    res.json({
      success: true,
      message: 'Payment method deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting payment method:', error);

    if (error instanceof PaymentError) {
      return res.status(error.statusCode).json({ success: false, error: error.message, code: error.code });
    }

    const errorMessage = error instanceof Error ? error.message : 'Failed to delete payment method';
    res.status(500).json({ success: false, error: errorMessage });
  }
};

/**
 * POST /payment-methods/:id/charge
 * Charge a payment method
 */
export const chargePaymentMethod = async (req: Request, res: Response) => {
  try {
  const { paymentProcessorService } = getServices(req);
    const user = getAuthenticatedUser(req);
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const paymentMethodId = req.params.id;
    if (!paymentMethodId) {
      return res.status(400).json({
        success: false,
        error: 'Payment method ID is required'
      });
    }

    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(paymentMethodId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid payment method ID format'
      });
    }

    // Validate request body
    const validation = chargePaymentMethodSchema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({
        success: false,
        error: 'Invalid request data',
        details: validation.error.errors
      });
    }

    const request: ChargePaymentMethodRequest = validation.data;

  // Process payment
  const result = await paymentProcessorService.chargePaymentMethod(user._id, paymentMethodId, request);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Error charging payment method:', error);

    if (error instanceof PaymentError) {
      return res.status(error.statusCode).json({ success: false, error: error.message, code: error.code });
    }

    const errorMessage = error instanceof Error ? error.message : 'Failed to charge payment method';
    res.status(500).json({ success: false, error: errorMessage });
  }
};

/**
 * POST /payments/3ds/method-notification/:orderId
 * Handle 3DS Method notification from ACS
 */
export const handle3DSMethodNotification = async (req: Request, res: Response) => {
  try {
  const { paymentProcessorService } = getServices(req);
  const azulOrderId = req.params.orderId;
    if (!azulOrderId) {
      return res.status(400).json({
        success: false,
        error: 'Order ID is required'
      });
    }

    // Find transaction by Azul Order ID
    const transaction = await PaymentTransaction.findOne({
      providerReference: azulOrderId
    });
    if (!transaction) {
      return res.status(404).json({
        success: false,
        error: 'Transaction not found'
      });
    }

    const notificationData: MethodNotificationData = {
      threeDSServerTransID: req.body.threeDSServerTransID || '',
      threeDSMethodData: req.body.threeDSMethodData
    };

    // Process method notification
    await paymentProcessorService.processMethodNotification(
      (transaction._id as mongoose.Types.ObjectId).toString(),
      notificationData
    );

    // Return 200 OK quickly as required by ACS
    res.status(200).json({
      success: true
    });

  } catch (error) {
    // High-severity logging with context so this isn't silently swallowed
    const context = {
      orderId: req.params.orderId,
      body: req.body,
      transactionId: (req as any)._transactionId || 'unknown'
    };

    // Use structured logger if available
    try {
      logger.error('Error handling 3DS method notification', { error: error instanceof Error ? error.stack || error.message : String(error), ...context });
    } catch (logErr) {
      // Fallback to console if logger fails
      console.error('Error handling 3DS method notification (logger failed):', logErr, 'original error:', error, 'context:', context);
    }

    // Emit monitoring events if integrations are available (no-op otherwise)
    try {
      // Example: Sentry
      (globalThis as any).Sentry?.captureException?.(error);
      // Example: Prometheus or custom metrics
      (globalThis as any).metrics?.increment?.('azul_method_notification_errors');
    } catch (monErr) {
      console.warn('Monitoring emission failed for 3DS method notification error', monErr);
    }

    // Still return 200 to ACS as required by the specification
    res.status(200).json({ success: true });
  }
};

/**
 * POST /payments/3ds/challenge/:transactionId
 * Process 3DS Challenge response
 */
export const process3DSChallenge = async (req: Request, res: Response) => {
  try {
  const { paymentProcessorService } = getServices(req);
    const user = getAuthenticatedUser(req);
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const transactionId = req.params.transactionId;
    if (!transactionId) {
      return res.status(400).json({
        success: false,
        error: 'Transaction ID is required'
      });
    }

    const challengeResponse = req.body.cres;
    if (!challengeResponse) {
      return res.status(400).json({
        success: false,
        error: 'Challenge response (cres) is required'
      });
    }

  // Process challenge
  const result = await paymentProcessorService.processChallenge(transactionId, challengeResponse);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Error processing 3DS challenge:', error);

    const errorMessage = error instanceof Error ? error.message : 'Failed to process 3DS challenge';
    let statusCode = 500;

    // Prefer explicit error types/codes over message matching
    if (error instanceof NotFoundPaymentError || (error as any)?.code === 'NOT_FOUND' || (error as any)?.status === 404) {
      statusCode = 404;
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage
    });
  }
};

/**
 * POST /payments/:transactionId/refund
 * Refund a transaction
 */
export const refundTransaction = async (req: Request, res: Response) => {
  try {
  const { paymentProcessorService } = getServices(req);
    const user = getAuthenticatedUser(req);
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const transactionId = req.params.transactionId;
    if (!transactionId) {
      return res.status(400).json({
        success: false,
        error: 'Transaction ID is required'
      });
    }

    const { amount, reason } = req.body;

    if (amount !== undefined && (typeof amount !== 'number' || amount <= 0)) {
      return res.status(400).json({
        success: false,
        error: 'Amount must be a positive number'
      });
    }

    await paymentProcessorService.refundTransaction(
      user._id,
      transactionId,
      amount,
      reason || 'Customer request'
    );

    res.json({
      success: true,
      message: 'Refund processed successfully'
    });

  } catch (error) {
    console.error('Error processing refund:', error);

    const errorMessage = error instanceof Error ? error.message : 'Failed to process refund';
    let statusCode = 500;

    if (error instanceof NotFoundPaymentError || (error as any)?.code === 'NOT_FOUND' || (error as any)?.status === 404) {
      statusCode = 404;
    } else if (error instanceof InvalidPaymentDataError || (error as any)?.code === 'INVALID_PAYMENT_DATA' || (error as any)?.status === 400) {
      statusCode = 400;
    } else if (error instanceof IdempotencyConflictError || (error as any)?.code === 'IDEMPOTENCY_CONFLICT' || (error as any)?.status === 409) {
      statusCode = 409;
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage
    });
  }
};

/**
 * POST /payments/:transactionId/capture
 * Capture a held transaction
 */
export const captureTransaction = async (req: Request, res: Response) => {
  try {
  const { paymentProcessorService } = getServices(req);
    const user = getAuthenticatedUser(req);
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const transactionId = req.params.transactionId;
    if (!transactionId) {
      return res.status(400).json({
        success: false,
        error: 'Transaction ID is required'
      });
    }

    const { amount } = req.body;

    if (amount !== undefined && (typeof amount !== 'number' || amount <= 0)) {
      return res.status(400).json({
        success: false,
        error: 'Amount must be a positive number'
      });
    }

  await paymentProcessorService.captureTransaction(user._id, transactionId, amount);

    res.json({
      success: true,
      message: 'Transaction captured successfully'
    });

  } catch (error) {
    console.error('Error capturing transaction:', error);

    const errorMessage = error instanceof Error ? error.message : 'Failed to capture transaction';
    let statusCode = 500;

    if (error instanceof NotFoundPaymentError || (error as any)?.code === 'NOT_FOUND' || (error as any)?.status === 404) {
      statusCode = 404;
    } else if (error instanceof InvalidPaymentDataError || (error as any)?.code === 'INVALID_PAYMENT_DATA' || (error as any)?.status === 400) {
      statusCode = 400;
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage
    });
  }
};
