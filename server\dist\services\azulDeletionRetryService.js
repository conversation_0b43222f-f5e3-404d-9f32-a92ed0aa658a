"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AzulDeletionRetryService = void 0;
exports.getAzulDeletionRetryService = getAzulDeletionRetryService;
const tslib_1 = require("tslib");
const schemas_1 = require("../database/schemas");
const azulClient_1 = require("./azulClient");
const crypto_1 = tslib_1.__importDefault(require("crypto"));
class AzulDeletionRetryService {
    constructor(environment = 'sandbox') {
        this.processingInterval = null;
        this.isProcessing = false;
        // Configurable settings
        this.DEFAULT_MAX_ATTEMPTS = 5;
        this.DEFAULT_BASE_DELAY_MS = 5000; // 5 seconds
        this.MAX_DELAY_MS = 300000; // 5 minutes max
        this.PROCESSING_INTERVAL_MS = 30000; // Check every 30 seconds
        this.ENCRYPTION_KEY = process.env.AZUL_TOKEN_ENCRYPTION_KEY || 'default-key-change-in-production';
        this.azulClient = new azulClient_1.AzulClient(environment);
    }
    /**
     * Encrypt sensitive token data for storage
     */
    encryptToken(token) {
        try {
            const algorithm = 'aes-256-gcm';
            const key = crypto_1.default.scryptSync(this.ENCRYPTION_KEY, 'salt', 32);
            const iv = crypto_1.default.randomBytes(16);
            const cipher = crypto_1.default.createCipheriv(algorithm, key, iv);
            cipher.setAAD(Buffer.from('azul-token', 'utf8'));
            let encrypted = cipher.update(token, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            const authTag = cipher.getAuthTag();
            return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
        }
        catch (error) {
            console.error('Error encrypting token:', error);
            // Fallback to base64 encoding if encryption fails (not secure, but better than plaintext)
            return Buffer.from(token).toString('base64');
        }
    }
    /**
     * Decrypt token data for use
     */
    decryptToken(encryptedToken) {
        try {
            const algorithm = 'aes-256-gcm';
            const key = crypto_1.default.scryptSync(this.ENCRYPTION_KEY, 'salt', 32);
            const parts = encryptedToken.split(':');
            if (parts.length !== 3) {
                // Fallback for base64 encoded tokens
                return Buffer.from(encryptedToken, 'base64').toString('utf8');
            }
            const [ivHex, authTagHex, encrypted] = parts;
            const iv = Buffer.from(ivHex, 'hex');
            const authTag = Buffer.from(authTagHex, 'hex');
            const decipher = crypto_1.default.createDecipheriv(algorithm, key, iv);
            decipher.setAAD(Buffer.from('azul-token', 'utf8'));
            decipher.setAuthTag(authTag);
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            return decrypted;
        }
        catch (error) {
            console.error('Error decrypting token:', error);
            // Fallback for base64 encoded tokens
            try {
                return Buffer.from(encryptedToken, 'base64').toString('utf8');
            }
            catch (_a) {
                throw new Error('Unable to decrypt token');
            }
        }
    }
    /**
     * Add a new retry job for failed Azul deletion
     */
    enqueueRetry(options) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const maxAttempts = options.maxAttempts || this.DEFAULT_MAX_ATTEMPTS;
                const baseDelayMs = options.baseDelayMs || this.DEFAULT_BASE_DELAY_MS;
                const nextRetryAt = new Date(Date.now() + baseDelayMs);
                const encryptedToken = this.encryptToken(options.dataVaultToken);
                // Use upsert to handle potential race conditions
                yield schemas_1.AzulDeletionRetryJob.findOneAndUpdate({ paymentMethodId: options.paymentMethodId, status: { $in: ['pending', 'processing'] } }, {
                    $setOnInsert: {
                        paymentMethodId: options.paymentMethodId,
                        dataVaultToken: encryptedToken,
                        userId: options.userId,
                        attemptCount: 0,
                        maxAttempts,
                        baseDelayMs,
                        nextRetryAt,
                        status: 'pending',
                        metadata: options.metadata || {}
                    }
                }, { upsert: true, new: true });
                console.log('Enqueued Azul deletion retry job', {
                    paymentMethodId: options.paymentMethodId,
                    userId: options.userId,
                    maxAttempts,
                    nextRetryAt: nextRetryAt.toISOString()
                });
            }
            catch (error) {
                console.error('Failed to enqueue Azul deletion retry job:', error);
                throw error;
            }
        });
    }
    /**
     * Process pending retry jobs
     */
    processRetryJobs() {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            if (this.isProcessing) {
                return; // Prevent concurrent processing
            }
            this.isProcessing = true;
            try {
                const now = new Date();
                // Find jobs ready for retry
                const pendingJobs = yield schemas_1.AzulDeletionRetryJob.find({
                    status: 'pending',
                    nextRetryAt: { $lte: now }
                }).select('+dataVaultToken').limit(10); // Process in batches
                for (const job of pendingJobs) {
                    yield this.processRetryJob(job);
                }
            }
            catch (error) {
                console.error('Error processing retry jobs:', error);
            }
            finally {
                this.isProcessing = false;
            }
        });
    }
    /**
     * Process a single retry job
     */
    processRetryJob(job) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                // Mark job as processing
                job.status = 'processing';
                yield job.save();
                // Decrypt token for API call
                const dataVaultToken = this.decryptToken(job.dataVaultToken);
                // Attempt Azul deletion
                const azulResponse = yield this.azulClient.deleteDataVaultToken(dataVaultToken);
                const validation = azulClient_1.AzulClient.validateResponse(azulResponse);
                if (validation.isSuccess) {
                    // Success - mark job as succeeded
                    job.status = 'succeeded';
                    job.completedAt = new Date();
                    yield job.save();
                    console.log('Azul deletion retry succeeded', {
                        jobId: String(job._id),
                        paymentMethodId: job.paymentMethodId,
                        attemptCount: job.attemptCount + 1
                    });
                }
                else {
                    // Failed - handle retry or permanent failure
                    yield this.handleRetryFailure(job, validation.errorMessage || 'Unknown error');
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                yield this.handleRetryFailure(job, errorMessage);
            }
        });
    }
    /**
     * Handle retry failure - either schedule next retry or mark as permanently failed
     */
    handleRetryFailure(job, errorMessage) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            job.attemptCount += 1;
            job.lastError = errorMessage;
            // Add to error history
            if (!job.metadata)
                job.metadata = {};
            if (!job.metadata.errorHistory)
                job.metadata.errorHistory = [];
            job.metadata.errorHistory.push({
                attempt: job.attemptCount,
                error: errorMessage,
                timestamp: new Date()
            });
            if (job.attemptCount >= job.maxAttempts) {
                // Permanent failure
                job.status = 'failed';
                job.completedAt = new Date();
                console.error('Azul deletion permanently failed after max attempts', {
                    jobId: String(job._id),
                    paymentMethodId: job.paymentMethodId,
                    userId: job.userId,
                    attemptCount: job.attemptCount,
                    maxAttempts: job.maxAttempts,
                    lastError: errorMessage
                });
                // TODO: Send alert/notification to operations team
                // This could be integrated with existing alerting systems
            }
            else {
                // Schedule next retry with exponential backoff
                const backoffDelay = Math.min(job.baseDelayMs * Math.pow(2, job.attemptCount - 1), this.MAX_DELAY_MS);
                job.nextRetryAt = new Date(Date.now() + backoffDelay);
                job.status = 'pending';
                console.warn('Azul deletion retry failed, scheduling next attempt', {
                    jobId: String(job._id),
                    paymentMethodId: job.paymentMethodId,
                    attemptCount: job.attemptCount,
                    nextRetryAt: job.nextRetryAt.toISOString(),
                    error: errorMessage
                });
            }
            yield job.save();
        });
    }
    /**
     * Start the retry processor (runs periodically)
     */
    startProcessor() {
        if (this.processingInterval) {
            return; // Already started
        }
        console.log('Starting Azul deletion retry processor');
        // Process immediately and then on interval
        this.processRetryJobs();
        this.processingInterval = setInterval(() => {
            this.processRetryJobs();
        }, this.PROCESSING_INTERVAL_MS);
    }
    /**
     * Stop the retry processor
     */
    stopProcessor() {
        if (this.processingInterval) {
            clearInterval(this.processingInterval);
            this.processingInterval = null;
            console.log('Stopped Azul deletion retry processor');
        }
    }
    /**
     * Get retry job statistics
     */
    getRetryStats() {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const stats = yield schemas_1.AzulDeletionRetryJob.aggregate([
                {
                    $group: {
                        _id: '$status',
                        count: { $sum: 1 }
                    }
                }
            ]);
            const result = {
                pending: 0,
                processing: 0,
                succeeded: 0,
                failed: 0
            };
            stats.forEach(stat => {
                if (stat._id in result) {
                    result[stat._id] = stat.count;
                }
            });
            return result;
        });
    }
}
exports.AzulDeletionRetryService = AzulDeletionRetryService;
// Global instance - can be configured as singleton
let retryServiceInstance = null;
function getAzulDeletionRetryService(environment) {
    if (!retryServiceInstance) {
        const env = environment || (process.env.NODE_ENV === 'production' ? 'production' : 'sandbox');
        retryServiceInstance = new AzulDeletionRetryService(env);
    }
    return retryServiceInstance;
}
