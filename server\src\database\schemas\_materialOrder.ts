import mongoose, { Document, Schema } from 'mongoose';

export type MaterialOrderStatus = 'pending' | 'approved' | 'rejected' | 'delivered' | 'cancelled';

export interface IMaterialItem {
  sku: string;
  name: string;
  quantity: number;
  unitPrice: number;  // integer minor units (cents)
}

export interface IMaterialOrder extends Document {
  jobId: string;
  ownerId: string; // client account id
  operatorId: string; // operator account id
  items: IMaterialItem[];
  subtotal: number; // sum of item quantity * unitPrice
  deliveryFee?: number;
  total: number; // subtotal + deliveryFee
  currency: 'DOP' | 'USD';
  status: MaterialOrderStatus;
  deliveryLocation?: {
    latitude: number;
    longitude: number;
  } | null;
  createdAt: Date;
  updatedAt: Date;
}

const materialOrderSchema = new mongoose.Schema<IMaterialOrder>({
  jobId: { type: String, required: true, ref: 'Job', index: true },
  ownerId: { type: String, required: true, ref: 'Account', index: true },
  operatorId: { type: String, required: true, ref: 'Account', index: true },
  items: {
    type: [
      {
        sku: { type: String, required: true },
        name: { type: String, required: true },
        quantity: {
          type: Number, required: true, min: 1,
          validate: {
            validator: Number.isInteger,
            message: 'quantity must be an integer'
          }
        },
        unitPrice: {
          type: Number, required: true, min: 0,
          validate: {
            validator: Number.isInteger,
            message: 'unitPrice must be integer minor units (cents)'
          }
        }
      }
    ],
    validate: {
      validator: (v: unknown[]) => Array.isArray(v) && v.length > 0,
      message: 'At least one item is required',
    },
  },

  subtotal: {
    type: Number, required: true, min: 0,
    validate: {
      validator: Number.isInteger,
      message: 'subtotal must be integer minor units (cents)'
    }
  },
  deliveryFee: {
    type: Number, default: 0, min: 0,
    validate: {
      validator: Number.isInteger,
      message: 'deliveryFee must be integer minor units (cents)'
    }
  },
  total: {
    type: Number, required: true, min: 0,
    validate: {
      validator: Number.isInteger,
      message: 'total must be integer minor units (cents)'
    }
  },
  currency: { type: String, enum: ['DOP', 'USD'], default: 'DOP' },
  status: { type: String, enum: ['pending', 'approved', 'rejected', 'delivered', 'cancelled'], default: 'pending', index: true },
  deliveryLocation: {
    type: new Schema({
      latitude: { type: Number, required: true, min: -90, max: 90 },
      longitude: { type: Number, required: true, min: -180, max: 180 },
    }),
    required: false,
    default: null,
  },
}, { timestamps: true });

materialOrderSchema.index({ operatorId: 1, status: 1 });

export default mongoose.model<IMaterialOrder>('MaterialOrder', materialOrderSchema);
