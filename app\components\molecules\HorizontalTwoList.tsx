import { StyleSheet, Text, View } from 'react-native'
import React from 'react'



interface HorizontalTwoProps {
    children: React.ReactNode,
    padding?: number,
    title?: string
}

const HorizontalTwoList = ({ padding, children, title }: HorizontalTwoProps) => {
    return (
        <View style={{flexDirection: 'column', width: '100%', marginTop: 30}}>
            <Text style={{ fontFamily: 'Montserrat', fontSize: 18, fontWeight: 500, paddingHorizontal: padding ?? 15, marginBottom: 10}}>{title}</Text>
            <View style={{ flexDirection: 'row', paddingHorizontal: padding ?? 15, height: 'auto', justifyContent: 'space-between' }}>
                {children}
            </View>
        </View>
    )
}

export default HorizontalTwoList