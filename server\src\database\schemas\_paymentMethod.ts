import mongoose, { Document, Schema } from 'mongoose';

export interface IPaymentMethod extends Document {
  id: string;
  userId: mongoose.Types.ObjectId; // Reference to Account
  provider: 'azul';
  dataVaultToken: string; // Azul token (DataVaultToken)
  last4: string;
  brand: string; // e.g., 'visa', 'mastercard', 'amex'
  exp_month: number;
  exp_year: number;
  has_cvv: boolean; // whether token was created with CVV (Azul returns HasCVV)
  is_favorite: boolean;
  metadata?: {
    azulOrderId?: string;
    notes?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const paymentMethodSchema = new mongoose.Schema<IPaymentMethod>({
  userId: {
  type: Schema.Types.ObjectId,
    required: true,
    ref: 'Account'
  },
  provider: {
    type: String,
    enum: ['azul'],
    default: 'azul',
    required: true
  },
  dataVaultToken: {
    type: String,
    required: true,
    unique: true, // Azul tokens should be unique
    select: false,
    immutable: true
  },
  last4: {
    type: String,
    required: true,
    match: /^\d{4}$/ // exactly 4 digits
  },
  brand: {
    type: String,
    required: true,
    enum: ['visa', 'mastercard', 'amex', 'discover', 'diners', 'jcb', 'other']
  },
  exp_month: {
    type: Number,
    required: true,
    min: 1,
    max: 12
  },
  exp_year: {
    type: Number,
    required: true,
    validate: {
      validator: (value: number) => {
        // Validate against the current year at validation time so the schema
        // doesn't freeze the minimum year at server start.
        return typeof value === 'number' && value >= new Date().getFullYear();
      },
      message: (props: any) => `Expiration year ${props.value} cannot be in the past`
    }
  },
  has_cvv: {
    type: Boolean,
    default: false
  },
  is_favorite: {
    type: Boolean,
    default: false
  },
  metadata: {
    azulOrderId: {
      type: String
    },
    notes: {
      type: String,
      maxlength: 500
    }
  }
}, {
  timestamps: true
});

// Indexes for efficient querying
paymentMethodSchema.index(
  { userId: 1, is_favorite: 1 },
  { unique: true, partialFilterExpression: { is_favorite: true } }
);
paymentMethodSchema.index({ userId: 1, createdAt: -1 });

// Ensure only one favorite payment method per user
paymentMethodSchema.pre<IPaymentMethod>('save', async function(next) {
  if (this.is_favorite && this.isModified('is_favorite')) {
    // If this payment method is being set as favorite, unset all others for this user
    const session = await mongoose.startSession();
    try {
      await session.withTransaction(async () => {
        await mongoose.model('PaymentMethod').updateMany(
          { userId: this.userId, _id: { $ne: this._id } },
          { $set: { is_favorite: false } },
          { session }
        );
      });
    } catch (err) {
      return next(err as any);
    } finally {
      await session.endSession();
    }
  }

  next();
});

export default mongoose.model<IPaymentMethod>('PaymentMethod', paymentMethodSchema);
