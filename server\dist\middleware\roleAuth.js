"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireAdminOrOperator = exports.requireOperator = exports.requireAdmin = exports.requireRoles = void 0;
const auth_1 = require("../utils/auth");
/**
 * Generic middleware factory to require specific roles
 */
const requireRoles = (allowedRoles) => {
    return (req, res, next) => {
        try {
            const user = (0, auth_1.getAuthenticatedUser)(req);
            if (!user) {
                return res.status(401).json({ success: false, error: 'Authentication required' });
            }
            if (!allowedRoles.includes(user.role)) {
                const msg = `${allowedRoles.join(' or ')} access required`;
                return res.status(403).json({ success: false, error: msg });
            }
            next();
        }
        catch (error) {
            return next(error);
        }
    };
};
exports.requireRoles = requireRoles;
/**
 * Middleware to require admin access
 */
exports.requireAdmin = (0, exports.requireRoles)(['admin']);
/**
 * Middleware to require operator access
 */
exports.requireOperator = (0, exports.requireRoles)(['operator']);
/**
 * Middleware to require admin or operator access
 */
exports.requireAdminOrOperator = (0, exports.requireRoles)(['admin', 'operator']);
