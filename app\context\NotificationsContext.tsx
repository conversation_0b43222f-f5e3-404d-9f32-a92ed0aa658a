
import React, { createContext, useState, useEffect, useContext, useRef } from 'react';
import { Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import * as SecureStore from "expo-secure-store";
import { saveNotificationToken } from '@/services/api';


interface NotificationsContextProps {
  initializeNotifications: () => Promise<void>
}

export const NotificationsContext = createContext<NotificationsContextProps>({
  initializeNotifications: async () => { }
});
export const useNotifications = () => {
  return useContext(NotificationsContext) as NotificationsContextProps
}

// Configure how notifications are handled when app is foregrounded
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldPlaySound: true,
    shouldSetBadge: false,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});



export const NotificationsProvider = ({ children }: { children: React.ReactNode }) => {

  // Hooks for push notifications
  const notificationListener = useRef<Notifications.Subscription | null>(null);
  const responseListener = useRef<Notifications.Subscription | null>(null);


  useEffect(() => {


    return () => {
      notificationListener.current &&
        notificationListener.current.remove();
      responseListener.current &&
        responseListener.current.remove()
    };
  }, []);

  const initializeNotifications = async () => {
    resetBadgeCount()

    if (Platform.OS === 'ios') {
      await registerForPushNotificationsAsync()

      notificationListener.current =
        Notifications.addNotificationReceivedListener(async notification => {
          console.log('Notification received:', notification);
          
        });

      responseListener.current =
        Notifications.addNotificationResponseReceivedListener(response => {
          console.log('Notification response:', response);
        });
    }
    if (Platform.OS === 'android') {
      // Register for push notifications on both platforms
      await registerForPushNotificationsAsync()

      // Set up notification listeners (for both iOS and Android)
      notificationListener.current =
        Notifications.addNotificationReceivedListener(async notification => {
          //console.log('Notification received:', notification);
          
        });

      responseListener.current =
        Notifications.addNotificationResponseReceivedListener(response => {
          //console.log('Notification response:', response);
        });

      Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }
  }
  const registerForPushNotificationsAsync = async (): Promise<string | null> => {
    try {
      // 1. Ask for permission
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Push notifications permission not granted');
        return null;
      }

      // 2. Get the Expo push token
      const { data: token } = await Notifications.getExpoPushTokenAsync();
      const saveToken = await saveNotificationToken(token);
      //console.log('Expo Push Token:', token);
      return token;
    } catch (error) {
      //console.error('Error getting push token:', error);
      return null;
    }
  }




  async function resetBadgeCount() {
    await Notifications.setBadgeCountAsync(0);
    
    //Also change the state of notifications in the backend                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               
  }

  return (
    <NotificationsContext.Provider value={{ initializeNotifications }}>
      {children}
    </NotificationsContext.Provider>
  );
};