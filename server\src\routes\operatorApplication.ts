import express from 'express';
import multer from 'multer';
import { tryCatch } from '../middleware';
import { AuthenticateTokenOAuth } from '../middleware/authentication';
import { requireAdmin } from '../middleware/roleAuth';
import { submitOperatorApplication, approveOperatorApplication, rejectOperatorApplication, listOperatorApplications, getApplicationDocumentRequirements } from '../controllers/operatorApplication';

const router = express.Router();
const upload = multer({ storage: multer.memoryStorage(), limits: { fileSize: 10 * 1024 * 1024, files: 12 } });


// User submits application
router.post(
  '/apply',
  AuthenticateTokenOAuth,
  // accept multiple files under field name 'documents' and optional body 'documentsMeta'
  upload.array('documents', 12),
  tryCatch(submitOperatorApplication)
);

// Public: return required document categories for a set of categories
router.get('/document-requirements', tryCatch(getApplicationDocumentRequirements));

// Admin lists applications
router.get('/applications', AuthenticateTokenOAuth, requireAdmin, tryCatch(listOperatorApplications));

// Admin reviews
router.post('/applications/:applicationId/approve', AuthenticateTokenOAuth, requireAdmin, tryCatch(approveOperatorApplication));
router.post('/applications/:applicationId/reject', AuthenticateTokenOAuth, requireAdmin, tryCatch(rejectOperatorApplication));

export default router;