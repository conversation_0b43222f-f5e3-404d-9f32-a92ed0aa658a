import express from 'express';
import { AuthenticateTokenOAuth, requireAdmin } from '../middleware';
import { tryCatch } from '../middleware';
import {
  getActiveCategories,
  getAllCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  getDocumentRequirementCategories,
  getCategoryDocumentRequirements,
  updateCategoryDocumentRequirements,
} from '../controllers/jobCategory';

const router = express.Router();

// Public endpoint - get active categories
router.get('/active', tryCatch(getActiveCategories));
// Public endpoint - get supported document requirement categories (master list)
router.get('/document-requirements', tryCatch(getDocumentRequirementCategories));

// Admin endpoints
router.get('/', AuthenticateTokenOAuth, requireAdmin, tryCatch(getAllCategories));
router.post('/', AuthenticateTokenOAuth, requireAdmin, tryCatch(createCategory));
router.put('/:categoryId', AuthenticateTokenOAuth, requireAdmin, tryCatch(updateCategory));
router.delete('/:categoryId', AuthenticateTokenOAuth, requireAdmin, tryCatch(deleteCategory));
router.get('/:categoryId/document-requirements', AuthenticateTokenOAuth, requireAdmin, tryCatch(getCategoryDocumentRequirements));
router.put('/:categoryId/document-requirements', AuthenticateTokenOAuth, requireAdmin, tryCatch(updateCategoryDocumentRequirements));

export default router;
