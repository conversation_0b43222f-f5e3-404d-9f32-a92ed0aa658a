"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const schemas_1 = require("../database/schemas");
class ValidationError extends Error {
    constructor(message) {
        super(message);
        this.name = 'ValidationError';
    }
}
class JobCategoryService {
    // Normalize a category name consistently across the service
    normalizeName(name) {
        const raw = String(name || '');
        const trimmed = raw.trim().toLowerCase();
        if (!trimmed)
            return undefined;
        // Strip accents, collapse non-alphanumerics to underscores, and trim underscores
        const ascii = trimmed.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
        const normalized = ascii.replace(/[^a-z0-9]+/g, '_').replace(/^_+|_+$/g, '');
        return normalized || undefined;
    }
    /**
     * Get all active job categories
     */
    getActiveCategories() {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            return yield schemas_1.JobCategory.find({ isActive: true }).sort({ order: 1, displayName: 1 });
        });
    }
    /**
     * Get all job categories (including inactive ones) - for admin
     */
    getAllCategories() {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            return yield schemas_1.JobCategory.find().sort({ order: 1, displayName: 1 });
        });
    }
    /** Get category by name */
    getCategoryByName(name) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const normalized = this.normalizeName(name);
            if (!normalized)
                return null;
            return yield schemas_1.JobCategory.findOne({ name: normalized });
        });
    }
    /**
     * Check if a category exists and is active
     */
    isCategoryValid(name) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const normalized = this.normalizeName(name);
            if (!normalized)
                return false;
            const category = yield schemas_1.JobCategory.findOne({ name: normalized, isActive: true });
            return !!category;
        });
    }
    /**
     * Create a new job category
     */
    createCategory(categoryData) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const normalizedName = this.normalizeName(categoryData.name);
            if (!normalizedName) {
                throw new Error('Category name is required');
            }
            const existingCategory = yield schemas_1.JobCategory.findOne({ name: normalizedName });
            if (existingCategory) {
                throw new Error(`Category with name '${normalizedName}' already exists`);
            }
            const { displayName, description, order, documents_required } = categoryData;
            const category = new schemas_1.JobCategory({
                name: normalizedName,
                displayName,
                description,
                order,
                documents_required,
            });
            return yield category.save();
        });
    }
    /**
     * Update job category
     */
    updateCategory(id, updateData) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            // Build a whitelist-only update object to avoid mass-assignment
            const toUpdate = {};
            // name
            if ('name' in updateData) {
                if (typeof updateData.name !== 'string') {
                    throw new ValidationError('Category name must be a string');
                }
                const normalized = this.normalizeName(updateData.name);
                if (!normalized) {
                    throw new ValidationError('Category name is required');
                }
                // Use normalized for uniqueness check and assignment
                const existingCategory = yield schemas_1.JobCategory.findOne({ name: normalized, _id: { $ne: id } });
                if (existingCategory) {
                    throw new Error(`Category with name '${normalized}' already exists`);
                }
                toUpdate.name = normalized;
            }
            // displayName
            if ('displayName' in updateData) {
                if (typeof updateData.displayName !== 'string') {
                    throw new ValidationError('displayName must be a string');
                }
                const displayName = updateData.displayName.trim();
                if (!displayName) {
                    throw new ValidationError('displayName cannot be empty');
                }
                toUpdate.displayName = displayName;
            }
            // description (optional)
            if ('description' in updateData) {
                if (typeof updateData.description !== 'string' && typeof updateData.description !== 'undefined') {
                    throw new ValidationError('description must be a string');
                }
                if (typeof updateData.description === 'string') {
                    toUpdate.description = updateData.description;
                }
            }
            // isActive
            if ('isActive' in updateData) {
                if (typeof updateData.isActive !== 'boolean') {
                    throw new ValidationError('isActive must be a boolean');
                }
                toUpdate.isActive = updateData.isActive;
            }
            // order
            if ('order' in updateData) {
                if (typeof updateData.order !== 'number' || !Number.isFinite(updateData.order)) {
                    throw new ValidationError('order must be a finite number');
                }
                toUpdate.order = updateData.order;
            }
            // Protected/ignored fields are never copied: _id, createdAt, updatedAt, isSystem, etc.
            // If nothing to update, short-circuit
            if (Object.keys(toUpdate).length === 0) {
                return yield schemas_1.JobCategory.findById(id);
            }
            try {
                return yield schemas_1.JobCategory.findByIdAndUpdate(id, toUpdate, { new: true, runValidators: true });
            }
            catch (err) {
                // Handle duplicate key race condition (unique index on name)
                if (err && (err.code === 11000 || (typeof err.message === 'string' && err.message.includes('E11000')))) {
                    const attemptedName = toUpdate.name || 'unknown';
                    throw new ValidationError(`Category with name '${attemptedName}' already exists`);
                }
                if (err && err.name === 'CastError') {
                    return null;
                }
                throw err;
            }
        });
    }
    /**
     * Delete job category (hard delete)
     */
    deleteCategory(id) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const result = yield schemas_1.JobCategory.findByIdAndDelete(id);
            return !!result;
        });
    }
    /**
     * Get category names for system prompt (used in chat)
     */
    getCategoryNamesForPrompt() {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const categories = yield this.getActiveCategories();
            return categories.map(cat => `"${cat.name}"`).join(', ');
        });
    }
    /**
     * Initialize default categories if none exist
     */
    initializeDefaultCategories() {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const count = yield schemas_1.JobCategory.countDocuments();
            if (count === 0) {
                const defaultCategories = [
                    { name: 'plumbing', displayName: 'Plomería', description: 'Servicios de plomería y fontanería', order: 1 },
                    { name: 'electrician', displayName: 'Electricidad', description: 'Servicios eléctricos y cableado', order: 2 },
                    { name: 'carpentry', displayName: 'Carpintería', description: 'Trabajos en madera y carpintería', order: 3 },
                    { name: 'painting', displayName: 'Pintura', description: 'Servicios de pintura y decoración', order: 4 },
                    { name: 'cleaning', displayName: 'Limpieza', description: 'Servicios de limpieza especializada', order: 5 },
                    { name: 'other', displayName: 'Otros', description: 'Otros servicios del hogar', order: 6 }
                ];
                const ops = defaultCategories.map(c => ({
                    updateOne: {
                        filter: { name: c.name },
                        update: { $setOnInsert: c },
                        upsert: true,
                    },
                }));
                yield schemas_1.JobCategory.bulkWrite(ops, { ordered: false });
                console.log('Default job categories initialized (idempotent)');
            }
        });
    }
}
exports.default = new JobCategoryService();
