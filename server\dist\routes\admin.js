"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const express_1 = tslib_1.__importDefault(require("express"));
const middleware_1 = require("../middleware");
const middleware_2 = require("../middleware");
const admin_1 = require("../controllers/admin");
const router = express_1.default.Router();
// Apply authentication and admin requirement to all routes
router.use(middleware_1.AuthenticateTokenOAuth);
router.use(middleware_1.requireAdmin);
// System stats
router.get('/stats', (0, middleware_2.tryCatch)(admin_1.getSystemStats));
// User management
router.get('/users', (0, middleware_2.tryCatch)(admin_1.getAllUsers));
router.post('/users', (0, middleware_2.tryCatch)(admin_1.createUser));
router.put('/users/:userId', middleware_1.validateUserId, (0, middleware_2.tryCatch)(admin_1.updateUser));
router.delete('/users/:userId', middleware_1.validateUserId, (0, middleware_2.tryCatch)(admin_1.deleteUser));
// Operator management
router.get('/operators', (0, middleware_2.tryCatch)(admin_1.getAllOperators));
router.post('/operators', (0, middleware_2.tryCatch)(admin_1.createOperator));
router.put('/operators/:operatorId', middleware_1.validateOperatorId, (0, middleware_2.tryCatch)(admin_1.updateOperator));
router.delete('/operators/:operatorId', middleware_1.validateOperatorId, (0, middleware_2.tryCatch)(admin_1.deleteOperator));
exports.default = router;
