import LottieView from 'lottie-react-native'
import React from 'react'
import { StyleSheet, View } from 'react-native'

const CheckLoader = () => {

  return (
    <View style={styles.viewport}>
      <LottieView 
        source={require('@/assets/animations/check.json')}
        style={{ height: 280, aspectRatio: 1}}
        speed={1}
        autoPlay
        //loop
        />
    </View>
  )
}

export default CheckLoader


const styles = StyleSheet.create({
  viewport: {

    zIndex: 100,
    width: '100%',
    height: 200,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    },

})