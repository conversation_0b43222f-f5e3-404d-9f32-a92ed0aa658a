import LottieView from 'lottie-react-native'
import React from 'react'
import { StyleSheet, View } from 'react-native'

const WrenchLoader = () => {

  return (
    <View style={styles.viewport}>
      <LottieView 
        source={require('@/assets/animations/wrench.json')}
        style={{ height: 180, aspectRatio: 1}}
        speed={1}
        autoPlay
        loop
        />
    </View>
  )
}

export default WrenchLoader


const styles = StyleSheet.create({
  viewport: {

    zIndex: 100,
    width: 100,
    height: 200,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    },

})