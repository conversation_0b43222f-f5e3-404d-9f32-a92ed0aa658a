"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const documentRequirements_1 = require("../../types/documentRequirements");
const jobCategorySchema = new mongoose_1.default.Schema({
    name: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true,
        match: /^[a-z_]+$/ // Only lowercase letters and underscores
    },
    displayName: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    isActive: {
        type: Boolean,
        default: true
    },
    order: {
        type: Number,
        default: 0
    },
    documents_required: {
        type: [String],
        default: ['none'],
        validate: {
            validator: function (arr) {
                if (!Array.isArray(arr))
                    return false;
                return arr.every((v) => documentRequirements_1.DOCUMENT_REQUIREMENT_CATEGORIES.includes(String(v)));
            },
            message: 'Invalid document requirement category provided in job category',
        },
    }
}, {
    timestamps: true
});
// Index for efficient querying 
jobCategorySchema.index({ isActive: 1, order: 1 });
const JobCategory = mongoose_1.default.models.JobCategory ||
    mongoose_1.default.model('JobCategory', jobCategorySchema);
exports.default = JobCategory;
