"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeAzulRetryProcessor = initializeAzulRetryProcessor;
exports.getRetryProcessorStats = getRetryProcessorStats;
const tslib_1 = require("tslib");
const azulDeletionRetryService_1 = require("../services/azulDeletionRetryService");
/**
 * Initialize and start the Azul deletion retry processor
 * Call this during application startup
 */
function initializeAzulRetryProcessor() {
    try {
        const environment = process.env.NODE_ENV === 'production' ? 'production' : 'sandbox';
        const retryService = (0, azulDeletionRetryService_1.getAzulDeletionRetryService)(environment);
        // Start the background processor
        retryService.startProcessor();
        console.log('Azul deletion retry processor initialized');
        // Handle graceful shutdown
        const gracefulShutdown = () => {
            console.log('Shutting down Azul deletion retry processor...');
            retryService.stopProcessor();
        };
        process.on('SIGTERM', gracefulShutdown);
        process.on('SIGINT', gracefulShutdown);
    }
    catch (error) {
        console.error('Failed to initialize Azul deletion retry processor:', error);
    }
}
/**
 * Get retry statistics for monitoring/admin endpoints
 */
function getRetryProcessorStats() {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        try {
            const environment = process.env.NODE_ENV === 'production' ? 'production' : 'sandbox';
            const retryService = (0, azulDeletionRetryService_1.getAzulDeletionRetryService)(environment);
            return yield retryService.getRetryStats();
        }
        catch (error) {
            console.error('Failed to get retry processor stats:', error);
            return {
                pending: 0,
                processing: 0,
                succeeded: 0,
                failed: 0
            };
        }
    });
}
