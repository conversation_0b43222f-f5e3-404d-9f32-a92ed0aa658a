import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { SplashScreen, Stack } from 'expo-router';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';
import { AuthProvider } from '@/context/AuthContext';
import { StatusBarExpo } from '@/components/templates/Statusbar';
import { NotificationsProvider } from '@/context/NotificationsContext';
import { ChatProvider } from '@/context/ChatContext';
import { BottomSheetProvider } from '@/context/BottomSheetContext';
import { useEffect } from 'react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import Toast from 'react-native-toast-message';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';

export default function RootLayout() {
  const colorScheme = useColorScheme();

  const [loaded] = useFonts({
    Montserrat: require('@/assets/fonts/Montserrat.ttf')
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);


  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <NotificationsProvider>
        <AuthProvider>
          <ChatProvider>
            <GestureHandlerRootView>



              <BottomSheetProvider>
                <Stack screenOptions={{ headerShown: false }}>
                  <Stack.Screen name="(home)" options={{ headerShown: false }} />
                  <Stack.Screen name="index" options={{ headerShown: false }} />
                  <Stack.Screen name="login" options={{ headerShown: false, animation: 'fade' }} />
                  <Stack.Screen name="+not-found" />
                </Stack>
                <StatusBarExpo style="dark" />
              </BottomSheetProvider>


            </GestureHandlerRootView>
          </ChatProvider>
        </AuthProvider>
      </NotificationsProvider>

      <Toast
        position='bottom'
        bottomOffset={30}
      />
    </ThemeProvider>
  );
}
