import { createAudioPlayer } from 'expo-audio';


export const LoginChime = async () => {
  try {
    const audioSource = require('../assets/sounds/sound-4.mp3');
    const player = createAudioPlayer(audioSource);

    player.play();

    // Emulate an async wait (e.g., 800ms)
    await new Promise(resolve => setTimeout(resolve, 70));
  } catch (error) {
    console.error('Error playing login chime:', error);
  }
};
