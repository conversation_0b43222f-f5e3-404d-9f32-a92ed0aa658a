"use strict";
/**
 * Comprehensive card brand detection utility
 * Based on industry-standard BIN (Bank Identification Number) ranges
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCardBrand = getCardBrand;
/**
 * Detect card brand from card number using comprehensive BIN range validation
 * @param cardNumber - Card number (can include spaces, dashes, etc.)
 * @returns Card brand as lowercase string
 */
function getCardBrand(cardNumber) {
    const cleaned = (cardNumber || '').replace(/\D/g, '');
    // Insufficient digits to determine brand
    if (cleaned.length < 4) {
        return 'other';
    }
    const p1 = cleaned.slice(0, 1);
    const p2 = cleaned.slice(0, 2);
    const p3 = cleaned.slice(0, 3);
    const p4 = cleaned.slice(0, 4);
    const p6 = cleaned.slice(0, 6);
    const numP2 = parseInt(p2, 10);
    const numP3 = parseInt(p3, 10);
    const numP4 = parseInt(p4, 10);
    const numP6 = parseInt(p6, 10);
    // Discover: 6011, 65, 644-649, 622126-622925
    if (p4 === '6011' || p2 === '65' || (numP3 >= 644 && numP3 <= 649) || (numP6 >= 622126 && numP6 <= 622925)) {
        return 'discover';
    }
    // Mastercard: 51-55, 2221-2720
    if ((numP2 >= 51 && numP2 <= 55) || (numP4 >= 2221 && numP4 <= 2720)) {
        return 'mastercard';
    }
    // JCB: 3528-3589
    if (numP4 >= 3528 && numP4 <= 3589) {
        return 'jcb';
    }
    // Diners Club: 300-305, 36, 38-39
    if ((numP3 >= 300 && numP3 <= 305) || p2 === '36' || (numP2 >= 38 && numP2 <= 39)) {
        return 'diners';
    }
    // American Express: 34, 37
    if (p2 === '34' || p2 === '37') {
        return 'amex';
    }
    // Visa: starts with 4
    if (p1 === '4') {
        return 'visa';
    }
    return 'other';
}
