import React, { useRef, useEffect } from 'react';
import { Image, ScrollView, StyleSheet, Text, View } from 'react-native';

import Page from '@/components/templates/Page';
import Header from '@/components/templates/Header';
import Map from '@/components/templates/Map';

const Index = () => {


  return (

    <Page noPaddingTop noBottomBar alignItems="center" justifyContent="space-between" page="home">
      <Header buttonBack text=' ' />
      <Map />
    </Page>

  );
};

export default Index;

const styles = StyleSheet.create({
  scrollView: {
    paddingTop: 100
  },
  title: {
    fontFamily: 'Montserrat',
    fontSize: 18,
    fontWeight: 700,
    color: '#000'
  },
  subtitle: {
    marginTop: 8,
    fontFamily: 'Montserrat',
    fontSize: 14,
    fontWeight: 400,

    textAlign: 'center',
    paddingHorizontal: 40,
    lineHeight: 20,
    color: '#000000db'
  },
});