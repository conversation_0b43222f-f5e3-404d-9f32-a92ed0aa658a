"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationError = exports.RatingUpdateError = exports.CancellationTimeoutError = exports.UnauthorizedCancellationError = exports.JobNotCancellableError = exports.PenaltyProcessingError = exports.WithdrawalProcessingError = exports.WithdrawalNotAllowedError = exports.InsufficientCreditBalanceError = exports.CancellationAlreadyProcessedError = exports.CancellationNotAllowedError = exports.InvalidCancellationError = exports.CancellationError = void 0;
class CancellationError extends Error {
    constructor(message, statusCode = 500, code, cause) {
        super(message);
        this.name = 'CancellationError';
        const ctor = (new.target || CancellationError);
        Object.setPrototypeOf(this, ctor.prototype);
        if (typeof Error.captureStackTrace === 'function') {
            Error.captureStackTrace(this, ctor);
        }
        this.statusCode = statusCode;
        this.code = code;
        if (cause !== undefined) {
            try {
                this.cause = cause;
            }
            catch (_a) {
                // ignore
            }
        }
    }
}
exports.CancellationError = CancellationError;
class InvalidCancellationError extends CancellationError {
    constructor(message = 'Invalid cancellation request') {
        super(message, 400, 'INVALID_CANCELLATION');
        this.name = 'InvalidCancellationError';
    }
}
exports.InvalidCancellationError = InvalidCancellationError;
class CancellationNotAllowedError extends CancellationError {
    constructor(message = 'Cancellation not allowed at this time') {
        super(message, 403, 'CANCELLATION_NOT_ALLOWED');
        this.name = 'CancellationNotAllowedError';
    }
}
exports.CancellationNotAllowedError = CancellationNotAllowedError;
class CancellationAlreadyProcessedError extends CancellationError {
    constructor(message = 'Cancellation has already been processed') {
        super(message, 409, 'CANCELLATION_ALREADY_PROCESSED');
        this.name = 'CancellationAlreadyProcessedError';
    }
}
exports.CancellationAlreadyProcessedError = CancellationAlreadyProcessedError;
class InsufficientCreditBalanceError extends CancellationError {
    constructor(message = 'Insufficient credit balance') {
        super(message, 400, 'INSUFFICIENT_CREDIT_BALANCE');
        this.name = 'InsufficientCreditBalanceError';
    }
}
exports.InsufficientCreditBalanceError = InsufficientCreditBalanceError;
class WithdrawalNotAllowedError extends CancellationError {
    constructor(message = 'Withdrawal not allowed') {
        super(message, 403, 'WITHDRAWAL_NOT_ALLOWED');
        this.name = 'WithdrawalNotAllowedError';
    }
}
exports.WithdrawalNotAllowedError = WithdrawalNotAllowedError;
class WithdrawalProcessingError extends CancellationError {
    constructor(message = 'Withdrawal processing failed') {
        super(message, 502, 'WITHDRAWAL_PROCESSING_ERROR');
        this.name = 'WithdrawalProcessingError';
    }
}
exports.WithdrawalProcessingError = WithdrawalProcessingError;
class PenaltyProcessingError extends CancellationError {
    constructor(message = 'Penalty processing failed') {
        super(message, 500, 'PENALTY_PROCESSING_ERROR');
        this.name = 'PenaltyProcessingError';
    }
}
exports.PenaltyProcessingError = PenaltyProcessingError;
class JobNotCancellableError extends CancellationError {
    constructor(message = 'Job cannot be cancelled in its current state') {
        super(message, 400, 'JOB_NOT_CANCELLABLE');
        this.name = 'JobNotCancellableError';
    }
}
exports.JobNotCancellableError = JobNotCancellableError;
class UnauthorizedCancellationError extends CancellationError {
    constructor(message = 'Not authorized to cancel this job') {
        super(message, 403, 'UNAUTHORIZED_CANCELLATION');
        this.name = 'UnauthorizedCancellationError';
    }
}
exports.UnauthorizedCancellationError = UnauthorizedCancellationError;
class CancellationTimeoutError extends CancellationError {
    constructor(message = 'Cancellation request timed out') {
        super(message, 408, 'CANCELLATION_TIMEOUT');
        this.name = 'CancellationTimeoutError';
    }
}
exports.CancellationTimeoutError = CancellationTimeoutError;
class RatingUpdateError extends CancellationError {
    constructor(message = 'Failed to update operator rating') {
        super(message, 500, 'RATING_UPDATE_ERROR');
        this.name = 'RatingUpdateError';
    }
}
exports.RatingUpdateError = RatingUpdateError;
class NotificationError extends CancellationError {
    constructor(message = 'Failed to send cancellation notification') {
        super(message, 500, 'NOTIFICATION_ERROR');
        this.name = 'NotificationError';
    }
}
exports.NotificationError = NotificationError;
exports.default = CancellationError;
