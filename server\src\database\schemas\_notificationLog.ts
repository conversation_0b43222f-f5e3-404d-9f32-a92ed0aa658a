import mongoose, { Document, Schema } from 'mongoose';

// Nnotification types
export const NOTIFICATION_TYPES = [
  'job_created',
  'bid_placed',
  'bid_accepted',
  'bid_rejected',
  'job_completed',
  'operator_request_submitted',
  'operator_request_approved',
  'operator_request_rejected',
  // New payment-related types
  'payment_escrowed',
  'refund_processed',
  'payout_released',
  'cancellation_penalty',
  'operator_no_show'
] as const;

export type NotificationType = typeof NOTIFICATION_TYPES[number];

export interface INotificationLog extends Document {
  recipientId: string; // Account ID of recipient
  type: NotificationType;
  title: string;
  message: string;
  data: any; // Additional data (jobId, bidId, etc.)
  jobId?: string; // Reference to related job
  bidId?: string; // Reference to related bid
  isRead: boolean;
  sentAt: Date;
  readAt?: Date;
}

const notificationLogSchema = new mongoose.Schema<INotificationLog>({
  recipientId: {
    type: String,
    required: true,
    ref: 'Account'
  },
  type: {
    type: String,
  enum: NOTIFICATION_TYPES,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  message: {
    type: String,
    required: true
  },
  data: {
    type: Schema.Types.Mixed,
    default: {}
  },
  jobId: {
    type: String,
    ref: 'Job'
  },
  bidId: {
    type: String,
    ref: 'Bid'
  },
  isRead: {
    type: Boolean,
    default: false
  },
  sentAt: {
    type: Date,
    default: Date.now
  },
  readAt: {
    type: Date
  }
}, {
  timestamps: true
});

// Indexes for efficient querying
notificationLogSchema.index({ recipientId: 1, sentAt: -1 });
notificationLogSchema.index({ recipientId: 1, isRead: 1 });
notificationLogSchema.index({ type: 1 });
notificationLogSchema.index({ jobId: 1 });

export default mongoose.model<INotificationLog>('NotificationLog', notificationLogSchema);
