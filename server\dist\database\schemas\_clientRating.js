"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const clientRatingSchema = new mongoose_1.default.Schema({
    clientId: { type: String, required: true, ref: 'Account', index: true },
    operatorId: { type: String, required: true, ref: 'Account', index: true },
    // jobId must exist but should not be unique by itself - uniqueness is enforced
    // per (jobId, operatorId) via a compound unique index below.
    jobId: { type: String, required: true, ref: 'Job' },
    stars: { type: Number, required: true, min: 1, max: 5 },
    adjectives: {
        positive: { type: [String], default: [] },
        negative: { type: [String], default: [] },
    },
}, { timestamps: true });
clientRatingSchema.index({ clientId: 1, createdAt: -1 });
// Enforce one rating per job per operator
clientRatingSchema.index({ jobId: 1, operatorId: 1 }, { unique: true });
exports.default = mongoose_1.default.model('ClientRating', clientRatingSchema);
