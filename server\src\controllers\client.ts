import { Request, Response } from 'express';
import { isValidObjectId } from 'mongoose';
import { ClientRating } from '../database/schemas';

export const getClientReputation = async (req: Request, res: Response) => {
  try {
    const { clientId } = req.params as any;
    const threshold = Math.max(1, Number(req.query.threshold) || 2);

  if (!clientId) return res.status(400).json({ success: false, error: 'clientId required' });
  if (!isValidObjectId(clientId)) return res.status(400).json({ success: false, error: 'invalid clientId' });

  const ratings = await ClientRating.find({ clientId }).lean();
    if (!ratings || ratings.length === 0) {
      return res.json({ success: true, data: { averageStars: null, totalRatings: 0, positiveAdjectives: [], negativeAdjectives: [] } });
    }

    const totalRatings = ratings.length;
    const averageStars = ratings.reduce((acc, r) => acc + (r.stars || 0), 0) / totalRatings;

    const posCounts: Record<string, number> = {};
    const negCounts: Record<string, number> = {};
    for (const r of ratings) {
      (r.adjectives?.positive || []).forEach(adj => { posCounts[adj] = (posCounts[adj] || 0) + 1; });
      (r.adjectives?.negative || []).forEach(adj => { negCounts[adj] = (negCounts[adj] || 0) + 1; });
    }

    const positiveAdjectives = Object.entries(posCounts)
      .map(([adjective, count]) => ({ adjective, count }))
      .sort((a, b) => b.count - a.count);

    const negativeAdjectives = Object.entries(negCounts)
      .map(([adjective, count]) => ({ adjective, count }))
      .filter(item => item.count >= threshold)
      .sort((a, b) => b.count - a.count);

    res.json({
      success: true,
      data: {
        averageStars: Number(averageStars.toFixed(2)),
        totalRatings,
        positiveAdjectives,
        negativeAdjectives,
        threshold,
      }
    });
  } catch (error) {
    console.error('Error getting client reputation:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

