import { router } from "expo-router";
import * as SecureStore from "expo-secure-store";
import { Alert, Platform } from "react-native";


//const API_URL = "http://localhost:4000"
const API_URL = "https://manito-iota.vercel.app"

const TOKEN_KEY = "manito_token";



//GLOBAL CALLS

export async function getToken() {
    const token = await SecureStore.getItemAsync(TOKEN_KEY);

    if (token) {
        return token
    } else {
        return false
    }
}
export async function setToken(token: string) {
    try {
        if (!token) throw new Error("Invalid token");
        await SecureStore.setItemAsync(TOKEN_KEY, token);
    } catch (error) {
        console.error("Error setting token:", error);
    }
}
export async function removeToken() {
    //delete from secure store
    await SecureStore.deleteItemAsync(TOKEN_KEY);

    //redirect to main selector screen
    router.replace('/login')
}


/* AUTH CALLS */

export async function authenticateOAuth() {

    const token = await getToken()

    if (token) {
        const call = await fetch(`${API_URL}/oauth/authenticate`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        })

        const response = await call.json()

        if (!response.success) {
            return { success: false }
        }

        if (Platform.OS == 'ios') {
            //const saveNotificationToken = registerForPushNotificationsAsync()
        }

        return {
            success: true,
            data: response.data,
            subscription: response.subscription
        }
    } else {
        return { success: false }
    }
}
export async function appleLogin(identityToken: string) {

    const response = await fetch(`${API_URL}/oauth/login/apple`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            identityToken,
        })
    })


    return await response.json()
}
export async function googleLogin(id: string, email: string, name: string, surname: string, photo: string) {

    const response = await fetch(`${API_URL}/oauth/login/google`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            id,
            email,
            name,
            surname,
            photo
        })
    })


    return await response.json()
}
export async function updateAccount(name: string, surname: string, username: string) {
    const token = await getToken()

    const response = await fetch(`${API_URL}/oauth/update/account`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
        },
        body: JSON.stringify({ name, surname, username })
    })


    return await response.json()
}



/* AGENT CHAT */
export async function agentStartChat(force?: boolean) {

    const token = await getToken()
    if (!token) {
        return {
            success: false
        }
    }


    const response = await fetch(`${API_URL}/chat/start`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            force
        })
    })

    return await response.json()
}
export async function agentSendMessage(chatId: string, message: string) {

    const token = await getToken()
    if (!token) {
        return {
            success: false
        }
    }


    const response = await fetch(`${API_URL}/chat/message`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            chatId,
            message
        })
    })

    return await response.json()
}
export async function agentSendPicture(chatId: string, imageUri: string) {
    const token = await getToken()

    if (!token) {
        return {
            success: false
        }
    }

    try {
        // Fetch the image as a blob
        const response = await fetch(imageUri);
        const blob = await response.blob();


        // Create FormData to send the image
        const formData = new FormData();

        formData.append('image', {
            uri: imageUri,
            type: blob.type || 'image/jpeg',
            name: `profile-pic-${Date.now()}.jpg`,
        } as any);

        formData.append('chatId', chatId)



        // Send to API
        const uploadResponse = await fetch(`${API_URL}/chat/upload`, {
            method: 'POST',
            body: formData,
            headers: {
                'Content-Type': 'multipart/form-data',
                "Authorization": `Bearer ${token}`
            },
        });

        const result = await uploadResponse.json();
        return result

    } catch (error) {
        return {
            success: false
        }
    }
}
export async function agentfetchMessages(chatId: string) {

    const token = await getToken()
    if (!token) {
        return {
            success: false
        }
    }


    const response = await fetch(`${API_URL}/chat/${chatId}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        }
    })

    return await response.json()
}


/* JOBS */
export async function viewJobs(page?: number) { //Operator Accepted or Owner All

    const token = await getToken()
    if (!token) {
        return {
            success: false
        }
    }


    const response = await fetch(`${API_URL}/jobs/?page=${page ?? 1}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        }
    })

    return await response.json()
}
export async function viewJob(jobId?: string) { //Operator Accepted or Owner All

    const token = await getToken()
    if (!token) {
        return {
            success: false
        }
    }


    const response = await fetch(`${API_URL}/jobs/${jobId}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        }
    })

    return await response.json()
}




/* NOTIFICATIONS  */
export async function saveNotificationToken(expoToken: string) {
    const token = await getToken()
    if (!token) return

    const response = await fetch(`${API_URL}/notifications/save-token`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`

        },
        body: JSON.stringify({
            token: expoToken
        })
    })


    return await response.json()
}



/* Jobs: status updates with GPS */
export async function updateJobStatus(jobId: string, params: { status: 'accepted'|'in_progress'|'completed'|'cancelled'|'pending'|'pending_refund', rating?: number, currentLocation?: { latitude: number, longitude: number } }) {
  const token = await getToken();
  if (!token) return { success: false };
    const controller = new AbortController();
    const timeoutMs = 10000;
    const timeout = setTimeout(() => controller.abort(), timeoutMs);
    try {
        const response = await fetch(`${API_URL}/jobs/${jobId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(params),
            signal: controller.signal
        });
        clearTimeout(timeout);
        let body: any;
        try { body = await response.json(); } catch (e) { body = await response.text(); }
        if (!response.ok) {
            const msg = body && (body.error || body.message) ? (body.error || body.message) : (typeof body === 'string' ? body : 'Request failed');
            return { success: false, status: response.status, error: msg } as any;
        }
        return body;
    } catch (err: any) {
        clearTimeout(timeout);
        const message = err?.name === 'AbortError' ? 'Request timed out' : (err?.message || String(err));
        return { success: false, error: message } as any;
    }
}

/* Jobs: upload completion photos */
export async function uploadCompletionPhotosApi(jobId: string, files: Array<{ uri: string; name?: string; type?: string }>) {
  const token = await getToken();
  if (!token) return { success: false };
    const controller = new AbortController();
    const timeoutMs = 15000;
    const timeout = setTimeout(() => controller.abort(), timeoutMs);
    try {
        const form = new FormData();
        files.forEach((f, idx) => {
            const fileName = f.name || `photo_${idx}.jpg`;
            const fileType = f.type || 'image/jpeg';
            // @ts-ignore - React Native FormData file type
            form.append('photos', {
                uri: f.uri,
                name: fileName,
                type: fileType,
            });
        });

        const response = await fetch(`${API_URL}/jobs/${jobId}/completion-photos`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            },
            body: form,
            signal: controller.signal
        });
        clearTimeout(timeout);
        let body: any;
        try { body = await response.json(); } catch (e) { body = await response.text(); }
        if (!response.ok) {
            const msg = body && (body.error || body.message) ? (body.error || body.message) : (typeof body === 'string' ? body : 'Request failed');
            return { success: false, status: response.status, error: msg } as any;
        }
        return body;
    } catch (err: any) {
        clearTimeout(timeout);
        const message = err?.name === 'AbortError' ? 'Request timed out' : (err?.message || String(err));
        return { success: false, error: message } as any;
    }
}

/* Jobs: operator rates client */
export async function rateClientForJobApi(jobId: string, stars: number, adjectives?: { positive?: string[]; negative?: string[] }) {
  const token = await getToken();
  if (!token) return { success: false };
    // Validate rating input early to avoid unnecessary network calls
    const MIN_RATING = 1;
    const MAX_RATING = 5;
    if (typeof stars !== 'number' || !isFinite(stars) || Math.floor(stars) !== stars || stars < MIN_RATING || stars > MAX_RATING) {
        return { success: false, error: 'Invalid rating' } as any;
    }

    if (adjectives !== undefined && adjectives !== null) {
        if (typeof adjectives !== 'object') {
            return { success: false, error: 'Invalid adjectives' } as any;
        }
        if (adjectives.positive !== undefined && (!Array.isArray(adjectives.positive) || !adjectives.positive.every(a => typeof a === 'string'))) {
            return { success: false, error: 'Invalid adjectives.positive' } as any;
        }
        if (adjectives.negative !== undefined && (!Array.isArray(adjectives.negative) || !adjectives.negative.every(a => typeof a === 'string'))) {
            return { success: false, error: 'Invalid adjectives.negative' } as any;
        }
    }

    const controller = new AbortController();
    const timeoutMs = 10000;
    const timeout = setTimeout(() => controller.abort(), timeoutMs);
    try {
        const response = await fetch(`${API_URL}/jobs/${jobId}/client-rating`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ stars, adjectives }),
            signal: controller.signal
        });
        clearTimeout(timeout);
        let body: any;
        try { body = await response.json(); } catch (e) { body = await response.text(); }
        if (!response.ok) {
            const msg = body && (body.error || body.message) ? (body.error || body.message) : (typeof body === 'string' ? body : 'Request failed');
            return { success: false, status: response.status, error: msg } as any;
        }
        return body;
    } catch (err: any) {
        clearTimeout(timeout);
        const message = err?.name === 'AbortError' ? 'Request timed out' : (err?.message || String(err));
        return { success: false, error: message } as any;
    }
}

/* Materials: request quote */
export async function requestMaterialsQuoteApi(jobId: string, items: Array<{ sku: string; name: string; quantity: number; unitPrice: number }>, deliveryLocation?: { latitude: number; longitude: number }) {
  const token = await getToken();
  if (!token) return { success: false };
    const controller = new AbortController();
    const timeoutMs = 15000;
    const timeout = setTimeout(() => controller.abort(), timeoutMs);
    try {
        const response = await fetch(`${API_URL}/jobs/${jobId}/material-orders`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ items, deliveryLocation }),
            signal: controller.signal
        });
        clearTimeout(timeout);
        let body: any;
        try { body = await response.json(); } catch (e) { body = await response.text(); }
        if (!response.ok) {
            const msg = body && (body.error || body.message) ? (body.error || body.message) : (typeof body === 'string' ? body : 'Request failed');
            return { success: false, status: response.status, error: msg } as any;
        }
        return body;
    } catch (err: any) {
        clearTimeout(timeout);
        const message = err?.name === 'AbortError' ? 'Request timed out' : (err?.message || String(err));
        return { success: false, error: message } as any;
    }
}

/* Materials: approve/reject */
export async function approveMaterialOrderApi(orderId: string) {
  const token = await getToken();
  if (!token) return { success: false };
    const controller = new AbortController();
    const timeoutMs = 10000;
    const timeout = setTimeout(() => controller.abort(), timeoutMs);
    try {
        const response = await fetch(`${API_URL}/jobs/material-orders/${orderId}/approve`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            },
            signal: controller.signal
        });
        clearTimeout(timeout);
        let body: any;
        try { body = await response.json(); } catch (e) { body = await response.text(); }
        if (!response.ok) {
            const msg = body && (body.error || body.message) ? (body.error || body.message) : (typeof body === 'string' ? body : 'Request failed');
            return { success: false, status: response.status, error: msg } as any;
        }
        return body;
    } catch (err: any) {
        clearTimeout(timeout);
        const message = err?.name === 'AbortError' ? 'Request timed out' : (err?.message || String(err));
        return { success: false, error: message } as any;
    }
}

export async function rejectMaterialOrderApi(orderId: string) {
  const token = await getToken();
  if (!token) return { success: false };
    const controller = new AbortController();
    const timeoutMs = 10000;
    const timeout = setTimeout(() => controller.abort(), timeoutMs);
    try {
        const response = await fetch(`${API_URL}/jobs/material-orders/${orderId}/reject`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            },
            signal: controller.signal
        });
        clearTimeout(timeout);
        let body: any;
        try { body = await response.json(); } catch (e) { body = await response.text(); }
        if (!response.ok) {
            const msg = body && (body.error || body.message) ? (body.error || body.message) : (typeof body === 'string' ? body : 'Request failed');
            return { success: false, status: response.status, error: msg } as any;
        }
        return body;
    } catch (err: any) {
        clearTimeout(timeout);
        const message = err?.name === 'AbortError' ? 'Request timed out' : (err?.message || String(err));
        return { success: false, error: message } as any;
    }
}
