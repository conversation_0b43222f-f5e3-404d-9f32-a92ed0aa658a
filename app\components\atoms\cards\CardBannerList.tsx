import { StyleSheet, Text, View, Dimensions, TouchableOpacity, Image } from 'react-native'
import React from 'react'
import { LinearGradient } from 'expo-linear-gradient';

const { width } = Dimensions.get('window');
const TILE_SIZE = (width - 50) * 0.5;


interface CardTileProps {
  onPress?: () => void,

  badge?: string | any,
  badgeColor?: string,
  text: string,
  img: React.ReactNode,
}

const CardBannerList = ({ onPress, badge, badgeColor, text, img }: CardTileProps) => {
  return (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={() => {
        onPress && onPress()
      }}
    >
      <LinearGradient

        colors={['#337836', '#3378369e', '#914b0e60']}
        style={styles.component}
        start={{ x: 0, y: 1 }}
        end={{ x: 1, y: 0 }} //y: 0.5
      >
        <View style={{ width: '70%', gap: 8 }}>
          {
          badge &&
            <View  style={{ backgroundColor: badgeColor ?? 'white', borderRadius: 20, borderBottomLeftRadius: 5, borderTopLeftRadius: 10, paddingHorizontal: 10, paddingVertical: 4, width: '60%'}}> 
              <Text style={{ fontFamily: 'Montserrat', fontWeight: 600, color: '#000000'}}>{badge}</Text>
            </View>
          }
          <Text style={{ width: '100%', fontFamily: 'Montserrat', fontWeight: 600, fontSize: 18, color: 'white' }}>{text}</Text>
        </View>
        {img}
      </LinearGradient>
    </TouchableOpacity>
  )

}

export default CardBannerList

const styles = StyleSheet.create({
  component: {
    width: width * 0.85,
    height: 120,
    marginLeft: 15,
    backgroundColor: '#e8e8e8ff',
    borderRadius: 20,
    zIndex: 10,

    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: 20

  }
})