"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const operatorPenaltySchema = new mongoose_1.default.Schema({
    operatorId: {
        type: String,
        required: true,
        ref: 'Account',
        index: true
    },
    jobId: {
        type: String,
        ref: 'Job',
        index: true
    },
    bidId: {
        type: String,
        ref: 'Bid'
    },
    cancellationId: {
        type: String,
        ref: 'CancellationRecord'
    },
    type: {
        type: String,
        required: true,
        enum: [
            'cancellation_before_start',
            'cancellation_after_arrival',
            'no_show',
            'poor_performance',
            'policy_violation',
            'client_complaint'
        ],
        index: true
    },
    status: {
        type: String,
        required: true,
        enum: ['pending', 'active', 'served', 'waived', 'disputed', 'expired'],
        default: 'pending',
        index: true
    },
    severity: {
        type: String,
        required: true,
        enum: ['minor', 'moderate', 'major', 'severe'],
        index: true
    },
    financialPenalty: {
        amount: { type: Number, required: true, min: 0 },
        currency: { type: String, required: true, enum: ['DOP', 'USD'], default: 'DOP' },
        deductedFrom: {
            type: String,
            required: true,
            enum: ['future_earnings', 'security_deposit', 'immediate_charge']
        },
        deductedAt: { type: Date },
        paymentTransactionId: { type: String, ref: 'PaymentTransaction' }
    },
    ratingImpact: {
        pointsDeducted: { type: Number, required: true, min: 0, max: 5 },
        appliedAt: { type: Date },
        previousRating: { type: Number, min: 0, max: 5 },
        newRating: { type: Number, min: 0, max: 5 },
        isConfigurable: { type: Boolean, default: true }
    },
    availabilityRestriction: {
        restrictionType: {
            type: String,
            required: true,
            enum: ['none', 'temporary_suspension', 'category_restriction', 'permanent_ban'],
            default: 'none'
        },
        restrictedUntil: { type: Date },
        restrictedCategories: [{ type: String }],
        appliedAt: { type: Date },
        liftedAt: { type: Date }
    },
    reason: {
        type: String,
        required: true,
        maxlength: 500
    },
    description: {
        type: String,
        required: true,
        maxlength: 2000
    },
    evidenceUrls: [{
            type: String // S3 URLs
        }],
    issuedBy: {
        type: String,
        required: true,
        ref: 'Account'
    },
    issuedByRole: {
        type: String,
        required: true,
        enum: ['admin', 'system']
    },
    reviewedBy: {
        type: String,
        ref: 'Account'
    },
    reviewedAt: {
        type: Date
    },
    reviewNotes: {
        type: String,
        maxlength: 1000
    },
    appealReason: {
        type: String,
        maxlength: 1000
    },
    appealedAt: {
        type: Date
    },
    appealStatus: {
        type: String,
        enum: ['none', 'pending', 'approved', 'rejected'],
        default: 'none'
    },
    appealResolvedAt: {
        type: Date
    },
    appealResolution: {
        type: String,
        maxlength: 1000
    },
    isRecurring: {
        type: Boolean,
        default: false
    },
    previousPenaltyIds: [{
            type: String,
            ref: 'OperatorPenalty'
        }],
    recurrenceMultiplier: {
        type: Number,
        default: 1.0,
        min: 1.0,
        max: 5.0
    },
    expiresAt: {
        type: Date,
        index: true
    },
    archivedAt: {
        type: Date
    },
    metadata: {
        automaticallyIssued: { type: Boolean, default: false },
        clientReported: { type: Boolean, default: false },
        systemDetected: { type: Boolean, default: false },
        warningIssued: { type: Boolean, default: false },
        escalationLevel: { type: Number, default: 1, min: 1 },
        relatedTicketId: { type: String },
        internalNotes: { type: String, maxlength: 2000 }
    }
}, {
    timestamps: true
});
// Indexes for efficient querying
operatorPenaltySchema.index({ operatorId: 1, createdAt: -1 });
operatorPenaltySchema.index({ type: 1, status: 1 });
operatorPenaltySchema.index({ severity: 1, status: 1 });
operatorPenaltySchema.index({ expiresAt: 1 });
operatorPenaltySchema.index({ jobId: 1 });
exports.default = mongoose_1.default.model('OperatorPenalty', operatorPenaltySchema);
