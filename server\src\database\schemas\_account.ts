import mongoose, { Document } from 'mongoose';

export interface IAccount extends Document {
  email: string;
  password: string;
  user: {
    name: string;
    surname: string;
    username: string;
    profile_picture: string;
    birthdate: Date | string; 
    type: 'user' | 'admin' | 'operator';
  };
  contacts: {
    phone: string;
    address: string;
  };
  settings: {
    currency: string;
    preferred_language: string;
    timezone: string;
  };
  finances: {
    billing_address: string;
  };
  booleans: {
    isVerified: boolean;
    isAdmin: boolean;
    isOperator: boolean; // Whether user has operator capabilities
  };
  currentRole: 'user' | 'operator'; // Current active role for dual-role users
  tokens: {
    verificationToken?: string;
    passwordResetToken?: string;
  };
  notifications: {
    expo_push_token?: string;
  };
  createdAt: Date
}


const accountSchema = new mongoose.Schema<IAccount>({
    email: {
      type: String,
      required: true,
      trim: true,
      //minlength: 5,
      maxlength: 255,
      unique: false
    },
    password: {
      type: String,
      //required: true,
    },
    user: {
      name: {
        type: String,
        default: "",
      },
      surname: {
        type: String,
        default: "",
      },
      username: {
        type: String,
        default: "",
      },
      profile_picture: {
        type: String,
        default: "",
      },
      birthdate: {
        type: Date,
        default: "",
      },
      type: {
        type: String,
        enum: ['user', 'admin', 'operator'],
        default: 'operator',
      },
    },
    contacts: {
      phone: {
        type: String,
        default: "",
      },
      address: {
        type: String,
        default: "",
      },
    },
    settings: {
      currency: {
        type: String,
        default: "EUR",
      },
      preferred_language: {
        type: String,
        default: "it",
      },
      timezone: {
        type: String,
        default: "CET",
      },
    },
    finances: {
      billing_address: {
        type: String,
        default: "",
      },
    },
    booleans: {
      isVerified: {
        type: Boolean,
        default: false,
      },
      isAdmin: {
        type: Boolean,
        default: false,
      },
      isOperator: {
        type: Boolean,
        default: false,
      },
    },
    currentRole: {
      type: String,
      enum: ['user', 'operator'],
      default: 'user'
    },
    tokens: {
      verificationToken: {
        type: String,
      },
      passwordResetToken: {
        type: String,
      },
    },
    notifications: {
      expo_push_token: {
        type: String,
        default: "",
      },
    },
    createdAt: {
      type: Date,
      default: Date.now()
    }
  });
  
  export default mongoose.model<IAccount>('Account', accountSchema);
  