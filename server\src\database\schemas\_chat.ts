import mongoose, { Document, Schema } from 'mongoose';
import JobCategory from './_jobCategory';

export interface IMessage extends Document {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  images?: string[]; // S3 URLs if message contains images
}

export interface IChat extends Document {
  id: string;
  ownerId: string;
  status: 'started' | 'chatting' | 'done';
  category: string | null;
  description: string | null;
  images: string[]; // Array of S3 URLs
  price: number | null;
  date: string | null; // Format: DD/MM/YYYY
  hour: string | null; // Format: HH:MM
  messages: IMessage[]; // For storage purposes only
  createdAt: Date;
  updatedAt: Date;
}

const messageSchema = new mongoose.Schema<IMessage>({
  role: {
    type: String,
    enum: ['user', 'assistant'],
    required: true
  },
  content: {
    type: String,
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  images: [{
    type: String // S3 URLs
  }]
});

const chatSchema = new mongoose.Schema<IChat>({
  ownerId: {
    type: String,
    required: true,
    ref: 'Account'
  },
  status: {
    type: String,
    enum: ['started', 'chatting', 'done'],
    default: 'started'
  },
  category: {
    type: String,
    default: null,
    validate: {
      // Allow null (category not chosen yet). When provided, it must exist in JobCategory (active).
      validator: async function (val: string | null) {
        if (val === null || typeof val === 'undefined') return true;
        const name = String(val).trim().toLowerCase();
        if (!name) return false;
        const found = await JobCategory.findOne({ name, isActive: true });
        return !!found;
      },
      message: 'Invalid category. Must be one of the active job categories.'
    }
  },
  description: {
    type: String,
    default: null
  },
  images: [{
    type: String // S3 URLs
  }],
  price: {
    type: Number,
    default: null
  },
  date: {
    type: String,
    default: null,
    match: /^\d{2}\/\d{2}\/\d{4}$/ // DD/MM/YYYY format
  },
  hour: {
    type: String,
    default: null,
    match: /^\d{2}:\d{2}$/ // HH:MM format
  },
  messages: [messageSchema]
}, {
  timestamps: true
});

// Index for efficient querying
chatSchema.index({ ownerId: 1, createdAt: -1 });
chatSchema.index({ status: 1 });

// Ensure validators run on update operations
chatSchema.pre(['updateOne', 'updateMany', 'findOneAndUpdate'], function() {
  this.setOptions({ runValidators: true, context: 'query' });
});

export default mongoose.model<IChat>('Chat', chatSchema);
