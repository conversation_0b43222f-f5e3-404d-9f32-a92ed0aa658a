"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const express_1 = tslib_1.__importDefault(require("express"));
const middleware_1 = require("../middleware");
const middleware_2 = require("../middleware");
const jobCategory_1 = require("../controllers/jobCategory");
const router = express_1.default.Router();
// Public endpoint - get active categories
router.get('/active', (0, middleware_2.tryCatch)(jobCategory_1.getActiveCategories));
// Public endpoint - get supported document requirement categories (master list)
router.get('/document-requirements', (0, middleware_2.tryCatch)(jobCategory_1.getDocumentRequirementCategories));
// Admin endpoints
router.get('/', middleware_1.AuthenticateTokenOAuth, middleware_1.requireAdmin, (0, middleware_2.tryCatch)(jobCategory_1.getAllCategories));
router.post('/', middleware_1.AuthenticateTokenOAuth, middleware_1.requireAdmin, (0, middleware_2.tryCatch)(jobCategory_1.createCategory));
router.put('/:categoryId', middleware_1.AuthenticateTokenOAuth, middleware_1.requireAdmin, (0, middleware_2.tryCatch)(jobCategory_1.updateCategory));
router.delete('/:categoryId', middleware_1.AuthenticateTokenOAuth, middleware_1.requireAdmin, (0, middleware_2.tryCatch)(jobCategory_1.deleteCategory));
router.get('/:categoryId/document-requirements', middleware_1.AuthenticateTokenOAuth, middleware_1.requireAdmin, (0, middleware_2.tryCatch)(jobCategory_1.getCategoryDocumentRequirements));
router.put('/:categoryId/document-requirements', middleware_1.AuthenticateTokenOAuth, middleware_1.requireAdmin, (0, middleware_2.tryCatch)(jobCategory_1.updateCategoryDocumentRequirements));
exports.default = router;
