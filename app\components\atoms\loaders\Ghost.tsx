import LottieView from 'lottie-react-native'
import React from 'react'
import { StyleSheet, View } from 'react-native'

const GhostLoader = () => {

  return (
    <View style={styles.viewport}>
      <LottieView 
        source={require('@/assets/animations/ghost.json')}
        style={{ height: 200, aspectRatio: 1.5}}
        speed={1}
        autoPlay
        loop
        />
    </View>
  )
}

export default GhostLoader


const styles = StyleSheet.create({
  viewport: {

    zIndex: 100,
    width: '100%',
    height: 200,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    },

})