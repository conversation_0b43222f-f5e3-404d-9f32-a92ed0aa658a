"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const cors_1 = tslib_1.__importDefault(require("cors"));
const chalk_1 = tslib_1.__importDefault(require("chalk"));
const express_1 = tslib_1.__importDefault(require("express"));
const middleware_1 = require("./middleware");
const routes_1 = require("./routes");
const connection_1 = tslib_1.__importDefault(require("./database/connection"));
// Create express app
const app = (0, express_1.default)();
// Middlewares
app.use((0, cors_1.default)());
app.use(express_1.default.json());
app.use(middleware_1.errorHandler);
// Routes
app.use('/notifications', routes_1.notifications);
app.use('/oauth', routes_1.oauth);
app.use('/chat', routes_1.chat);
app.use('/jobs', routes_1.job);
app.use('/job-categories', routes_1.jobCategory);
app.use('/bids', routes_1.bid);
app.use('/operators', routes_1.operatorProfile);
app.use('/operator-app', routes_1.operatorApplication);
app.use('/admin', routes_1.admin);
app.use('/payments', routes_1.payments);
app.use('/clients', routes_1.clients);
app.use('/cancellation', routes_1.cancellation);
const startServer = () => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    if (process.env.NODE_ENV === 'development') {
        console.log(chalk_1.default.green(`[SERVER] Starting`));
    }
    const PORT = process.env.PORT || 4000;
    try {
        // Await database connection
        yield (0, connection_1.default)();
        if (process.env.NODE_ENV === 'development') {
            console.log(chalk_1.default.green(`[SERVER] Database Connected`));
        }
        if (process.env.NODE_ENV === 'development') {
            console.log(chalk_1.default.green(`[SERVER] Default data initialized`));
        }
        // Start the server after successful database connection
        const server = app.listen(PORT, () => {
            if (process.env.NODE_ENV === 'development') {
                console.log(chalk_1.default.green(`[SERVER] Running on`), chalk_1.default.yellow.underline(`http://localhost:${PORT}`));
            }
        });
        // Optionally schedule nightly no-show processing. Disabled by default to avoid
        // duplicate side-effectful runs in multi-replica deployments. To enable set
        // the environment variable ENABLE_NO_SHOW_CRON='true' in production.
        //
        // Note: a better long-term approach is to run this job from an external
        // scheduler (Cloud Scheduler, cron job, Kubernetes CronJob) or protect the
        // in-process task with a distributed lock (Redis, etcd) so only one replica
        // executes the job at a time.
        const enableNoShowCron = process.env.ENABLE_NO_SHOW_CRON === 'true';
        const isProd = process.env.NODE_ENV === 'production';
        if (enableNoShowCron && isProd) {
            try {
                const { processOperatorNoShows } = yield Promise.resolve().then(() => tslib_1.__importStar(require('./services/noShowCron')));
                // Schedule a daily job at local 03:00 using a recalculated setTimeout chain.
                // This avoids overlapping runs, DST drift, and keeps timers from keeping Node alive (.unref()).
                const scheduleNextRun = () => {
                    const now = new Date();
                    const next = new Date(now);
                    next.setHours(3, 0, 0, 0);
                    if (next.getTime() <= now.getTime()) {
                        // already past today's 03:00, schedule for tomorrow
                        next.setDate(next.getDate() + 1);
                    }
                    const delay = next.getTime() - now.getTime();
                    const timer = setTimeout(() => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
                        try {
                            yield processOperatorNoShows();
                        }
                        catch (err) {
                            console.error('No-show cron failed:', err);
                        }
                        finally {
                            // After the run completes (success or failure), schedule the next run.
                            try {
                                scheduleNextRun();
                            }
                            catch (schedErr) {
                                console.error('Failed to schedule next no-show run:', schedErr);
                            }
                        }
                    }), delay);
                    // Do not keep the process alive solely because of this timer
                    if (typeof timer.unref === 'function')
                        timer.unref();
                };
                // Kick off the scheduling chain
                scheduleNextRun();
                console.log('No-show cron enabled and scheduled');
            }
            catch (e) {
                console.warn('No-show cron failed to initialize:', e);
            }
        }
        else {
            console.log('No-show cron disabled. To enable set ENABLE_NO_SHOW_CRON=true in production.');
        }
        /* Handle unhandled promise rejections */
        process.on('unhandledRejection', (err) => {
            if (err instanceof Error) {
                if (process.env.NODE_ENV === 'development') {
                    console.log(chalk_1.default.red(`[ERROR] Unhandled Rejection: ${err.message}`));
                }
            }
            // Close server & exit process
            server.close(() => process.exit(1));
        });
    }
    catch (error) {
        if (process.env.NODE_ENV === 'development') {
            console.log(chalk_1.default.red(`[DATABASE] Connection failed: ${error.message}`));
        }
        process.exit(1);
    }
});
startServer();
