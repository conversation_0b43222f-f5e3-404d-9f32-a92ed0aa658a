import mongoose, { Document, Schema } from 'mongoose';

export interface IBid extends Document {
  jobId: string; // Reference to Job
  operatorId: string; // Reference to Account (operator)
  amount: number; // Bid amount
  message: string; // Proposal/message from operator
  estimatedDuration: string; // Estimated time to complete (e.g., "2 days", "1 week")
  status: 'pending' | 'accepted' | 'rejected' | 'withdrawn';
  proposedStartDate: string; // When operator can start (DD/MM/YYYY)
  proposedStartTime: string; // Time operator can start (HH:MM)
  createdAt: Date;
  updatedAt: Date;
}

const bidSchema = new mongoose.Schema<IBid>({
  jobId: {
    type: String,
    required: true,
    ref: 'Job'
  },
  operatorId: {
    type: String,
    required: true,
    ref: 'Account'
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  message: {
    type: String,
    required: true,
    maxlength: 1000
  },
  estimatedDuration: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'accepted', 'rejected', 'withdrawn'],
    default: 'pending'
  },
  proposedStartDate: {
    type: String,
    required: true,
    match: /^\d{2}\/\d{2}\/\d{4}$/ // DD/MM/YYYY format
  },
  proposedStartTime: {
    type: String,
    required: true,
    match: /^\d{2}:\d{2}$/ // HH:MM format
  }
}, {
  timestamps: true
});

// Compound index to prevent duplicate bids from same operator on same job
bidSchema.index({ jobId: 1, operatorId: 1 }, { unique: true });
bidSchema.index({ jobId: 1, status: 1 });
bidSchema.index({ operatorId: 1, createdAt: -1 });
bidSchema.index({ status: 1 });

export default mongoose.model<IBid>('Bid', bidSchema);
