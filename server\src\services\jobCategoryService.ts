import { JobCategory } from '../database/schemas';
import { IJobCategory } from '../database/schemas/_jobCategory';

class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

class JobCategoryService {
  // Normalize a category name consistently across the service
  private normalizeName(name: string): string | undefined {
    const raw = String(name || '');
    const trimmed = raw.trim().toLowerCase();
    if (!trimmed) return undefined;
    // Strip accents, collapse non-alphanumerics to underscores, and trim underscores
    const ascii = trimmed.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    const normalized = ascii.replace(/[^a-z0-9]+/g, '_').replace(/^_+|_+$/g, '');
    return normalized || undefined;
  }

  /**
   * Get all active job categories
   */
  async getActiveCategories(): Promise<IJobCategory[]> {
    return await JobCategory.find({ isActive: true }).sort({ order: 1, displayName: 1 });
  }

  /**
   * Get all job categories (including inactive ones) - for admin
   */
  async getAllCategories(): Promise<IJobCategory[]> {
    return await JobCategory.find().sort({ order: 1, displayName: 1 });
  }

  /** Get category by name */
    async getCategoryByName(name: string): Promise<IJobCategory | null> {
    const normalized = this.normalizeName(name);
    if (!normalized) return null;
    return await JobCategory.findOne({ name: normalized });
  }

  /**
   * Check if a category exists and is active
   */
  async isCategoryValid(name: string): Promise<boolean> {
    const normalized = this.normalizeName(name);
    if (!normalized) return false;
    const category = await JobCategory.findOne({ name: normalized, isActive: true });
    return !!category;
  }

  /**
   * Create a new job category
   */
  async createCategory(categoryData: { name: string; displayName: string; description?: string; order?: number; documents_required?: IJobCategory['documents_required'] }): Promise<IJobCategory> {
  const normalizedName = this.normalizeName(categoryData.name);
  if (!normalizedName) {
    throw new Error('Category name is required');
  }
  const existingCategory = await JobCategory.findOne({ name: normalizedName });
  if (existingCategory) {
    throw new Error(`Category with name '${normalizedName}' already exists`);
  }

  const { displayName, description, order, documents_required } = categoryData;
  const category = new JobCategory({
    name: normalizedName,
    displayName,
    description,
    order,
    documents_required,
  });
  return await category.save();
}

  /**
   * Update job category
   */
  async updateCategory(id: string, updateData: Partial<IJobCategory>): Promise<IJobCategory | null> {
    // Build a whitelist-only update object to avoid mass-assignment
    const toUpdate: Partial<IJobCategory> = {};

    // name
    if ('name' in updateData) {
      if (typeof updateData.name !== 'string') {
        throw new ValidationError('Category name must be a string');
      }
      const normalized = this.normalizeName(updateData.name);
      if (!normalized) {
        throw new ValidationError('Category name is required');
      }
      // Use normalized for uniqueness check and assignment
      const existingCategory = await JobCategory.findOne({ name: normalized, _id: { $ne: id } });
      if (existingCategory) {
        throw new Error(`Category with name '${normalized}' already exists`);
      }
      toUpdate.name = normalized;
    }

    // displayName
    if ('displayName' in updateData) {
      if (typeof updateData.displayName !== 'string') {
        throw new ValidationError('displayName must be a string');
      }
      const displayName = updateData.displayName.trim();
      if (!displayName) {
        throw new ValidationError('displayName cannot be empty');
      }
      toUpdate.displayName = displayName;
    }

    // description (optional)
    if ('description' in updateData) {
      if (typeof updateData.description !== 'string' && typeof updateData.description !== 'undefined') {
        throw new ValidationError('description must be a string');
      }
      if (typeof updateData.description === 'string') {
        toUpdate.description = updateData.description;
      }
    }

    // isActive
    if ('isActive' in updateData) {
      if (typeof updateData.isActive !== 'boolean') {
        throw new ValidationError('isActive must be a boolean');
      }
      toUpdate.isActive = updateData.isActive;
    }

    // order
    if ('order' in updateData) {
      if (typeof updateData.order !== 'number' || !Number.isFinite(updateData.order)) {
        throw new ValidationError('order must be a finite number');
      }
      toUpdate.order = updateData.order;    }    
    // Protected/ignored fields are never copied: _id, createdAt, updatedAt, isSystem, etc.
    // If nothing to update, short-circuit
    if (Object.keys(toUpdate).length === 0) {
      return await JobCategory.findById(id);
    }

    try {
      return await JobCategory.findByIdAndUpdate(id, toUpdate, { new: true, runValidators: true });
    } catch (err: any) {
      // Handle duplicate key race condition (unique index on name)
      if (err && (err.code === 11000 || (typeof err.message === 'string' && err.message.includes('E11000')))) {
        const attemptedName = (toUpdate.name as any) || 'unknown';
        throw new ValidationError(`Category with name '${attemptedName}' already exists`);
      }
      if (err && err.name === 'CastError') {
        return null;
      }
      throw err;
    }
  }
  /**
   * Delete job category (hard delete)
   */
  async deleteCategory(id: string): Promise<boolean> {
    const result = await JobCategory.findByIdAndDelete(id);
    return !!result;
  }

  /**
   * Get category names for system prompt (used in chat)
   */
  async getCategoryNamesForPrompt(): Promise<string> {
    const categories = await this.getActiveCategories();
    return categories.map(cat => `"${cat.name}"`).join(', ');
  }

  /**
   * Initialize default categories if none exist
   */
  async initializeDefaultCategories(): Promise<void> {
    const count = await JobCategory.countDocuments();
    if (count === 0) {
      const defaultCategories = [
        { name: 'plumbing', displayName: 'Plomería', description: 'Servicios de plomería y fontanería', order: 1 },
        { name: 'electrician', displayName: 'Electricidad', description: 'Servicios eléctricos y cableado', order: 2 },
        { name: 'carpentry', displayName: 'Carpintería', description: 'Trabajos en madera y carpintería', order: 3 },
        { name: 'painting', displayName: 'Pintura', description: 'Servicios de pintura y decoración', order: 4 },
        { name: 'cleaning', displayName: 'Limpieza', description: 'Servicios de limpieza especializada', order: 5 },
        { name: 'other', displayName: 'Otros', description: 'Otros servicios del hogar', order: 6 }
      ];

      const ops = defaultCategories.map(c => ({
        updateOne: {
          filter: { name: c.name },
          update: { $setOnInsert: c },
          upsert: true,
        },
      }));
      await JobCategory.bulkWrite(ops, { ordered: false });
      console.log('Default job categories initialized (idempotent)');
    }
  }
}

export default new JobCategoryService();
