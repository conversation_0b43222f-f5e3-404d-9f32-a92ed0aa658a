
import * as MediaLibrary from 'expo-media-library'
import { agentfetchMessages, agentSendMessage, agentSendPicture, agentStartChat } from '@/services/api';
import { ChatMessage } from '@/types/chat';
import React, { createContext, useState, useEffect, useContext } from 'react';
import { Alert } from 'react-native';



interface ChatContextProps {
  handleSendPicture: (uri: string) => Promise<void>
  handleSendMessage: (message: string) => Promise<void>
  handleNewChat: (force?: boolean) => Promise<void>
  messages: ChatMessage[]
  chatId: string | null
  //handleFetchMessages: (chatId: string) => Promise<void>
}

export const ChatContext = createContext<ChatContextProps>({
  handleSendPicture: async (_uri: string) => { },
  handleSendMessage: async (_message: string) => { },
  handleNewChat: async (_force?: boolean) => { },
  messages: [],
  chatId: null
  //handleFetchMessages: async (_chatId: string) => { },
});

export const useChat = () => {
  return useContext(ChatContext) as ChatContextProps
}
 


export const ChatProvider = ({ children }: { children: React.ReactNode }) => {
  const [chatId, setChatId] = useState<string | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])


  const handleSendPicture = async (uri: any) => {
    try {

      if (!chatId || !uri) {
        console.error("Missing required params");
        return;
      }
      handleUserMessage('', new Date(Date.now()).toISOString(), uri)

      const call = await agentSendPicture(chatId, uri)

       if (!call.success) {
      return Alert.alert('No se pudo enviar el mensaje, intentar mas tardes')
    }

    handleAgentMessage(call.message, new Date(Date.now()).toISOString(), call.action)

    } catch (error: any) {
      console.log(error.message)
    }
  }
  const handleSendMessage = async (message: string) => {

    if (!chatId) {
      return Alert.alert('Error al manejar el chat, intentar mas tardes')
    }

    //Set the message in the message array (User)
    handleUserMessage(message, new Date(Date.now()).toISOString())

    //Send the actual message
    const call = await agentSendMessage(chatId, message)

    if (!call.success) {
      return Alert.alert('No se pudo enviar el mensaje, intentar mas tardes')
    }

    console.log(call)

    handleAgentMessage(call.message, new Date(Date.now()).toISOString(), call.action)
  }
  const handleNewChat = async (force?: boolean) => {
    setMessages([])
    const call = await agentStartChat(force)
    if (!call.success) {
      Alert.alert('No se pudo iniciar una nueva conversacion, intentar mas tardes')
    }

    if (call.isExistingChat) {
      if (!call.chatId) return
      setMessages([])

      const chat = await agentfetchMessages(call.chatId)
      console.log(chat.chat.messages)
      chat.chat.messages.forEach((message: any) => {
        if (message.role == 'assistant') {
          handleAgentMessage(message.content, new Date(message.timestamp).toISOString(), message.action ?? 'none')
        } else {
          handleUserMessage(message.content, new Date(message.timestamp).toISOString(), message.images[0])
        }


      })
    }

    setChatId(call.chatId)
  }



  const handleAgentMessage = (content: string, timestamp: string, action: string,) => {
    const newMessage: ChatMessage = {
      id: '',
      action: action ?? 'none',
      type: 'bot',
      content,
      timestamp: new Date(timestamp),
      //ingredients: type === "ingredient-display" ? userIngredients : undefined,
    }
    setMessages((prev) => [...prev, newMessage])

  }
  const handleUserMessage = (content: string, timestamp: string, image?: string) => {
    const newMessage: ChatMessage = {
      id: '',
      type: 'user',
      image: image ?? undefined,
      content: content,
      timestamp: new Date(timestamp)
    }
    setMessages((prev) => [...prev, newMessage])

  }



  return (
    <ChatContext.Provider value={{ handleSendMessage, handleSendPicture, handleNewChat, messages, chatId }}>
      {children}
    </ChatContext.Provider>
  );
};