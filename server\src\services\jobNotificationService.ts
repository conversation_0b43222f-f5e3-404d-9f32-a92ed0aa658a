import { OperatorProfile, NotificationLog } from '../database/schemas';
import { getUserNotificationToken, sendCustomNotification } from '../controllers/notifications';

export interface JobCreatedData {
  jobId: string;
  category: string;
  description: string;
  price: number;
  ownerId: string;
}

// Notify operators about new job creation
export const notifyOperatorsAboutNewJob = async (jobData: JobCreatedData): Promise<void> => {
  try {
    console.log(`Starting notification process for new ${jobData.category} job: ${jobData.jobId}`);

    // Get all available operators authorized for this category
    const authorizedOperators = await OperatorProfile.find({
      authorizedCategories: jobData.category,
      isAvailable: true
    }).populate('accountId', 'notifications.expo_push_token user.name');

    console.log(`Found ${authorizedOperators.length} authorized operators for category: ${jobData.category}`);

    if (authorizedOperators.length === 0) {
      console.log('No available operators found for this category');
      return;
    }

    // Prepare notification content
    const notificationTitle = 'New Job Available';
    const notificationMessage = `New ${jobData.category} job available - Budget: $${jobData.price}`;
    const notificationData = {
      jobId: jobData.jobId,
      category: jobData.category,
      type: 'job_created'
    };

    // Send notifications to all authorized operators
    const notificationPromises = authorizedOperators.map(async (operatorProfile) => {
      try {
        const account = operatorProfile.accountId as any;
        const operatorId = operatorProfile.accountId.toString();
        
        // Skip if this is the job owner (shouldn't happen, but safety check)
        if (operatorId === jobData.ownerId) {
          return;
        }

        // Get notification token
        const tokenResult = await getUserNotificationToken(operatorId);
        
        if (tokenResult.success && tokenResult.notificationToken) {
          // Send push notification
          const notificationResult = await sendCustomNotification(
            tokenResult.notificationToken,
            {
              title: notificationTitle,
              message: notificationMessage,
              data: notificationData,
              soundOn: true,
              badgeCount: 1
            }
          );

          console.log(`Notification sent to operator ${operatorId}:`, notificationResult ? 'success' : 'failed');
        } else {
          console.log(`No notification token found for operator ${operatorId}`);
        }

        // Log the notification in database
        await NotificationLog.create({
          recipientId: operatorId,
          type: 'job_created',
          title: notificationTitle,
          message: notificationMessage,
          data: notificationData,
          jobId: jobData.jobId
        });

        console.log(`Notification logged for operator ${operatorId}`);

      } catch (operatorError) {
        console.error(`Error notifying operator ${operatorProfile.accountId}:`, operatorError);
      }
    });

    // Wait for all notifications to complete
    await Promise.allSettled(notificationPromises);
    
    console.log(`Notification process completed for job ${jobData.jobId}`);

  } catch (error) {
    console.error('Error in notifyOperatorsAboutNewJob:', error);
  }
};

// Get notification statistics
export const getNotificationStats = async (jobId: string) => {
  try {
    const stats = await NotificationLog.aggregate([
      { $match: { jobId, type: 'job_created' } },
      {
        $group: {
          _id: null,
          totalSent: { $sum: 1 },
          totalRead: { $sum: { $cond: ['$isRead', 1, 0] } }
        }
      }
    ]);

    return stats[0] || { totalSent: 0, totalRead: 0 };
  } catch (error) {
    console.error('Error getting notification stats:', error);
    return { totalSent: 0, totalRead: 0 };
  }
};

// Mark notification as read
export const markNotificationAsRead = async (notificationId: string): Promise<boolean> => {
  try {
    const result = await NotificationLog.findByIdAndUpdate(
      notificationId,
      { 
        isRead: true, 
        readAt: new Date() 
      }
    );

    return !!result;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return false;
  }
};

// Get user notifications
export const getUserNotifications = async (
  userId: string, 
  page: number = 1, 
  limit: number = 20,
  unreadOnly: boolean = false
) => {
  try {
    const query: any = { recipientId: userId };
    
    if (unreadOnly) {
      query.isRead = false;
    }

    const notifications = await NotificationLog.find(query)
      .sort({ sentAt: -1 })
      .limit(limit)
      .skip((page - 1) * limit)
      .populate('jobId', 'category description status')
      .populate('bidId', 'amount status');

    const total = await NotificationLog.countDocuments(query);
    const unreadCount = await NotificationLog.countDocuments({ 
      recipientId: userId, 
      isRead: false 
    });

    return {
      notifications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      unreadCount
    };
  } catch (error) {
    console.error('Error getting user notifications:', error);
    return null;
  }
};
