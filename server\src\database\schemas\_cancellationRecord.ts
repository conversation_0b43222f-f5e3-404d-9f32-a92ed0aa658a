import mongoose, { Document } from 'mongoose';

export type CancellationType = 
  | 'operator_before_start'      // Operator cancels before job starts
  | 'client_less_than_24h'       // Client cancels less than 24h before start
  | 'client_after_start'         // Client cancels after job has started
  | 'operator_after_arrival'     // Operator cancels after arriving at location
  | 'system_no_show';            // System cancellation for no-show

export type CancellationStatus = 
  | 'pending'                    // Cancellation initiated but not processed
  | 'processing'                 // Currently processing payments/penalties
  | 'completed'                  // Cancellation fully processed
  | 'failed'                     // Cancellation processing failed
  | 'disputed';                  // Cancellation is under dispute

export interface ICancellationRecord extends Document {
  id: string;
  jobId: string; // Reference to Job
  bidId?: string; // Reference to Bid (if job was accepted)
  initiatedBy: string; // Account ID of who initiated the cancellation
  initiatorRole: 'client' | 'operator' | 'admin' | 'system';
  type: CancellationType;
  status: CancellationStatus;
  
  // Timing information
  scheduledStartTime: Date; // When the job was scheduled to start
  cancellationTime: Date; // When cancellation was initiated
  hoursBeforeStart: number; // Hours between cancellation and scheduled start
  
  // Financial details
  originalAmount: number; // Original job amount in minor units
  penaltyAmount: number; // Total penalty amount
  refundAmount: number; // Amount refunded to client
  adminShare: number; // Platform's share of penalty
  operatorShare: number; // Operator's share of penalty (if applicable)
  currency: string;
  
  // Reason and context
  reason: string; // Cancellation reason provided by initiator
  explanation?: string; // Detailed explanation (required for some types)
  evidenceUrls?: string[]; // Supporting evidence (photos, documents)
  
  // Related records
  paymentTransactionIds: string[]; // Related payment transactions
  creditTransactionIds: string[]; // Related credit transactions
  operatorPenaltyId?: string; // Related operator penalty record
  
  // Processing details
  processedAt?: Date; // When cancellation was fully processed
  processedBy?: string; // Admin who processed (if manual)
  processingNotes?: string; // Internal processing notes
  
  // Dispute information
  disputeReason?: string; // Reason for dispute
  disputedAt?: Date; // When dispute was raised
  disputedBy?: string; // Who raised the dispute
  disputeResolution?: string; // How dispute was resolved
  disputeResolvedAt?: Date; // When dispute was resolved
  
  // Metadata
  metadata: {
    clientNotified?: boolean; // Whether client was notified
    operatorNotified?: boolean; // Whether operator was notified
    ratingImpactApplied?: boolean; // Whether rating penalty was applied
    jobReposted?: boolean; // Whether job was reposted
    rescheduleOffered?: boolean; // Whether reschedule was offered
    originalJobData?: any; // Snapshot of original job data
    systemFlags?: string[]; // System flags for special handling
  };
  
  createdAt: Date;
  updatedAt: Date;
}

const cancellationRecordSchema = new mongoose.Schema<ICancellationRecord>({
  jobId: {
    type: String,
    required: true,
    ref: 'Job',
    index: true
  },
  bidId: {
    type: String,
    ref: 'Bid',
    index: true
  },
  initiatedBy: {
    type: String,
    required: true,
    ref: 'Account',
    index: true
  },
  initiatorRole: {
    type: String,
    required: true,
    enum: ['client', 'operator', 'admin', 'system'],
    index: true
  },
  type: {
    type: String,
    required: true,
    enum: [
      'operator_before_start',
      'client_less_than_24h', 
      'client_after_start',
      'operator_after_arrival',
      'system_no_show'
    ],
    index: true
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'processing', 'completed', 'failed', 'disputed'],
    default: 'pending',
    index: true
  },
  scheduledStartTime: {
    type: Date,
    required: true,
    index: true
  },
  cancellationTime: {
    type: Date,
    required: true,
    default: Date.now,
    index: true
  },
  hoursBeforeStart: {
    type: Number,
    required: true
  },
  originalAmount: {
    type: Number,
    required: true,
    min: 0
  },
  penaltyAmount: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  refundAmount: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  adminShare: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  operatorShare: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  currency: {
    type: String,
    required: true,
    enum: ['DOP', 'USD'],
    default: 'DOP'
  },
  reason: {
    type: String,
    required: true,
    maxlength: 500
  },
  explanation: {
    type: String,
    maxlength: 2000
  },
  evidenceUrls: [{
    type: String // S3 URLs
  }],
  paymentTransactionIds: [{
    type: String,
    ref: 'PaymentTransaction'
  }],
  creditTransactionIds: [{
    type: String,
    ref: 'CreditTransaction'
  }],
  operatorPenaltyId: {
    type: String,
    ref: 'OperatorPenalty'
  },
  processedAt: {
    type: Date,
    index: true
  },
  processedBy: {
    type: String,
    ref: 'Account'
  },
  processingNotes: {
    type: String,
    maxlength: 1000
  },
  disputeReason: {
    type: String,
    maxlength: 1000
  },
  disputedAt: {
    type: Date
  },
  disputedBy: {
    type: String,
    ref: 'Account'
  },
  disputeResolution: {
    type: String,
    maxlength: 1000
  },
  disputeResolvedAt: {
    type: Date
  },
  metadata: {
    clientNotified: { type: Boolean, default: false },
    operatorNotified: { type: Boolean, default: false },
    ratingImpactApplied: { type: Boolean, default: false },
    jobReposted: { type: Boolean, default: false },
    rescheduleOffered: { type: Boolean, default: false },
    originalJobData: { type: mongoose.Schema.Types.Mixed },
    systemFlags: [{ type: String }]
  }
}, {
  timestamps: true
});

// Compound indexes for efficient querying
cancellationRecordSchema.index({ jobId: 1, type: 1 });
cancellationRecordSchema.index({ initiatedBy: 1, createdAt: -1 });
cancellationRecordSchema.index({ type: 1, status: 1 });
cancellationRecordSchema.index({ scheduledStartTime: 1, cancellationTime: 1 });

export default mongoose.model<ICancellationRecord>('CancellationRecord', cancellationRecordSchema);
