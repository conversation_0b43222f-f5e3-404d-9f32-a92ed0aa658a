import mongoose, { Document, Schema } from 'mongoose';

// RATE_SCALE controls the fixed-point scaling used when converting a decimal tax rate
// to an integer numerator for precise integer arithmetic. Default is 1_000_000
// (six decimal places). It can be overridden via the TAX_RATE_SCALE environment
// variable for deployments that require different precision.
const RATE_SCALE: number = (() => {
  const env = process.env.TAX_RATE_SCALE;
  if (!env) return 1_000_000;
  const parsed = parseInt(env, 10);
  return Number.isInteger(parsed) && parsed > 0 ? parsed : 1_000_000;
})();

export interface ITax extends Document {
  id: string;
  jobId?: string;
  bidId?: string;
  subjectType: 'commission' | 'operator';
  subjectId?: string; // operatorId for operator tax; optional for commission
  rate: number; // percentage as decimal (e.g., 0.18)
  baseAmount: number; // amount in minor units used to compute tax
  taxAmount: number; // computed tax in minor units
  currency: 'DOP' | 'USD';
  metadata?: Record<string, any>;
  // If a manual override was applied to taxAmount, this flag is true
  manualTaxOverride?: boolean;
  taxOverrideMeta?: {
    original?: number;
    expected?: number;
    changedAt?: Date;
    changedBy?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const taxSchema = new mongoose.Schema<ITax>({
  jobId: { type: String, ref: 'Job', index: true },
  bidId: { type: String, ref: 'Bid', index: true },
  subjectType: { type: String, enum: ['commission', 'operator'], required: true, index: true },
  subjectId: { type: String, ref: 'Account', index: true },
  rate: { type: Number, required: true, min: 0, max: 1 },
  baseAmount: { type: Number, required: true, min: 0 },
  taxAmount: { type: Number, required: true, min: 0 },
  currency: { type: String, enum: ['DOP', 'USD'], default: 'DOP', required: true },
  metadata: { type: Schema.Types.Mixed, default: {} },
  // If caller intends to intentionally override the computed tax, set this flag to true
  // and the hook will accept the provided rounded value and record audit info in `taxOverrideMeta`.
  // Important: application code should set `doc._ctxUserId = '<userId>'` before save so the audit
  // record can capture who performed the override. If you prefer, populate `taxOverrideMeta.changedBy`
  // directly from application context instead of using `_ctxUserId`.
  manualTaxOverride: { type: Boolean, default: false },
  taxOverrideMeta: {
    original: { type: Number },
    expected: { type: Number },
    changedAt: { type: Date },
    changedBy: { type: String }
  }
}, { timestamps: true, toObject: { virtuals: true }, toJSON: { virtuals: true } });

// Validation/coercion hook
taxSchema.pre('validate', function (next) {
  const doc = this as ITax;
  try {
    // XOR: exactly one of jobId or bidId must be present
    const hasJob = !!doc.jobId;
    const hasBid = !!doc.bidId;
    if (hasJob === hasBid) {
      return next(new Error('Exactly one of jobId or bidId must be provided'));
    }

    // subjectId must be present when subjectType === 'operator'
    if (doc.subjectType === 'operator' && !doc.subjectId) {
      return next(new Error('subjectId is required when subjectType is "operator"'));
    }

    // rate must be within [0,1]
    if (typeof doc.rate !== 'number' || doc.rate < 0 || doc.rate > 1) {
      return next(new Error('rate must be a number between 0 and 1'));
    }

    // Ensure baseAmount is integer minor units; coerce by rounding and validate
    if (typeof doc.baseAmount !== 'number' || Number.isNaN(doc.baseAmount) || doc.baseAmount < 0) {
      return next(new Error('baseAmount must be a non-negative number in minor units'));
    }
    doc.baseAmount = Math.round(doc.baseAmount);

  // Compute expected tax using integer-scaled arithmetic to avoid floating-point precision issues.
  // We scale the rate to an integer numerator with a fixed denominator (RATE_SCALE) and
  // perform integer math: expectedTax = round(baseAmount * (rateNumerator / RATE_SCALE)).
  const rateNumerator = Math.round(doc.rate * RATE_SCALE);
  const expectedTax = Math.round((doc.baseAmount * rateNumerator) / RATE_SCALE);

    // Handle taxAmount: coerce/round the incoming value first
    const originalTaxProvided = doc.taxAmount;

    if (originalTaxProvided === undefined || originalTaxProvided === null) {
      // No provided value: set the expected tax
      doc.taxAmount = expectedTax;
    } else {
      // Provided a value: ensure it's a finite non-negative number
      if (typeof originalTaxProvided !== 'number' || Number.isNaN(originalTaxProvided) || !Number.isFinite(originalTaxProvided) || originalTaxProvided < 0) {
        return next(new Error('taxAmount must be a finite non-negative number in minor units'));
      }

      // Round the provided value to integer minor units
      const roundedProvided = Math.round(originalTaxProvided);

      if (roundedProvided === expectedTax) {
        // Matches expected after rounding — accept it
        doc.taxAmount = roundedProvided;
      } else {
        // Mismatch between provided and expected
        if (doc.manualTaxOverride === true) {
          // Accept the provided rounded value but record audit metadata and warn
          doc.taxAmount = roundedProvided;
          try {
            const changedBy = (doc as any)._ctxUserId || null;
            doc.taxOverrideMeta = {
              original: roundedProvided,
              expected: expectedTax,
              changedAt: new Date(),
              changedBy
            } as any;
            console.warn(`Tax manual override: jobId=${doc.jobId || ''} bidId=${doc.bidId || ''} provided=${roundedProvided} expected=${expectedTax} changedBy=${changedBy}`);
          } catch (e) {
            // Don't fail the save because of logging/audit issues; just warn
            console.warn('Failed to write taxOverrideMeta audit info', e);
          }
        } else {
          // Reject the save to avoid silent data loss; require explicit override
          return next(new Error(`taxAmount mismatch: provided ${roundedProvided} vs expected ${expectedTax}. To accept, set manualTaxOverride=true and include audit meta.`));
        }
      }
    }

    return next();
  } catch (err) {
    return next(err as any);
  }
});

// Indexes
taxSchema.index({ subjectType: 1, createdAt: -1 });
taxSchema.index({ currency: 1, createdAt: -1 });

export default mongoose.model<ITax>('Tax', taxSchema);

