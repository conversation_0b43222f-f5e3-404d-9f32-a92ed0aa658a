import axios, { AxiosInstance, AxiosResponse } from 'axios';
import crypto from 'crypto';
import https from 'https';
import fs from 'fs';
import path from 'path';
import { getCardBrand } from '../utils/cardBrandDetection';
import { logger } from '../utils/logger';
import {
  AzulCredentials,
  ProcessDataVaultRequest,
  ProcessDataVaultResponse,
  ProcessPaymentRequest,
  ProcessPaymentResponse,
  ProcessThreeDSMethodRequest,
  ProcessThreeDSMethodResponse,
  ProcessThreeDSChallengeRequest,
  ProcessThreeDSChallengeResponse,
  RefundRequest,
  RefundResponse,
  ProcessPostRequest,
  ProcessPostResponse,
  AZUL_ERROR_CODES
} from '../types/azul';

export class AzulClient {
  private credentials: AzulCredentials;
  private httpClient: AxiosInstance;
  private fallbackHttpClient?: AxiosInstance;

  constructor(environment: 'sandbox' | 'production' = 'sandbox') {
    // Load credentials from environment variables
    this.credentials = this.loadCredentials(environment);

    // Create HTTP clients with retry and timeout configuration
    this.httpClient = this.createHttpClient(this.credentials.primaryEndpoint!);

    if (this.credentials.secondaryEndpoint) {
      this.fallbackHttpClient = this.createHttpClient(this.credentials.secondaryEndpoint);
    }
  }

  private loadCredentials(environment: 'sandbox' | 'production'): AzulCredentials {
    const prefix = environment === 'production' ? 'AZUL_PROD' : 'AZUL_SANDBOX';

    const credentials = {
      merchantId: process.env[`${prefix}_MERCHANT_ID`],
      auth1: process.env[`${prefix}_AUTH1`],
      auth2: process.env[`${prefix}_AUTH2`],
      primaryEndpoint: process.env[`${prefix}_PRIMARY_ENDPOINT`],
      secondaryEndpoint: process.env[`${prefix}_SECONDARY_ENDPOINT`]
    };

    if (!credentials.merchantId || !credentials.auth1 || !credentials.auth2 || !credentials.primaryEndpoint) {
      throw new Error(`Missing required Azul ${environment} credentials in environment variables`);
    }

    return credentials;
  }

  private createHttpClient(baseURL: string): AxiosInstance {
    const certsPath = process.env.AZUL_CERTS_PATH || path.resolve(process.cwd(), 'certs/azul');

    let httpsAgent: https.Agent;

    try {
      // Check if certificates exist
      const clientCert = path.join(certsPath, 'local.pem');
      const privateKey = path.join(certsPath, 'private.key');
      const intermediateCert = path.join(certsPath, 'intermediate.pem');
      const rootCert = path.join(certsPath, 'root.pem');

      if (fs.existsSync(clientCert) && fs.existsSync(privateKey)) {
        logger.info('Loading Azul mTLS certificates for authentication');

        // Read and validate certificate files safely
        let clientCertBuf: Buffer;
        let privateKeyBuf: Buffer;
        const caChain: Buffer[] = [];

        try {
          clientCertBuf = fs.readFileSync(clientCert);
          if (!clientCertBuf || clientCertBuf.length === 0) {
            throw new Error(`Client certificate file is empty: ${clientCert}`);
          }
        } catch (readErr) {
          logger.error('Failed to read client certificate file', { path: clientCert, error: readErr instanceof Error ? readErr.message : String(readErr) });
          throw readErr;
        }

        try {
          privateKeyBuf = fs.readFileSync(privateKey);
          if (!privateKeyBuf || privateKeyBuf.length === 0) {
            throw new Error(`Private key file is empty: ${privateKey}`);
          }
        } catch (readErr) {
          logger.error('Failed to read private key file', { path: privateKey, error: readErr instanceof Error ? readErr.message : String(readErr) });
          throw readErr;
        }

        // Build CA chain (intermediate + root) if present
        try {
          if (fs.existsSync(intermediateCert)) {
            const buf = fs.readFileSync(intermediateCert);
            if (!buf || buf.length === 0) throw new Error(`Intermediate cert is empty: ${intermediateCert}`);
            caChain.push(buf);
          }
          if (fs.existsSync(rootCert)) {
            const buf = fs.readFileSync(rootCert);
            if (!buf || buf.length === 0) throw new Error(`Root cert is empty: ${rootCert}`);
            caChain.push(buf);
          }
        } catch (readErr) {
          logger.error('Failed to read CA certificate file', { error: readErr instanceof Error ? readErr.message : String(readErr) });
          throw readErr;
        }

        // Ensure we have at least one CA in the chain; if not, fail early to avoid passing an empty CA array
        if (caChain.length === 0) {
          const msg = `No CA certificates found in ${certsPath}; aborting mTLS setup`;
          logger.error(msg, { certsPath });
          throw new Error(msg);
        }

        httpsAgent = new https.Agent({
          cert: clientCertBuf,          // Client certificate from Azul
          key: privateKeyBuf,           // Private key from CSR
          ca: caChain,                  // Certificate authority chain
          rejectUnauthorized: process.env.NODE_ENV === 'development' ? false : true, // Only disable in development
          keepAlive: true,             // Performance optimization
          secureProtocol: 'TLSv1_2_method' // TLS 1.2+ required
        });

        logger.info('mTLS certificates loaded successfully');
      } else {
        logger.warn('mTLS certificates not found, using standard TLS');
        httpsAgent = new https.Agent({
          secureProtocol: 'TLSv1_2_method',
          rejectUnauthorized: true
        });
      }
    } catch (error) {
      logger.error('Error loading mTLS certificates', { error: error instanceof Error ? error.message : String(error) });
      // Fallback to standard TLS
      httpsAgent = new https.Agent({
        secureProtocol: 'TLSv1_2_method',
        rejectUnauthorized: true
      });
    }

    return axios.create({
      baseURL,
      timeout: 120000, // 120 seconds as recommended by Azul
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Manito-Payment-Service/1.0'
      },
      httpsAgent
    });
  }

  /**
   * Generate Azul AuthHash using HMAC-SHA512 over UTF-16LE encoded auth input + AuthKey bytes.
   * Azul requires concatenating fields in the exact order defined by their docs (no separators),
   * encoding the concatenated string as UTF-16LE, appending the AuthKey bytes, and computing
   * HMAC-SHA512 over that byte sequence. The resulting digest is returned as hex.
   */
  private generateAuthHash(data: any): string {
    // Build auth input by concatenating fields in the exact order required by Azul.
    // IMPORTANT: The exact ordering depends on the request type; here we'll use a deterministic
    // order based on the keys array from Azul's spec. For generic requests, we will use the
    // documented field order when available. As a fallback, use Object.keys in insertion order.
    const fieldOrder: string[] = Array.isArray(data.__authFieldOrder) ? data.__authFieldOrder : Object.keys(data);

    // Concatenate values with no separator in the specified order. Skip undefined/null.
    let concatStr = '';
    for (const key of fieldOrder) {
      if (key === '__authFieldOrder') continue; // internal helper
      const val = data[key];
      if (val === undefined || val === null) continue;
      // For nested objects/arrays use JSON.stringify to produce a stable representation
      concatStr += (typeof val === 'object') ? JSON.stringify(val) : String(val);
    }

    // Encode concatenated string as UTF-16LE bytes
    const concatBuf = Buffer.from(concatStr, 'utf16le');

    // Append AuthKey bytes (auth2 used as AuthKey per Azul HMAC flow)
    const authKeyBuf = Buffer.from(this.credentials.auth2!, 'utf8');
    const payloadBuf = Buffer.concat([concatBuf, authKeyBuf]);

    // Compute HMAC-SHA512 over payloadBuf using auth1 as the HMAC key (constant-time by Node crypto)
    const hmac = crypto.createHmac('sha512', Buffer.from(this.credentials.auth1!, 'utf8'));
    hmac.update(payloadBuf);
    const digestHex = hmac.digest('hex');

    return digestHex;
  }

  private sortObjectKeys(obj: any): any {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sortObjectKeys(item));
    }

    const sorted: any = {};
    Object.keys(obj)
      .sort()
      .forEach(key => {
        sorted[key] = this.sortObjectKeys(obj[key]);
      });

    return sorted;
  }

  private async makeRequest<T>(
    endpoint: string,
    data: any,
    retryCount = 0
  ): Promise<T> {
    const requestData = {
      ...data,
      MerchantId: this.credentials.merchantId!
    };

    // Compute Azul AuthHash per HMAC-SHA512 flow and attach it to the payload
    const authHash = this.generateAuthHash(requestData);
    // Attach AuthHash field expected by Azul
    (requestData as any).AuthHash = authHash;

    try {
  const response: AxiosResponse<T> = await this.httpClient.post(endpoint, requestData);

      return response.data;
    } catch (error) {
      // Structured logging (avoid sensitive data)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      // Extract requestId if present in request payload
      const requestId = (requestData && (requestData.requestId || requestData.request_id)) || 'unknown';
      logger.error('Azul API request failed', {
        endpoint,
        retryCount,
        requestId,
        error: String(errorMessage).replace(/\s+/g, ' ').trim()
      });

      // Try fallback endpoint if available and we haven't retried yet
      if (this.fallbackHttpClient && retryCount === 0) {
        logger.info('Attempting fallback endpoint', { endpoint, retryCount, requestId });
        try {
          const response: AxiosResponse<T> = await this.fallbackHttpClient.post(endpoint, requestData);
          return response.data;
        } catch (fallbackError) {
          const fallbackErrorMessage = fallbackError instanceof Error ? fallbackError.message : 'Unknown fallback error';
          logger.error('Azul fallback request failed', {
            endpoint,
            retryCount,
            requestId,
            error: String(fallbackErrorMessage).replace(/\s+/g, ' ').trim()
          });
        }
      }

      // Retry logic for transient errors with exponential backoff + jitter to avoid thundering herds
      if (retryCount < 3 && this.isRetryableError(error)) {
        const MAX_DELAY_MS = 30000;
        const baseDelay = Math.pow(2, retryCount) * 1000; // base exponential delay
        const jitter = Math.floor(Math.random() * baseDelay); // jitter up to baseDelay
        const delay = Math.min(baseDelay + jitter, MAX_DELAY_MS);
        logger.info('Retrying Azul request', { delay, baseDelay, jitter });
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.makeRequest(endpoint, data, retryCount + 1);
      }

      throw new Error(`Azul API request failed: ${errorMessage}`);
    }
  }

  private isRetryableError(error: any): boolean {
    // Network errors, timeouts, 5xx server errors are retryable
    if (error?.code) {
      return (
        error.code === 'ECONNRESET' ||
        error.code === 'ETIMEDOUT' ||
        error.code === 'ENOTFOUND'
      );
    }

    if (error?.response?.status) {
      return error.response.status >= 500;
    }

    return false;
  }

  /**
   * Create a new Data Vault token
   */
  async createDataVaultToken(request: {
    cardNumber: string;
    cvc?: string;
    expiration: string; // MMYY format
    cardHolderName?: string;
  }): Promise<ProcessDataVaultResponse> {
    const data = {
      TrxType: 'CREATE' as const,
      CardNumber: request.cardNumber,
      Expiration: request.expiration,
      ...(request.cvc && { CVC: request.cvc }),
      ...(request.cardHolderName && { CardHolderName: request.cardHolderName })
    };

    return this.makeRequest<ProcessDataVaultResponse>('/ProcessDatavault', data);
  }

  /**
   * Delete a Data Vault token
   */
  async deleteDataVaultToken(dataVaultToken: string): Promise<ProcessDataVaultResponse> {
    const data = {
      TrxType: 'DELETE' as const,
      DataVaultToken: dataVaultToken
    };

    return this.makeRequest<ProcessDataVaultResponse>('/ProcessDatavault', data);
  }

  /**
   * Process a payment using Data Vault token
   */
  async processPayment(request: ProcessPaymentRequest): Promise<ProcessPaymentResponse> {
    return this.makeRequest<ProcessPaymentResponse>('/ProcessPayment', request);
  }

  /**
   * Process 3DS Method notification
   */
  async processThreeDSMethod(request: ProcessThreeDSMethodRequest): Promise<ProcessThreeDSMethodResponse> {
    return this.makeRequest<ProcessThreeDSMethodResponse>('/ProcessThreeDSMethod', request);
  }

  /**
   * Process 3DS Challenge response
   */
  async processThreeDSChallenge(request: ProcessThreeDSChallengeRequest): Promise<ProcessThreeDSChallengeResponse> {
    return this.makeRequest<ProcessThreeDSChallengeResponse>('/ProcessThreeDSChallenge', request);
  }

  /**
   * Process refund
   */
  async processRefund(request: RefundRequest): Promise<RefundResponse> {
    return this.makeRequest<RefundResponse>('/Refund', request);
  }

  /**
   * Process post-authorization (capture/void)
   */
  async processPost(request: ProcessPostRequest): Promise<ProcessPostResponse> {
    return this.makeRequest<ProcessPostResponse>('/ProcessPost', request);
  }

  /**
   * Validate Azul response and check for errors
   */
  static validateResponse(response: any): { isSuccess: boolean; errorMessage?: string } {
    if (!response) {
      return { isSuccess: false, errorMessage: 'No response received from Azul' };
    }

    // Check ResponseCode first
    if (response.ResponseCode && response.ResponseCode !== '00') {
      const errorMessage = AZUL_ERROR_CODES[response.ResponseCode as keyof typeof AZUL_ERROR_CODES]
        || response.ErrorDescription
        || `Unknown error code: ${response.ResponseCode}`;
      return { isSuccess: false, errorMessage };
    }

    // Check IsoCode for payment responses
    if (response.IsoCode) {
      if (response.IsoCode === '00') {
        return { isSuccess: true };
      } else if (response.IsoCode === '3D' || response.IsoCode === '3D2METHOD') {
        // 3DS required is not an error, it's a flow continuation
        return { isSuccess: true };
      } else {
        const errorMessage = AZUL_ERROR_CODES[response.IsoCode as keyof typeof AZUL_ERROR_CODES]
          || response.ResponseMessage
          || `Transaction declined with code: ${response.IsoCode}`;
        return { isSuccess: false, errorMessage };
      }
    }

    return { isSuccess: true };
  }

  /**
   * Generate unique Azul Order ID
   */
  static generateOrderId(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8);
    return `MNT-${timestamp}-${random}`.toUpperCase();
  }

  /**
   * Format expiration date for Azul (MMYY)
   */
  static formatExpiration(month: number, year: number): string {
    const mm = month.toString().padStart(2, '0');
    const yy = year.toString().slice(-2);
    return `${mm}${yy}`;
  }

  /**
   * Parse card brand from card number
   */
  static getCardBrand(cardNumber: string): string {
    return getCardBrand(cardNumber);
  }

  /**
   * Mask card number for storage
   */
  static maskCardNumber(cardNumber: string): string {
    // Preserve original formatting (spaces/dashes) while masking digits.
    const original = cardNumber || '';
    const digitPositions: number[] = [];
    for (let i = 0; i < original.length; i++) {
      if (/[0-9]/.test(original[i])) digitPositions.push(i);
    }

    const totalDigits = digitPositions.length;
    // If there are 4 or fewer digits, don't mask (return original formatting)
    if (totalDigits <= 4) return original;

    // Determine which digit positions correspond to the last four digits
    const lastFourPositions = new Set<number>(digitPositions.slice(-4));

    // Build masked string: replace digit characters with '*' unless they are in lastFourPositions
    let result = '';
    for (let i = 0; i < original.length; i++) {
      const ch = original[i];
      if (/[0-9]/.test(ch)) {
        if (lastFourPositions.has(i)) {
          result += ch;
        } else {
          result += '*';
        }
      } else {
        result += ch;
      }
    }

    return result;
  }

  /**
   * Validate card number using Luhn algorithm
   */
  static validateCardNumber(cardNumber: string): boolean {
    const cleaned = cardNumber.replace(/\D/g, '');

    if (cleaned.length < 13 || cleaned.length > 19) {
      return false;
    }

    let sum = 0;
    let isEven = false;

    for (let i = cleaned.length - 1; i >= 0; i--) {
      let digit = parseInt(cleaned[i], 10);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    return sum % 10 === 0;
  }
}

export default AzulClient;
