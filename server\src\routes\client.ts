import express from 'express';
import { tryCatch } from '../middleware';
import { getClientReputation } from '../controllers/client';

const router = express.Router();

// Public reputation summary for a client
router.get(
  '/:clientId/reputation',
  (req, res, next) => {
    const { clientId } = req.params;
    if (
      !/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|[0-9a-f]{24})$/i.test(
        clientId
      )
    ) {
      return res.status(400).json({ error: 'Invalid clientId' });
    }
    next();
  },
  tryCatch(getClientReputation)
);
export default router;

