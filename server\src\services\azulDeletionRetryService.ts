import { AzulDeletionRetryJob } from '../database/schemas';
import { IAzulDeletionRetryJob } from '../database/schemas/_azulDeletionRetryJob';
import { AzulClient } from './azulClient';
import crypto from 'crypto';

export interface RetryJobOptions {
  paymentMethodId: string;
  dataVaultToken: string;
  userId: string;
  maxAttempts?: number;
  baseDelayMs?: number;
  metadata?: {
    originalDeletedAt?: Date;
    paymentMethodType?: string;
  };
}

export class AzulDeletionRetryService {
  private azulClient: AzulClient;
  private processingInterval: NodeJS.Timeout | null = null;
  private isProcessing = false;
  
  // Configurable settings
  private readonly DEFAULT_MAX_ATTEMPTS = 5;
  private readonly DEFAULT_BASE_DELAY_MS = 5000; // 5 seconds
  private readonly MAX_DELAY_MS = 300000; // 5 minutes max
  private readonly PROCESSING_INTERVAL_MS = 30000; // Check every 30 seconds
  private readonly ENCRYPTION_KEY = process.env.AZUL_TOKEN_ENCRYPTION_KEY || 'default-key-change-in-production';
  
  constructor(environment: 'sandbox' | 'production' = 'sandbox') {
    this.azulClient = new AzulClient(environment);
  }

  /**
   * Encrypt sensitive token data for storage
   */
  private encryptToken(token: string): string {
    try {
      const algorithm = 'aes-256-gcm';
      const key = crypto.scryptSync(this.ENCRYPTION_KEY, 'salt', 32);
      const iv = crypto.randomBytes(16);
      
      const cipher = crypto.createCipheriv(algorithm, key, iv);
      cipher.setAAD(Buffer.from('azul-token', 'utf8'));
      
      let encrypted = cipher.update(token, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      
      return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
    } catch (error) {
      console.error('Error encrypting token:', error);
      // Fallback to base64 encoding if encryption fails (not secure, but better than plaintext)
      return Buffer.from(token).toString('base64');
    }
  }

  /**
   * Decrypt token data for use
   */
  private decryptToken(encryptedToken: string): string {
    try {
      const algorithm = 'aes-256-gcm';
      const key = crypto.scryptSync(this.ENCRYPTION_KEY, 'salt', 32);
      
      const parts = encryptedToken.split(':');
      if (parts.length !== 3) {
        // Fallback for base64 encoded tokens
        return Buffer.from(encryptedToken, 'base64').toString('utf8');
      }
      
      const [ivHex, authTagHex, encrypted] = parts;
      const iv = Buffer.from(ivHex, 'hex');
      const authTag = Buffer.from(authTagHex, 'hex');
      
      const decipher = crypto.createDecipheriv(algorithm, key, iv);
      decipher.setAAD(Buffer.from('azul-token', 'utf8'));
      decipher.setAuthTag(authTag);
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      console.error('Error decrypting token:', error);
      // Fallback for base64 encoded tokens
      try {
        return Buffer.from(encryptedToken, 'base64').toString('utf8');
      } catch {
        throw new Error('Unable to decrypt token');
      }
    }
  }

  /**
   * Add a new retry job for failed Azul deletion
   */
  async enqueueRetry(options: RetryJobOptions): Promise<void> {
    try {
      const maxAttempts = options.maxAttempts || this.DEFAULT_MAX_ATTEMPTS;
      const baseDelayMs = options.baseDelayMs || this.DEFAULT_BASE_DELAY_MS;
      const nextRetryAt = new Date(Date.now() + baseDelayMs);
      
      const encryptedToken = this.encryptToken(options.dataVaultToken);
      
      // Use upsert to handle potential race conditions
      await AzulDeletionRetryJob.findOneAndUpdate(
        { paymentMethodId: options.paymentMethodId, status: { $in: ['pending', 'processing'] } },
        {
          $setOnInsert: {
            paymentMethodId: options.paymentMethodId,
            dataVaultToken: encryptedToken,
            userId: options.userId,
            attemptCount: 0,
            maxAttempts,
            baseDelayMs,
            nextRetryAt,
            status: 'pending',
            metadata: options.metadata || {}
          }
        },
        { upsert: true, new: true }
      );
      
      console.log('Enqueued Azul deletion retry job', {
        paymentMethodId: options.paymentMethodId,
        userId: options.userId,
        maxAttempts,
        nextRetryAt: nextRetryAt.toISOString()
      });
    } catch (error) {
      console.error('Failed to enqueue Azul deletion retry job:', error);
      throw error;
    }
  }

  /**
   * Process pending retry jobs
   */
  async processRetryJobs(): Promise<void> {
    if (this.isProcessing) {
      return; // Prevent concurrent processing
    }
    
    this.isProcessing = true;
    
    try {
      const now = new Date();
      
      // Find jobs ready for retry
      const pendingJobs = await AzulDeletionRetryJob.find({
        status: 'pending',
        nextRetryAt: { $lte: now }
      }).select('+dataVaultToken').limit(10); // Process in batches
      
      for (const job of pendingJobs) {
        await this.processRetryJob(job);
      }
    } catch (error) {
      console.error('Error processing retry jobs:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process a single retry job
   */
  private async processRetryJob(job: IAzulDeletionRetryJob): Promise<void> {
    try {
      // Mark job as processing
      job.status = 'processing';
      await job.save();
      
      // Decrypt token for API call
      const dataVaultToken = this.decryptToken(job.dataVaultToken);
      
      // Attempt Azul deletion
      const azulResponse = await this.azulClient.deleteDataVaultToken(dataVaultToken);
      const validation = AzulClient.validateResponse(azulResponse);
      
      if (validation.isSuccess) {
        // Success - mark job as succeeded
        job.status = 'succeeded';
        job.completedAt = new Date();
        await job.save();
        
        console.log('Azul deletion retry succeeded', {
          jobId: String(job._id),
          paymentMethodId: job.paymentMethodId,
          attemptCount: job.attemptCount + 1
        });
        
      } else {
        // Failed - handle retry or permanent failure
        await this.handleRetryFailure(job, validation.errorMessage || 'Unknown error');
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.handleRetryFailure(job, errorMessage);
    }
  }

  /**
   * Handle retry failure - either schedule next retry or mark as permanently failed
   */
  private async handleRetryFailure(job: IAzulDeletionRetryJob, errorMessage: string): Promise<void> {
    job.attemptCount += 1;
    job.lastError = errorMessage;
    
    // Add to error history
    if (!job.metadata) job.metadata = {};
    if (!job.metadata.errorHistory) job.metadata.errorHistory = [];
    
    job.metadata.errorHistory.push({
      attempt: job.attemptCount,
      error: errorMessage,
      timestamp: new Date()
    });
    
    if (job.attemptCount >= job.maxAttempts) {
      // Permanent failure
      job.status = 'failed';
      job.completedAt = new Date();
      
      console.error('Azul deletion permanently failed after max attempts', {
        jobId: String(job._id),
        paymentMethodId: job.paymentMethodId,
        userId: job.userId,
        attemptCount: job.attemptCount,
        maxAttempts: job.maxAttempts,
        lastError: errorMessage
      });
      
      // TODO: Send alert/notification to operations team
      // This could be integrated with existing alerting systems
      
    } else {
      // Schedule next retry with exponential backoff
      const backoffDelay = Math.min(
        job.baseDelayMs * Math.pow(2, job.attemptCount - 1),
        this.MAX_DELAY_MS
      );
      
      job.nextRetryAt = new Date(Date.now() + backoffDelay);
      job.status = 'pending';
      
      console.warn('Azul deletion retry failed, scheduling next attempt', {
        jobId: String(job._id),
        paymentMethodId: job.paymentMethodId,
        attemptCount: job.attemptCount,
        nextRetryAt: job.nextRetryAt.toISOString(),
        error: errorMessage
      });
    }
    
    await job.save();
  }

  /**
   * Start the retry processor (runs periodically)
   */
  startProcessor(): void {
    if (this.processingInterval) {
      return; // Already started
    }
    
    console.log('Starting Azul deletion retry processor');
    
    // Process immediately and then on interval
    this.processRetryJobs();
    
    this.processingInterval = setInterval(() => {
      this.processRetryJobs();
    }, this.PROCESSING_INTERVAL_MS);
  }

  /**
   * Stop the retry processor
   */
  stopProcessor(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
      console.log('Stopped Azul deletion retry processor');
    }
  }

  /**
   * Get retry job statistics
   */
  async getRetryStats(): Promise<{
    pending: number;
    processing: number;
    succeeded: number;
    failed: number;
  }> {
    const stats = await AzulDeletionRetryJob.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);
    
    const result = {
      pending: 0,
      processing: 0,
      succeeded: 0,
      failed: 0
    };
    
    stats.forEach(stat => {
      if (stat._id in result) {
        result[stat._id as keyof typeof result] = stat.count;
      }
    });
    
    return result;
  }
}

// Global instance - can be configured as singleton
let retryServiceInstance: AzulDeletionRetryService | null = null;

export function getAzulDeletionRetryService(environment?: 'sandbox' | 'production'): AzulDeletionRetryService {
  if (!retryServiceInstance) {
    const env = environment || (process.env.NODE_ENV === 'production' ? 'production' : 'sandbox');
    retryServiceInstance = new AzulDeletionRetryService(env);
  }
  return retryServiceInstance;
}
