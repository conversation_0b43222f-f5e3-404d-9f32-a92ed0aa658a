"use strict";
// Azul API Types and Interfaces
Object.defineProperty(exports, "__esModule", { value: true });
exports.AZUL_ERROR_CODES = void 0;
// Azul Error Codes
exports.AZUL_ERROR_CODES = {
    // Success
    '00': 'Approved',
    // 3DS Codes
    '3D': '3D Secure Challenge Required',
    '3D2METHOD': '3D Secure 2.0 Method Required',
    // Decline Codes
    '05': 'Declined',
    '51': 'Insufficient Funds',
    '54': 'Expired Card',
    '57': 'Transaction Not Permitted',
    '61': 'Exceeds Withdrawal Amount Limit',
    '65': 'Exceeds Withdrawal Frequency Limit',
    // Error Codes
    '96': 'System Error',
    '30': 'Format Error',
    '12': 'Invalid Transaction',
    '13': 'Invalid Amount',
    '14': 'Invalid Card Number',
    '15': 'Invalid Issuer',
};
