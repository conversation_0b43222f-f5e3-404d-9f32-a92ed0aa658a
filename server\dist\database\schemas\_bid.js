"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const bidSchema = new mongoose_1.default.Schema({
    jobId: {
        type: String,
        required: true,
        ref: 'Job'
    },
    operatorId: {
        type: String,
        required: true,
        ref: 'Account'
    },
    amount: {
        type: Number,
        required: true,
        min: 0
    },
    message: {
        type: String,
        required: true,
        maxlength: 1000
    },
    estimatedDuration: {
        type: String,
        required: true
    },
    status: {
        type: String,
        enum: ['pending', 'accepted', 'rejected', 'withdrawn'],
        default: 'pending'
    },
    proposedStartDate: {
        type: String,
        required: true,
        match: /^\d{2}\/\d{2}\/\d{4}$/ // DD/MM/YYYY format
    },
    proposedStartTime: {
        type: String,
        required: true,
        match: /^\d{2}:\d{2}$/ // HH:MM format
    }
}, {
    timestamps: true
});
// Compound index to prevent duplicate bids from same operator on same job
bidSchema.index({ jobId: 1, operatorId: 1 }, { unique: true });
bidSchema.index({ jobId: 1, status: 1 });
bidSchema.index({ operatorId: 1, createdAt: -1 });
bidSchema.index({ status: 1 });
exports.default = mongoose_1.default.model('Bid', bidSchema);
