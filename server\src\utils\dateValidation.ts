/**
 * Date and time validation utilities for chat scheduling
 */

export interface DateTimeValidation {
  isValid: boolean;
  error?: string;
  parsed?: {
    date: string;
    hour: string;
  };
}

/**
 * Parse datetime from various formats including frontend datetime picker
 */
export function parseDateTimeFromInput(datetimeInput: string): { date?: string; hour?: string; isValid: boolean; error?: string } {
  if (!datetimeInput || datetimeInput.trim() === '') {
    return { isValid: false, error: 'Datetime input is empty' };
  }

  const input = datetimeInput.trim();
  
  // Try to parse ISO format first (from datetime picker)
  try {
    const isoDate = new Date(input);
    if (!isNaN(isoDate.getTime())) {
      const day = String(isoDate.getDate()).padStart(2, '0');
      const month = String(isoDate.getMonth() + 1).padStart(2, '0');
      const year = isoDate.getFullYear();
      const hours = String(isoDate.getHours()).padStart(2, '0');
      const minutes = String(isoDate.getMinutes()).padStart(2, '0');
      
      return {
        date: `${day}/${month}/${year}`,
        hour: `${hours}:${minutes}`,
        isValid: true
      };
    }
  } catch (e) {
    // Continue with other parsing methods
  }
  
  // Try DD/MM/YYYY HH:MM format
  const ddmmyyyyHHmm = /^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{2})$/;
  const ddmmyyyyMatch = ddmmyyyyHHmm.exec(input);
  
  if (ddmmyyyyMatch) {
    const day = ddmmyyyyMatch[1].padStart(2, '0');
    const month = ddmmyyyyMatch[2].padStart(2, '0');
    const year = ddmmyyyyMatch[3];
    const hours = ddmmyyyyMatch[4].padStart(2, '0');
    const minutes = ddmmyyyyMatch[5];
    
    return {
      date: `${day}/${month}/${year}`,
      hour: `${hours}:${minutes}`,
      isValid: true
    };
  }
  
  // Try DD/MM/YYYY at HH:MM format
  const ddmmyyyyAtHHmm = /^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(?:at|a las)\s+(\d{1,2}):(\d{2})$/i;
  const atMatch = ddmmyyyyAtHHmm.exec(input);
  
  if (atMatch) {
    const day = atMatch[1].padStart(2, '0');
    const month = atMatch[2].padStart(2, '0');
    const year = atMatch[3];
    const hours = atMatch[4].padStart(2, '0');
    const minutes = atMatch[5];
    
    return {
      date: `${day}/${month}/${year}`,
      hour: `${hours}:${minutes}`,
      isValid: true
    };
  }
  
  return { isValid: false, error: 'Invalid datetime format. Expected: DD/MM/YYYY HH:MM or ISO format' };
}

/**
 * Validates date in DD/MM/YYYY format and ensures it's today or in the future
 */
export function validateDate(dateString: string): { isValid: boolean; error?: string } {
  const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
  
  if (!dateRegex.test(dateString)) {
    return { isValid: false, error: 'Date must be in DD/MM/YYYY format' };
  }

  const [day, month, year] = dateString.split('/').map(Number);
  
  // Check if date components are valid
  if (month < 1 || month > 12) {
    return { isValid: false, error: 'Invalid month. Must be between 01 and 12' };
  }
  
  if (day < 1 || day > 31) {
    return { isValid: false, error: 'Invalid day. Must be between 01 and 31' };
  }
  
  if (year < new Date().getFullYear()) {
    return { isValid: false, error: 'Year cannot be in the past' };
  }

  // Create date object for further validation
  const inputDate = new Date(year, month - 1, day);
  
  // Check if the date is valid (handles leap years, different month lengths)
  if (inputDate.getFullYear() !== year || 
      inputDate.getMonth() !== (month - 1) || 
      inputDate.getDate() !== day) {
    return { isValid: false, error: 'Invalid date' };
  }

  // Check if date is today or in the future
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  inputDate.setHours(0, 0, 0, 0);
  
  if (inputDate < today) {
    return { isValid: false, error: 'Date cannot be in the past' };
  }

  return { isValid: true };
}

/**
 * Validates time in HH:MM format (24-hour)
 */
export function validateTime(timeString: string): { isValid: boolean; error?: string } {
  const timeRegex = /^\d{2}:\d{2}$/;
  
  if (!timeRegex.test(timeString)) {
    return { isValid: false, error: 'Time must be in HH:MM format' };
  }

  const [hours, minutes] = timeString.split(':').map(Number);
  
  if (hours < 0 || hours > 23) {
    return { isValid: false, error: 'Hours must be between 00 and 23' };
  }
  
  if (minutes < 0 || minutes > 59) {
    return { isValid: false, error: 'Minutes must be between 00 and 59' };
  }

  return { isValid: true };
}

/**
 * Validates complete date and time combination
 */
export function validateDateTime(date: string, hour: string): DateTimeValidation {
  const dateValidation = validateDate(date);
  if (!dateValidation.isValid) {
    return { isValid: false, error: dateValidation.error };
  }

  const timeValidation = validateTime(hour);
  if (!timeValidation.isValid) {
    return { isValid: false, error: timeValidation.error };
  }

  // Check if the datetime is in the future for today's date
  const [day, month, year] = date.split('/').map(Number);
  const [hours, minutes] = hour.split(':').map(Number);
  
  const inputDateTime = new Date(year, month - 1, day, hours, minutes);
  const now = new Date();
  
  if (inputDateTime <= now) {
    return { isValid: false, error: 'Date and time must be in the future' };
  }

  return { 
    isValid: true, 
    parsed: { date, hour } 
  };
}

/**
 * Format date and time for display
 */
export function formatDateTime(date: string, hour: string): string {
  const [day, month, year] = date.split('/');
  const [hours, minutes] = hour.split(':');
  
  const dateObj = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), parseInt(hours), parseInt(minutes));
  
  return dateObj.toLocaleString('es-DO', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Extract date and time from natural language (Spanish/English)
 * For extracting user mentioned date/time from chat messages
 */
export function extractDateTimeFromMessage(message: string): { date?: string; hour?: string; confidence: 'high' | 'medium' | 'low' | 'none' } {
  const normalizedMessage = message.toLowerCase();
  
  // High confidence patterns (specific date/time mentions)
  const datePatterns = [
    /(\d{1,2})\/(\d{1,2})\/(\d{4})/g,
    /(\d{1,2})\s+de\s+(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/g,
    /(january|february|march|april|may|june|july|august|september|october|november|december)\s+(\d{1,2})/g
  ];
  
  const timePatterns = [
    /(\d{1,2}):(\d{2})/g,
    /(\d{1,2})\s+(am|pm|a\.m\.|p\.m\.)/g,
    /(mañana|tarde|noche)/g
  ];
  
  let foundDate = '';
  let foundTime = '';
  let confidence: 'high' | 'medium' | 'low' | 'none' = 'none';
  
  // Check for specific date patterns
  for (const pattern of datePatterns) {
    const match = pattern.exec(normalizedMessage);
    if (match) {
      foundDate = match[0];
      confidence = 'high';
      break;
    }
  }
  
  // Check for specific time patterns
  for (const pattern of timePatterns) {
    const match = pattern.exec(normalizedMessage);
    if (match) {
      foundTime = match[0];
      if (confidence === 'none') confidence = 'high';
      break;
    }
  }
  
  // Medium confidence patterns (relative mentions)
  if (!foundDate && !foundTime) {
    const relativePtterns = /(hoy|mañana|pasado mañana|today|tomorrow|next week|la proxima semana)/;
    if (relativePtterns.test(normalizedMessage)) {
      confidence = 'medium';
    }
  }
  
  return {
    date: foundDate || undefined,
    hour: foundTime || undefined,
    confidence
  };
}
