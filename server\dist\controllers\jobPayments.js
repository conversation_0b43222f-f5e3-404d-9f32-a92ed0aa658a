"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseJobDateTime = exports.cancelJobAndRefund = exports.completeJobAndPayout = exports.processEscrow = void 0;
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const zod_1 = require("zod");
const auth_1 = require("../utils/auth");
const schemas_1 = require("../database/schemas");
const paymentProcessorService_1 = require("../services/paymentProcessorService");
const parseJobDateTime_1 = require("../utils/parseJobDateTime");
const notifications_1 = require("./notifications");
// Constants (amounts in minor units)
const COMMISSION_AMOUNT = 500; // ₫500 fixed
const COMMISSION_TAX_RATE = 0.18; // 18%
const OPERATOR_TAX_RATE = 0.02; // 2%
const MAX_BID_AMOUNT = 1000000000; // upper bound in minor units (e.g. 1 billion)
const environment = process.env.NODE_ENV === 'production' ? 'production' : 'sandbox';
// Async lazy payment processor to avoid initializing external clients at module import time (helps tests) and to avoid race conditions
let paymentProcessorService = null;
let paymentProcessorInitializing = null;
function getPaymentProcessor() {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        if (paymentProcessorService)
            return paymentProcessorService;
        if (paymentProcessorInitializing)
            return paymentProcessorInitializing;
        paymentProcessorInitializing = (() => tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const instance = new paymentProcessorService_1.PaymentProcessorService(environment);
                paymentProcessorService = instance;
                return instance;
            }
            catch (err) {
                // Clear the initializing promise so retries are possible
                paymentProcessorInitializing = null;
                const msg = err instanceof Error ? err.message : String(err);
                throw new Error(`Failed to initialize PaymentProcessorService: ${msg}`);
            }
        }))();
        return paymentProcessorInitializing;
    });
}
// Zod schemas
const processEscrowSchema = zod_1.z.object({
    paymentMethodId: zod_1.z.string(),
    idempotencyKey: zod_1.z.string().uuid(),
});
const completeJobSchema = zod_1.z.object({
    transactionId: zod_1.z.string().optional(),
});
const cancelJobSchema = zod_1.z.object({
    reason: zod_1.z.string().optional(),
});
const processEscrow = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user)
            return res.status(401).json({ success: false, error: 'Authentication required' });
        const { jobId } = req.params;
        const parse = processEscrowSchema.safeParse(req.body);
        if (!parse.success) {
            return res.status(400).json({ success: false, error: 'Invalid request data', details: parse.error.errors });
        }
        const { paymentMethodId, idempotencyKey } = parse.data;
        if (!mongoose_1.default.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({ success: false, error: 'Invalid jobId' });
        }
        const job = yield schemas_1.Job.findById(jobId);
        if (!job)
            return res.status(404).json({ success: false, error: 'Job not found' });
        if (job.ownerId !== String(user._id))
            return res.status(403).json({ success: false, error: 'Access denied' });
        if (!job.acceptedBidId)
            return res.status(400).json({ success: false, error: 'No accepted bid for this job' });
        const bid = yield schemas_1.Bid.findById(job.acceptedBidId);
        if (!bid)
            return res.status(404).json({ success: false, error: 'Accepted bid not found' });
        // Validate bid.amount: must exist and be coercible to a finite number
        if (typeof bid !== 'object' || bid === null || !Object.prototype.hasOwnProperty.call(bid, 'amount')) {
            return res.status(400).json({ success: false, error: 'Invalid bid amount' });
        }
        const rawBidValue = bid.amount;
        // Accept numeric strings (trim) or numbers. Coerce safely to Number and ensure it's finite.
        const normalized = typeof rawBidValue === 'string' ? rawBidValue.trim() : rawBidValue;
        const coercedAmount = Number(normalized);
        if (!Number.isFinite(coercedAmount)) {
            return res.status(400).json({ success: false, error: 'Invalid bid amount' });
        }
        // If the input appears to be in major units (e.g., dollars), detect and convert if needed.
        // The codebase elsewhere uses amounts in minor units already; assume incoming bid.amount is minor units.
        // Round to integer minor units and validate bounds
        const validatedBidAmount = Math.round(coercedAmount);
        if (!Number.isFinite(validatedBidAmount) || validatedBidAmount <= 0 || validatedBidAmount > MAX_BID_AMOUNT) {
            return res.status(400).json({ success: false, error: 'Invalid bid amount' });
        }
        // Atomic idempotency: claim or return existing transaction by idempotencyKey + userId
        const upsertFilter = { userId: String(user._id), idempotencyKey };
        // Optionally scope to paymentMethodId to avoid cross-method collisions
        if (paymentMethodId)
            upsertFilter.paymentMethodId = paymentMethodId;
        const setOnInsert = {
            jobId: String(job._id),
            bidId: String(bid._id),
            userId: String(user._id),
            paymentMethodId: paymentMethodId,
            // amount stored as total to charge (bid + commission + commission tax)
            amount: validatedBidAmount + COMMISSION_AMOUNT + Math.round(COMMISSION_AMOUNT * COMMISSION_TAX_RATE),
            currency: 'DOP',
            status: 'initiated',
            type: 'Hold',
            idempotencyKey
        };
        const upsertResult = yield schemas_1.PaymentTransaction.findOneAndUpdate(upsertFilter, { $setOnInsert: setOnInsert }, { new: true, upsert: true, rawResult: true }).exec();
        // If an existing document was returned (not inserted now), check its status and return existing info
        const raw = upsertResult;
        if (raw && raw.lastErrorObject && raw.lastErrorObject.updatedExisting) {
            const existingEscrow = raw.value;
            if (existingEscrow && ['escrow', 'successful'].includes(existingEscrow.status)) {
                return res.json({ success: true, data: { transactionId: String(existingEscrow._id), status: existingEscrow.status, amounts: { bid: bid.amount, commission: existingEscrow.commissionAmount || 0, commissionTax: existingEscrow.commissionTaxAmount || 0, total: existingEscrow.amount } } });
            }
            // If existing but not in escrow/successful, proceed to attempt charging but do not create duplicate tx
        }
        // Compute totals
        const commission = COMMISSION_AMOUNT;
        const commissionTax = Math.round(commission * COMMISSION_TAX_RATE);
        const totalCharge = validatedBidAmount + commission + commissionTax;
        // Charge customer with Hold (escrow)
        const _pp = yield getPaymentProcessor();
        const charge = yield _pp.chargePaymentMethod(String(user._id), paymentMethodId, {
            amount: totalCharge,
            currency: 'DOP',
            type: 'Hold',
            jobId: String(job._id),
            bidId: String(bid._id),
            idempotencyKey,
        });
        // Update transaction with commission breakdown and create tax record atomically
        const session = yield mongoose_1.default.startSession();
        let tx = null;
        try {
            yield session.withTransaction(() => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
                tx = yield schemas_1.PaymentTransaction.findOneAndUpdate({ _id: charge.transactionId }, { $set: { commissionAmount: commission, commissionTaxAmount: commissionTax, status: 'escrow' } }, { new: true, session });
                if (!tx) {
                    throw new Error('Transaction not found when updating commission');
                }
                // Create tax record within the same transaction
                const taxRecord = new schemas_1.Tax({
                    jobId: String(job._id),
                    bidId: String(bid._id),
                    subjectType: 'commission',
                    rate: COMMISSION_TAX_RATE,
                    baseAmount: commission,
                    taxAmount: commissionTax,
                    currency: 'DOP',
                    metadata: { reason: 'Commission tax on escrow' }
                });
                yield taxRecord.save({ session });
            }));
        }
        catch (err) {
            console.error('Failed to update transaction and create tax record atomically:', err);
            // Rethrow to outer catch which returns 500
            throw err;
        }
        finally {
            yield session.endSession();
        }
        // Notification to customer
        const token = yield (0, notifications_1.getUserNotificationToken)(job.ownerId);
        if (token.success && token.notificationToken) {
            yield (0, notifications_1.sendCustomNotification)(token.notificationToken, {
                title: 'Payment Authorized (Escrow)',
                message: 'Your payment has been authorized and held in escrow.',
                data: { type: 'payment_escrowed', jobId: jobId, bidId: String(bid._id), transactionId: charge.transactionId },
                soundOn: true,
                badgeCount: 1,
            });
        }
        return res.json({ success: true, data: { transactionId: charge.transactionId, status: 'escrow', amounts: { bid: validatedBidAmount, commission, commissionTax, total: totalCharge } } });
    }
    catch (error) {
        console.error('Error processing escrow:', error);
        const message = error instanceof Error ? error.message : 'Failed to process escrow';
        return res.status(500).json({ success: false, error: message });
    }
});
exports.processEscrow = processEscrow;
const completeJobAndPayout = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user)
            return res.status(401).json({ success: false, error: 'Authentication required' });
        const { jobId } = req.params;
        const parse = completeJobSchema.safeParse(req.body);
        if (!parse.success)
            return res.status(400).json({ success: false, error: 'Invalid request data', details: parse.error.errors });
        const job = yield schemas_1.Job.findById(jobId);
        if (!job)
            return res.status(404).json({ success: false, error: 'Job not found' });
        if (job.ownerId !== String(user._id))
            return res.status(403).json({ success: false, error: 'Access denied' });
        if (job.status !== 'completed')
            return res.status(400).json({ success: false, error: 'Job must be completed before payout' });
        if (!job.acceptedBidId || !job.assignedOperatorId)
            return res.status(400).json({ success: false, error: 'Missing accepted bid or operator' });
        const bid = yield schemas_1.Bid.findById(job.acceptedBidId);
        if (!bid)
            return res.status(404).json({ success: false, error: 'Accepted bid not found' });
        // Idempotency: if a successful payout already exists for this job/bid, return it
        const existingPayout = yield schemas_1.Payout.findOne({
            jobId: String(job._id),
            bidId: String(bid._id),
            operatorId: String(job.assignedOperatorId),
            status: 'completed'
        }).lean();
        if (existingPayout) {
            return res.json({
                success: true,
                data: {
                    payoutId: String(existingPayout._id),
                    amount: existingPayout.amount,
                    operatorTaxAmount: existingPayout.operatorTaxAmount
                }
            });
        }
        // Find escrow transaction
        const escrowTx = yield schemas_1.PaymentTransaction.findOne({ jobId: String(job._id), bidId: String(bid._id), type: 'Hold', status: { $in: ['escrow', 'successful'] } });
        if (!escrowTx)
            return res.status(404).json({ success: false, error: 'Escrow transaction not found' });
        // Capture escrow if still in escrow status
        if (escrowTx.status === 'escrow') {
            const _pp = yield getPaymentProcessor();
            yield _pp.captureTransaction(String(user._id), String(escrowTx._id));
        }
        // Compute payout and operator tax
        const operatorTaxAmount = Math.round(bid.amount * OPERATOR_TAX_RATE);
        const payoutAmount = bid.amount - operatorTaxAmount;
        // Log operator tax
        yield schemas_1.Tax.create({
            jobId: String(job._id),
            bidId: String(bid._id),
            subjectType: 'operator',
            subjectId: String(job.assignedOperatorId),
            rate: OPERATOR_TAX_RATE,
            baseAmount: bid.amount,
            taxAmount: operatorTaxAmount,
            currency: escrowTx.currency,
            metadata: { reason: 'Operator payout tax' }
        });
        // Get operator payout account
        const operatorProfile = yield schemas_1.OperatorProfile.findOne({ accountId: String(job.assignedOperatorId) });
        if (!operatorProfile || !((_a = operatorProfile.payout) === null || _a === void 0 ? void 0 : _a.accountNumber)) {
            return res.status(400).json({
                success: false,
                error: 'Operator payout account not configured'
            });
        }
        const destinationAccount = (_b = operatorProfile === null || operatorProfile === void 0 ? void 0 : operatorProfile.payout) === null || _b === void 0 ? void 0 : _b.accountNumber;
        // Create payout record (processing local ledger)
        const payout = yield schemas_1.Payout.create({
            jobId: String(job._id),
            bidId: String(bid._id),
            operatorId: String(job.assignedOperatorId),
            amount: payoutAmount,
            currency: escrowTx.currency,
            status: 'completed',
            operatorTaxRate: OPERATOR_TAX_RATE,
            operatorTaxAmount,
            destinationAccount,
            transactionRef: escrowTx.providerReference,
        });
        // Notify operator
        const operatorToken = yield (0, notifications_1.getUserNotificationToken)(String(job.assignedOperatorId));
        if (operatorToken.success && operatorToken.notificationToken) {
            yield (0, notifications_1.sendCustomNotification)(operatorToken.notificationToken, {
                title: 'Payout Released',
                message: `Your payout of ${payoutAmount} has been released`,
                data: { type: 'payout_released', jobId: jobId, bidId: String(bid._id), payoutId: String(payout._id) },
                soundOn: true,
                badgeCount: 1,
            });
        }
        return res.json({ success: true, data: { payoutId: payout._id.toString(), amount: payoutAmount, operatorTaxAmount } });
    }
    catch (error) {
        console.error('Error completing job and payout:', error);
        const message = error instanceof Error ? error.message : 'Failed to complete job payout';
        return res.status(500).json({ success: false, error: message });
    }
});
exports.completeJobAndPayout = completeJobAndPayout;
const cancelJobAndRefund = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user)
            return res.status(401).json({ success: false, error: 'Authentication required' });
        const { jobId } = req.params;
        const parse = cancelJobSchema.safeParse(req.body);
        if (!parse.success)
            return res.status(400).json({ success: false, error: 'Invalid request data', details: parse.error.errors });
        const job = yield schemas_1.Job.findById(jobId);
        if (!job)
            return res.status(404).json({ success: false, error: 'Job not found' });
        if (job.ownerId !== String(user._id))
            return res.status(403).json({ success: false, error: 'Access denied' });
        // Idempotency: if already cancelled, return success
        if (job.status === 'cancelled') {
            return res.json({ success: true, data: { alreadyCancelled: true } });
        }
        // Find escrow transaction
        const escrowTx = yield schemas_1.PaymentTransaction.findOne({ jobId: String(job._id), type: 'Hold' });
        if (!escrowTx)
            return res.status(404).json({ success: false, error: 'Escrow transaction not found' });
        // Get job owner's timezone for accurate time calculations
        const jobOwner = yield schemas_1.Account.findById(job.ownerId);
        const jobOwnerTimezone = ((_a = jobOwner === null || jobOwner === void 0 ? void 0 : jobOwner.settings) === null || _a === void 0 ? void 0 : _a.timezone) || 'Europe/Paris'; // Default fallback
        // Time-based refund logic
        let scheduledDateTime;
        try {
            scheduledDateTime = (0, parseJobDateTime_1.parseJobDateTime)(job.date, job.hour, jobOwnerTimezone);
        }
        catch (parseError) {
            // Log parsing error with contextual information and return a 400 so controller doesn't crash
            try {
                console.error('Failed to parse job scheduled datetime', {
                    jobId: String(job._id || jobId),
                    date: job.date,
                    hour: job.hour,
                    timezone: jobOwnerTimezone,
                    error: parseError instanceof Error ? parseError.message : String(parseError)
                });
            }
            catch (logErr) {
                // Best-effort logging; swallow to avoid secondary errors
                console.error('Failed to log parse error for job scheduled datetime', logErr);
            }
            return res.status(400).json({ success: false, error: 'Invalid job date or hour format' });
        }
        const now = new Date();
        const hoursDiff = (scheduledDateTime.getTime() - now.getTime()) / (1000 * 60 * 60);
        let refundAmount = escrowTx.amount;
        let cancellationFee = 0;
        let adminShare = 0;
        let operatorShare = 0;
        let reservedFees = 0;
        if (hoursDiff >= 24) {
            // Full refund
            refundAmount = escrowTx.amount;
        }
        else {
            // Apply cancellation fee: 15% of bid amount retained by admin, 5% to operator
            const bid = yield schemas_1.Bid.findById(job.acceptedBidId);
            if (!bid)
                return res.status(404).json({ success: false, error: 'Accepted bid not found' });
            // Determine reserved fees already held in the escrow (commission and commission tax)
            // Ensure we operate in smallest currency unit (minor units) and use integer arithmetic only
            const reservedCommission = Math.round(escrowTx.commissionAmount || 0);
            const reservedCommissionTax = Math.round(escrowTx.commissionTaxAmount || 0);
            reservedFees = reservedCommission + reservedCommissionTax;
            // Convert escrow amount to integer minor units
            const escrowAmountMinor = Math.round(escrowTx.amount || 0);
            // Compute available balance in escrow for penalty distribution (minor units)
            const escrowAvailable = Math.max(0, escrowAmountMinor - reservedFees);
            // Derive shares from the escrow-available amount using integer math
            // admin gets 15%, operator gets 5% -> use Math.floor to avoid over-allocation
            adminShare = Math.floor((escrowAvailable * 15) / 100);
            operatorShare = Math.floor((escrowAvailable * 5) / 100);
            // Ensure rounding/logic does not allow total taken to exceed escrow amount
            let totalTaken = reservedFees + adminShare + operatorShare;
            if (totalTaken > escrowAmountMinor) {
                // Reduce admin first, then operator to remove any overage
                let over = totalTaken - escrowAmountMinor;
                const reduceAdmin = Math.min(over, adminShare);
                adminShare -= reduceAdmin;
                over -= reduceAdmin;
                if (over > 0) {
                    const reduceOperator = Math.min(over, operatorShare);
                    operatorShare -= reduceOperator;
                    over -= reduceOperator;
                }
                totalTaken = reservedFees + adminShare + operatorShare;
            }
            cancellationFee = adminShare + operatorShare + reservedFees;
            refundAmount = Math.max(0, escrowAmountMinor - (adminShare + operatorShare + reservedFees));
        }
        // Transactional payment flow with step persistence and compensation
        const session = yield mongoose_1.default.startSession();
        let compensationNeeded = false;
        let captureCompleted = false;
        try {
            yield session.withTransaction(() => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
                // Check if payment steps already completed (idempotency)
                const existingCancellation = yield schemas_1.PaymentTransaction.findOne({
                    jobId: String(job._id),
                    type: 'Refund',
                    metadata: { reason: 'Job cancellation' }
                }).session(session);
                if (existingCancellation) {
                    // Already processed, skip payment operations
                    return;
                }
                const _pp = yield getPaymentProcessor();
                // Step 1: Handle escrow based on current status
                if (escrowTx.status === 'escrow') {
                    if (cancellationFee > 0) {
                        // Step 1a: Capture full amount
                        try {
                            yield _pp.captureTransaction(String(user._id), String(escrowTx._id));
                            captureCompleted = true;
                            // Persist capture step
                            yield schemas_1.PaymentTransaction.findByIdAndUpdate(escrowTx._id, {
                                $set: {
                                    status: 'captured',
                                    capturedAt: new Date(),
                                    metadata: Object.assign(Object.assign({}, (escrowTx.metadata || {})), { captureReason: 'Job cancellation with fee' })
                                }
                            }, { session });
                            // Step 1b: Issue refund if needed
                            if (refundAmount > 0) {
                                try {
                                    yield _pp.refundTransaction(String(user._id), String(escrowTx._id), refundAmount, 'Job cancellation');
                                    // Persist refund transaction
                                    yield schemas_1.PaymentTransaction.create([{
                                            jobId: String(job._id),
                                            bidId: String(job.acceptedBidId),
                                            userId: String(user._id),
                                            amount: refundAmount,
                                            currency: escrowTx.currency,
                                            status: 'successful',
                                            type: 'Refund',
                                            parentTransactionId: String(escrowTx._id),
                                            metadata: { reason: 'Job cancellation', adminShare, operatorShare, reservedFees }
                                        }], { session });
                                }
                                catch (refundError) {
                                    compensationNeeded = true;
                                    throw new Error(`Refund failed after capture: ${refundError instanceof Error ? refundError.message : String(refundError)}`);
                                }
                            }
                        }
                        catch (captureError) {
                            throw new Error(`Capture failed: ${captureError instanceof Error ? captureError.message : String(captureError)}`);
                        }
                    }
                    else {
                        // Step 1c: Simply void the escrow (no fee)
                        try {
                            yield _pp.voidTransaction(String(user._id), String(escrowTx._id));
                            // Persist void step
                            yield schemas_1.PaymentTransaction.findByIdAndUpdate(escrowTx._id, {
                                $set: {
                                    status: 'voided',
                                    voidedAt: new Date(),
                                    metadata: Object.assign(Object.assign({}, (escrowTx.metadata || {})), { voidReason: 'Job cancellation no fee' })
                                }
                            }, { session });
                        }
                        catch (voidError) {
                            throw new Error(`Void failed: ${voidError instanceof Error ? voidError.message : String(voidError)}`);
                        }
                    }
                }
                else {
                    // Already captured, issue refund if needed
                    if (refundAmount > 0) {
                        try {
                            yield _pp.refundTransaction(String(user._id), String(escrowTx._id), refundAmount, 'Job cancellation');
                            // Persist refund transaction
                            yield schemas_1.PaymentTransaction.create([{
                                    jobId: String(job._id),
                                    bidId: String(job.acceptedBidId),
                                    userId: String(user._id),
                                    amount: refundAmount,
                                    currency: escrowTx.currency,
                                    status: 'successful',
                                    type: 'Refund',
                                    parentTransactionId: String(escrowTx._id),
                                    metadata: { reason: 'Job cancellation', adminShare, operatorShare, reservedFees }
                                }], { session });
                        }
                        catch (refundError) {
                            throw new Error(`Refund failed: ${refundError instanceof Error ? refundError.message : String(refundError)}`);
                        }
                    }
                }
            }));
        }
        catch (transactionError) {
            // Compensation logic: attempt to reverse any completed payment operations
            if (compensationNeeded && captureCompleted) {
                try {
                    console.warn('Attempting compensation: voiding captured transaction due to refund failure');
                    const _pp = yield getPaymentProcessor();
                    yield _pp.voidTransaction(String(user._id), String(escrowTx._id));
                    // Update transaction status to reflect compensation
                    yield schemas_1.PaymentTransaction.findByIdAndUpdate(escrowTx._id, {
                        $set: {
                            status: 'voided',
                            voidedAt: new Date(),
                            metadata: Object.assign(Object.assign({}, (escrowTx.metadata || {})), { compensationReason: 'Voided due to refund failure', originalError: transactionError instanceof Error ? transactionError.message : String(transactionError) })
                        }
                    });
                }
                catch (compensationError) {
                    console.error('Compensation failed:', compensationError);
                    // Log but don't throw - original error takes precedence
                }
            }
            console.error('Payment transaction failed:', transactionError);
            throw transactionError;
        }
        finally {
            yield session.endSession();
        }
        // Helper to format amounts for display: convert minor units to major units
        // ASSUMPTION: 100 minor units = 1 major unit (e.g., cents). Adjust if different.
        const MINOR_PER_MAJOR = 100;
        const formatMajor = (amt) => Number((amt / MINOR_PER_MAJOR).toFixed(2));
        // If operator gets a share on late cancellation, create payout record (idempotent check)
        if (operatorShare > 0 && job.assignedOperatorId) {
            const existingPenaltyPayout = yield schemas_1.Payout.findOne({ jobId: String(job._id), bidId: String(job.acceptedBidId), operatorId: String(job.assignedOperatorId), amount: operatorShare }).lean();
            if (!existingPenaltyPayout) {
                // Load operator profile to validate payout settings and avoid marking funds as completed when operator has no valid payout configured
                const operatorProfile = yield schemas_1.OperatorProfile.findOne({ accountId: String(job.assignedOperatorId) }).lean();
                // OperatorProfile.payout currently only exposes accountNumber and bankName
                const hasValidPayout = !!(operatorProfile && operatorProfile.payout && operatorProfile.payout.accountNumber);
                if (hasValidPayout) {
                    // Create completed payout and include destination account when available
                    yield schemas_1.Payout.create({
                        jobId: String(job._id),
                        bidId: String(job.acceptedBidId),
                        operatorId: String(job.assignedOperatorId),
                        amount: operatorShare,
                        currency: escrowTx.currency,
                        status: 'completed',
                        operatorTaxRate: 0,
                        operatorTaxAmount: 0,
                        destinationAccount: ((_b = operatorProfile === null || operatorProfile === void 0 ? void 0 : operatorProfile.payout) === null || _b === void 0 ? void 0 : _b.accountNumber) || null,
                        transactionRef: escrowTx.providerReference,
                    });
                }
                else {
                    // Operator has no valid payout configured: create a pending payout with a clear reason/note so funds are not stranded
                    const note = 'Operator payout not configured or inactive; awaiting admin action';
                    console.warn(`Pending penalty payout for operator ${job.assignedOperatorId} for job ${job._id}: ${note}`);
                    const pendingPayout = yield schemas_1.Payout.create({
                        jobId: String(job._id),
                        bidId: String(job.acceptedBidId),
                        operatorId: String(job.assignedOperatorId),
                        amount: operatorShare,
                        currency: escrowTx.currency,
                        status: 'pending',
                        reason: 'no_valid_payout',
                        note,
                        operatorTaxRate: 0,
                        operatorTaxAmount: 0,
                        transactionRef: escrowTx.providerReference,
                        metadata: { operatorProfilePresent: !!operatorProfile }
                    });
                    // Notify operator about pending payout and instruct them to configure payout settings
                    try {
                        const operatorToken2 = yield (0, notifications_1.getUserNotificationToken)(String(job.assignedOperatorId));
                        if (operatorToken2.success && operatorToken2.notificationToken) {
                            yield (0, notifications_1.sendCustomNotification)(operatorToken2.notificationToken, {
                                title: 'Payout Pending',
                                message: `A payout of ${formatMajor(operatorShare)} is pending because your payout account is not configured or inactive. Please update your payout settings.`,
                                data: { type: 'payout_pending', jobId: String(job._id), payoutId: String(pendingPayout._id) },
                                soundOn: true,
                                badgeCount: 1,
                            });
                        }
                    }
                    catch (notifyErr) {
                        console.error('Failed to notify operator about pending payout:', notifyErr);
                    }
                }
            }
        }
        // Notify customer
        const token = yield (0, notifications_1.getUserNotificationToken)(job.ownerId);
        if (token.success && token.notificationToken) {
            yield (0, notifications_1.sendCustomNotification)(token.notificationToken, {
                title: 'Refund Processed',
                message: `Your refund of ${formatMajor(refundAmount)} has been processed`,
                data: { type: 'refund_processed', jobId },
                soundOn: true,
                badgeCount: 1,
            });
        }
        // Notify operator about cancellation penalty if applicable
        if (operatorShare > 0 && job.assignedOperatorId) {
            const operatorToken = yield (0, notifications_1.getUserNotificationToken)(String(job.assignedOperatorId));
            if (operatorToken.success && operatorToken.notificationToken) {
                yield (0, notifications_1.sendCustomNotification)(operatorToken.notificationToken, {
                    title: 'Late Cancellation Penalty',
                    message: `A cancellation penalty has been applied. You received ${formatMajor(operatorShare)}.`,
                    data: { type: 'cancellation_penalty', jobId },
                    soundOn: true,
                    badgeCount: 1,
                });
            }
        }
        // Update job status
        job.status = 'cancelled';
        yield job.save();
        // Return amounts in major currency units (for display)
        return res.json({ success: true, data: { refundAmount: formatMajor(refundAmount), cancellationFee: formatMajor(cancellationFee), adminShare: formatMajor(adminShare), operatorShare: formatMajor(operatorShare) } });
    }
    catch (error) {
        console.error('Error cancelling job and refund:', error);
        const message = error instanceof Error ? error.message : 'Failed to cancel job/refund';
        return res.status(500).json({ success: false, error: message });
    }
});
exports.cancelJobAndRefund = cancelJobAndRefund;
exports.parseJobDateTime = parseJobDateTime_1.parseJobDateTime;
