import { PaymentMethod, PaymentTransaction } from '../database/schemas';
import { AzulClient } from './azulClient';
import {
  CreatePaymentMethodRequest,
  PaymentMethodResponse
} from '../types/azul';
import { NotFoundPaymentError } from '../errors/paymentErrors';
import { IPaymentMethod } from '../database/schemas/_paymentMethod';
import { getAzulDeletionRetryService } from './azulDeletionRetryService';

export class PaymentMethodService {
  private azulClient: AzulClient;
  private environment: 'sandbox' | 'production';
  
  constructor(environment: 'sandbox' | 'production' = 'sandbox') {
    this.environment = environment;
    this.azulClient = new AzulClient(environment);
  }
  
  /**
   * Create a new payment method
   */
  async createPaymentMethod(
    userId: string,
    request: CreatePaymentMethodRequest
  ): Promise<PaymentMethodResponse> {
    try {
      let dataVaultToken: string;
      let last4: string;
      let brand: string;
      let expMonth: number;
      let expYear: number;
      let hasCvv: boolean = false;
      
      if ('azulToken' in request) {
        // Option 1: Client provided token (preferred)
        // We would need to verify the token with Azul or trust it's valid
        // For now, we'll assume it's valid and extract info from it
        throw new Error('Client-provided tokens not yet implemented. Use card details.');
      } else if ('cardNumber' in request && request.cardNumber && request.expiryMonth && request.expiryYear) {
        // Option 2: Create token from card details (only if PCI compliant)
        
        // Validate card number
        if (!AzulClient.validateCardNumber(request.cardNumber)) {
          throw new Error('Invalid card number');
        }
        
        // Validate expiration
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1;
        
        if (request.expiryYear < currentYear || 
            (request.expiryYear === currentYear && request.expiryMonth < currentMonth)) {
          throw new Error('Card has expired');
        }
        
        if (request.expiryMonth < 1 || request.expiryMonth > 12) {
          throw new Error('Invalid expiry month');
        }
        
        // Validate CVC format if provided
        if (request.cvc) {
          const cvcRegex = /^\d{3,4}$/;
          if (!cvcRegex.test(request.cvc)) {
            throw new Error('Invalid CVC format');
          }
        }
        
        // Create Data Vault token with Azul
        const expiration = AzulClient.formatExpiration(request.expiryMonth, request.expiryYear);
        
        const azulResponse = await this.azulClient.createDataVaultToken({
          cardNumber: request.cardNumber,
          cvc: request.cvc,
          expiration,
          cardHolderName: request.cardHolderName
        });
        
        // Validate Azul response
        const validation = AzulClient.validateResponse(azulResponse);
        if (!validation.isSuccess) {
          throw new Error(`Failed to create payment method: ${validation.errorMessage}`);
        }
        
        if (!azulResponse.DataVaultToken) {
          throw new Error('Azul did not return a Data Vault token');
        }
        
        dataVaultToken = azulResponse.DataVaultToken;
        // Ensure last4 is a plain 4-digit string. Prefer Azul response; otherwise normalize card number to digits and take last 4.
        // Sanitize inputs and guard against strings shorter than 4 chars
        const sanitizeAndExtractLast4 = (cardNumber: string | undefined): string => {
          if (!cardNumber) return '';
          const digitsOnly = cardNumber.replace(/\D/g, '');
          return digitsOnly.length >= 4 ? digitsOnly.slice(-4) : '';
        };
        
        last4 = sanitizeAndExtractLast4(azulResponse.CardNumber) || sanitizeAndExtractLast4(request.cardNumber);
        brand = AzulClient.getCardBrand(request.cardNumber);
        expMonth = request.expiryMonth;
        expYear = request.expiryYear;
        hasCvv = azulResponse.HasCVV || false;
        
      } else {
        throw new Error('Either azulToken or card details (cardNumber, expiryMonth, expiryYear) must be provided');
      }
      
      // Atomic approach: Try to create with isFavorite=true first if requested or if user has no favorites
      let isFavorite = request.setAsFavorite || false;
      let shouldTryAsFavorite = isFavorite;
      
      // If not explicitly set as favorite, check if user should get their first favorite atomically
      if (!isFavorite) {
        // Try to create as favorite first - if user has no existing favorites, this will succeed
        // If they do have a favorite, the unique partial index will prevent this and we'll retry with false
        shouldTryAsFavorite = true;
      }
      
      let paymentMethod: any;
      let finalIsFavorite = shouldTryAsFavorite;
      
      try {
        // Attempt to create with isFavorite=true (either explicitly requested or as first method)
        paymentMethod = new PaymentMethod({
          userId,
          provider: 'azul',
          dataVaultToken,
          last4,
          brand,
          exp_month: expMonth,
          exp_year: expYear,
          has_cvv: hasCvv,
          is_favorite: finalIsFavorite
        });
        
        await paymentMethod.save();
        
      } catch (error: any) {
        // Handle unique constraint violation for favorite (duplicate key error)
        if (error.code === 11000 && error.keyPattern?.is_favorite) {
          if (!request.setAsFavorite) {
            // User didn't explicitly request favorite, so retry with isFavorite=false
            // This means they already have a favorite method
            finalIsFavorite = false;
            
            paymentMethod = new PaymentMethod({
              userId,
              provider: 'azul',
              dataVaultToken,
              last4,
              brand,
              exp_month: expMonth,
              exp_year: expYear,
              has_cvv: hasCvv,
              is_favorite: finalIsFavorite
            });
            
            await paymentMethod.save();
          } else {
            // User explicitly requested favorite but there's already one
            // The pre-save hook should handle unsetting others, so this shouldn't happen
            // unless there's a race condition in the hook itself
            throw new Error('Unable to set payment method as favorite. Please try again.');
          }
        } else {
          // Re-throw other errors (validation, network, etc.)
          throw error;
        }
      }
      
      return {
        id: (paymentMethod as any)._id.toString(),
        last4: paymentMethod.last4,
        brand: paymentMethod.brand,
        exp_month: paymentMethod.exp_month,
        exp_year: paymentMethod.exp_year,
        is_favorite: paymentMethod.is_favorite,
        has_cvv: paymentMethod.has_cvv,
        createdAt: paymentMethod.createdAt.toISOString()
      };
      
    } catch (error) {
      console.error('Error creating payment method:', error);
      throw error;
    }
  }
  
  /**
   * Get all payment methods for a user
   */
  async getPaymentMethods(userId: string): Promise<PaymentMethodResponse[]> {
    try {
      const paymentMethods = await PaymentMethod.find({ userId })
        .sort({ is_favorite: -1, createdAt: -1 })
        .lean();
      
      return paymentMethods.map(method => ({
        id: (method as any)._id.toString(),
        last4: method.last4,
        brand: method.brand,
        exp_month: method.exp_month,
        exp_year: method.exp_year,
        is_favorite: method.is_favorite,
        has_cvv: method.has_cvv,
        createdAt: method.createdAt.toISOString()
      }));
      
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      throw new Error('Failed to fetch payment methods');
    }
  }
  
  /**
   * Set a payment method as favorite
   */
  async setFavoritePaymentMethod(userId: string, paymentMethodId: string): Promise<PaymentMethodResponse> {
    try {
      // Verify the payment method belongs to the user
      const paymentMethod = await PaymentMethod.findOne({
        _id: paymentMethodId,
        userId
      });
      
      if (!paymentMethod) {
        throw new NotFoundPaymentError('Payment method not found or does not belong to user');
      }
      
      // Use a transaction to ensure atomic update
      const session = await PaymentMethod.startSession();
      
      try {
        await session.withTransaction(async () => {
          // Unset all other payment methods as favorite
          await PaymentMethod.updateMany(
            { userId, _id: { $ne: paymentMethodId } },
            { $set: { is_favorite: false } },
            { session }
          );
          
          // Set this payment method as favorite
          await PaymentMethod.updateOne(
            { _id: paymentMethodId },
            { $set: { is_favorite: true } },
            { session }
          );
        });
        
        // Fetch updated payment method
        const updatedMethod = await PaymentMethod.findById(paymentMethodId);
        if (!updatedMethod) {
          throw new NotFoundPaymentError('Payment method not found after update');
        }
        
        return {
          id: (updatedMethod as any)._id.toString(),
          last4: updatedMethod.last4,
          brand: updatedMethod.brand,
          exp_month: updatedMethod.exp_month,
          exp_year: updatedMethod.exp_year,
          is_favorite: updatedMethod.is_favorite,
          has_cvv: updatedMethod.has_cvv,
          createdAt: updatedMethod.createdAt.toISOString()
        };
        
      } catch (error) {
        // withTransaction automatically handles transaction abortion on error
        throw error;
      } finally {
        // Ensure session is always ended regardless of success or failure
        await session.endSession();
      }
      
    } catch (error) {
      console.error('Error setting favorite payment method:', error);
      throw error;
    }
  }
  
  /**
   * Delete a payment method
   */
  async deletePaymentMethod(userId: string, paymentMethodId: string): Promise<void> {
    try {
      // Verify the payment method belongs to the user
      const paymentMethod = await PaymentMethod.findOne({
        _id: paymentMethodId,
        userId
      });
      
      if (!paymentMethod) {
        throw new NotFoundPaymentError('Payment method not found or does not belong to user');
      }
      
      // Check if there are any pending transactions using this payment method
      const pendingTransactions = await PaymentTransaction.findOne({
        paymentMethodId,
        status: { $in: ['initiated', 'escrow'] }
      });
      
      if (pendingTransactions) {
        throw new Error('Cannot delete payment method with pending transactions');
      }
      
      // Delete token from Azul Data Vault
      let azulDeletionFailed = false;
      let azulErrorMessage = '';
      
      try {
        const azulResponse = await this.azulClient.deleteDataVaultToken(paymentMethod.dataVaultToken);
        const validation = AzulClient.validateResponse(azulResponse);
        
        if (!validation.isSuccess) {
          azulDeletionFailed = true;
          azulErrorMessage = validation.errorMessage || 'Unknown Azul validation error';
          console.error('Failed to delete token from Azul:', azulErrorMessage);
        }
      } catch (azulError) {
        azulDeletionFailed = true;
        azulErrorMessage = azulError instanceof Error ? azulError.message : String(azulError);
        console.error('Error calling Azul delete token API:', azulError);
      }
      
      // If Azul deletion failed, enqueue for retry
      if (azulDeletionFailed) {
        try {
          const retryService = getAzulDeletionRetryService(this.environment);
          await retryService.enqueueRetry({
            paymentMethodId: String(paymentMethod._id),
            dataVaultToken: paymentMethod.dataVaultToken,
            userId: String(paymentMethod.userId),
            metadata: {
              originalDeletedAt: new Date(),
              paymentMethodType: paymentMethod.brand || 'unknown'
            }
          });
          
          console.log('Enqueued Azul deletion retry for payment method', {
            paymentMethodId: String(paymentMethod._id),
            userId: String(paymentMethod.userId),
            error: azulErrorMessage
          });
        } catch (enqueueError) {
          console.error('Failed to enqueue Azul deletion retry:', enqueueError);
          // Continue with local deletion even if retry enqueueing fails
        }
      }
      
      // Delete local record
      await PaymentMethod.deleteOne({ _id: paymentMethodId });
      
      // If this was the favorite method and there are other methods, set the most recent as favorite
      if (paymentMethod.is_favorite) {
        const remainingMethods = await PaymentMethod.find({ userId }).sort({ createdAt: -1 }).limit(1);
        if (remainingMethods.length > 0) {
          await PaymentMethod.updateOne(
            { _id: remainingMethods[0]._id },
            { $set: { is_favorite: true } }
          );
        }
      }
      
    } catch (error) {
      console.error('Error deleting payment method:', error);
      throw error;
    }
  }
  
  /**
   * Get a specific payment method (internal use)
   */
  async getPaymentMethodById(userId: string, paymentMethodId: string): Promise<IPaymentMethod | null> {
    try {
      return await PaymentMethod.findOne({
        _id: paymentMethodId,
        userId
      });
    } catch (error) {
      console.error('Error fetching payment method by ID:', error);
      return null;
    }
  }
  
  /**
   * Validate payment method ownership
   */
  async validateOwnership(userId: string, paymentMethodId: string): Promise<boolean> {
    try {
      const method = await PaymentMethod.findOne({
        _id: paymentMethodId,
        userId
      });
      return !!method;
    } catch (error) {
      console.error('Error validating payment method ownership:', error);
      return false;
    }
  }
}

export default PaymentMethodService;
