import React, { useContext, useEffect, useState } from 'react'
import { Image, StyleSheet, Text, View } from 'react-native'
import { signInWithApple } from '@/utils/AppleAuth'
import { GoogleSignin, isSuccessResponse } from '@react-native-google-signin/google-signin'

import Page from '@/components/templates/Page'
import ButtonIcon from '@/components/atoms/buttons/ButtonIcon'
import AppleIcon from '@/components/atoms/icons/Apple'
import GoogleIcon from '@/components/atoms/icons/Google'
import Toast from 'react-native-toast-message'

import { router } from 'expo-router'
import { AuthContext } from '@/context/AuthContext'
import { appleLogin, googleLogin, setToken } from '@/services/api'
import ScreenSpinnerLoader from '@/components/atoms/loaders/ScreenSpinner'
import { Ionicons } from '@expo/vector-icons'
import { LinearGradient } from 'expo-linear-gradient'


const Login = () => {
  const { sessionAuthentication } = useContext(AuthContext)
  const [loading, setLoading] = useState(false)

  const handleAppleLogin = async () => {
    const { identityToken } = await signInWithApple()

    if (!identityToken) {
      Toast.show({
        type: 'error',
        text1: 'Apple Sign In Failed',
        text2: 'Please try again later.',
      })
      return
    }
    setLoading(true)
    const response = await appleLogin(identityToken)
    if (!response.success) {
      Toast.show({
        type: 'error',
        text1: 'Apple Login Failed',
        text2: response.error || 'Please try again later.',
      })
      setLoading(false)
      return
    }

    //Set Token in Secure Store
    setToken(response.token)

    //Go to Home
    await sessionAuthentication()
    setLoading(false)
    router.replace('/(home)')
  }
  const handleGoogleLogin = async () => {
    try {
      await GoogleSignin.hasPlayServices()
      const response = await GoogleSignin.signIn()

      if (isSuccessResponse(response)) {
        const { user } = response.data
        const { givenName, familyName, email, photo, id } = user

        setLoading(true)
        const call = await googleLogin(id, email, givenName ?? '', familyName ?? '', photo ?? '')

        if (!call.success) {
          setLoading(false)
          Toast.show({
            type: 'error',
            text1: 'Google Login Failed',
            text2: 'Please try again later.',
          })
          return
        }

        //Set Token in Secure Store
        setToken(call.token)

        //Go to Home
        await sessionAuthentication()
        setLoading(false)
        router.replace('/(home)')

      } else {
        Toast.show({
          type: 'error',
          text1: 'Google Login Failed',
          text2: 'Please try again later.',
        })
        setLoading(false)
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Google Login Failed',
        text2: 'Please try again later.',
      })
      setLoading(false)
    }
  }


  useEffect(() => {
    GoogleSignin.configure({
      iosClientId: "684414254538-iea4399buqu70spud45kkq457i5mpckv.apps.googleusercontent.com",
      webClientId: "684414254538-5spcgehljv9153jsamtlunalcj3nco43.apps.googleusercontent.com",
      profileImageSize: 150
    })
  }, [])


  return (
    <Page noPaddingTop alignItems='center' justifyContent='space-between' >

      {
        loading &&
        <ScreenSpinnerLoader />
      }

      <View style={{
        flex: 1,
        height: '100%',
        width: '100%',
        flexDirection: 'column',
        justifyContent: 'space-between'
      }}>
        {/* Top Part */}
        <View style={{ width: '100%', height: '60%', position: 'relative' }}>

          <Image
            source={require('@/assets/pictures/landing_secondary.jpg')}
            style={styles.landingImage}
          />
          <LinearGradient
            colors={["#ffffff", "#ffffff00"]}
            start={{ x: 0, y: 1 }}
            end={{ x: 0, y: 0 }}
            style={{ width: '100%', height: 300, position: 'absolute', bottom: 0, left: 0 }}
          />

          <View style={{ alignItems: 'center', justifyContent: 'center', flexDirection: 'column', width: '100%', position: 'absolute', left: 0, bottom: -40 }}>

            <Image
              source={require('@/assets/pictures/manito.png')}
              style={{ width: 100, height: 100, resizeMode: 'contain' }}
            />

            <Text style={{ fontSize: 50, color: '#337836', fontWeight: 600, transform: [{ translateY: -15 }], fontFamily: 'Montserrat'}}>Manito</Text>

          </View>
        </View>

        {/* Bottom Part */}
        <View style={{ width: '100%', justifyContent: 'space-between', flexDirection: 'column', paddingBottom: 30, height: '40%' }}>


          <View style={{ width: '100%', alignItems: 'center', paddingTop: 50, gap: 10 }}>

            <ButtonIcon
              icon={<AppleIcon />}
              text='Continuar con Apple'
              onPress={() => {
                handleAppleLogin()
              }}
              style={{ width: '90%', height: 48, backgroundColor: 'white' }}
              styleText={{ fontSize: 15, color: 'black', fontWeight: '600' }}
            />
            <ButtonIcon
              icon={<GoogleIcon />}
              text='Continuar con Google'
              onPress={() => { handleGoogleLogin() }}
              style={{ width: '90%', height: 48, backgroundColor: 'white' }}
              styleText={{ fontSize: 15, color: 'black', fontWeight: '600' }}
            />
            <ButtonIcon
              icon={<Ionicons name="mail-outline" size={20} />}
              text='Continuar con Email'
              onPress={() => { router.push('/auth/email') }}
              style={{ width: '90%', backgroundColor: 'white' }}
              styleText={{ fontSize: 15, color: 'black', fontWeight: '600' }}
            />
          </View>


          <View style={{ flexDirection: 'row', justifyContent: 'center', paddingHorizontal: 30, gap: 30 }}>
            <Text
              style={{ color: '#000000a9', fontFamily: 'Montserrat' }}
              onPress={() => router.push('/settings/terms')}
            >
              Terms of Service
            </Text>

            <Text
              style={{ color: '#000000a9', fontFamily: 'Montserrat' }}
              onPress={() => router.push('/settings/policy')}
            >
              Privacy Policy
            </Text>
          </View>

        </View>
      </View>




    </Page>
  )
}

export default Login

const styles = StyleSheet.create({
  landingImage: {

    height: '100%',
    width: '100%',
    objectFit: 'cover'
  }
})