import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { ScrollView } from 'react-native-gesture-handler'



interface HorizontalTwoProps {
    children: React.ReactNode,
    padding?: number,
    title?: string,
    noMargin?: boolean
}

const HorizontalScrollableList = ({ padding, children, title, noMargin }: HorizontalTwoProps) => {
    return (
        <View style={{ flexDirection: 'column', width: '100%', marginTop: noMargin ? 0 : 30 }}>
            {
                title &&
                <Text style={{ fontFamily: 'Montserrat', fontSize: 18, fontWeight: 500, paddingHorizontal: padding ?? 15, marginBottom: 10 }}>{title}</Text>
            }
            <ScrollView
                style={{ flexDirection: 'row', height: 'auto', width: '100%' }}
                horizontal={true}
                showsHorizontalScrollIndicator={false}
            >
                {children}

                <View style={{ height: '100%', width: 15 }} />
            </ScrollView>
        </View>
    )
}

export default HorizontalScrollableList