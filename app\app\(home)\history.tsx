import React, { useState } from 'react'
import { Image, ScrollView, StyleSheet, Text, View } from 'react-native'

import Page from '@/components/templates/Page'
import ScreenLoader from '@/components/atoms/loaders/Screen'
import Navbar from '@/components/templates/Navbar'
import BottomSheet from '@/components/templates/BottomSheet'
import Header from '@/components/templates/Header'
import UnderConstructionLoader from '@/components/atoms/loaders/UnderConstruction'

const Index = () => {
  const [bottomSheet, setBottomSheet] = useState(false)


  return (
    <Page noPaddingTop noBottomBar alignItems='center' justifyContent='space-between' page='fridge'>

      <Header text='Historial' burgerMenu />

      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.scrollView}
      >

        <View style={{ alignItems: 'center', justifyContent: 'center', height: 650 }}>
          <UnderConstructionLoader />
          <Text style={styles.title}>Nada que ver aqui</Text>
          <Text style={styles.subtitle}>Esta pagina esta en construccion</Text>
          
        </View>


      </ScrollView>

    </Page>
  )
}

export default Index

const styles = StyleSheet.create({
  scrollView: {
    paddingTop: 100
  },
  title: {
    fontFamily: 'Montserrat',
    fontSize: 18,
    fontWeight: 700,
    color: '#000'
  },
  subtitle: {
    marginTop: 8,
    fontFamily: 'Montserrat',
    fontSize: 14,
    fontWeight: 400,

    textAlign: 'center',
    paddingHorizontal: 40,
    lineHeight: 20,
    color: '#000000db'
  },
})