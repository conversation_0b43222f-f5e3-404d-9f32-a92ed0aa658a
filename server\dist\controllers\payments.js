"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.captureTransaction = exports.refundTransaction = exports.process3DSChallenge = exports.handle3DSMethodNotification = exports.chargePaymentMethod = exports.deletePaymentMethod = exports.setFavoritePaymentMethod = exports.getPaymentMethods = exports.createPaymentMethod = exports.getServices = void 0;
const tslib_1 = require("tslib");
const paymentMethodService_1 = require("../services/paymentMethodService");
const paymentProcessorService_1 = require("../services/paymentProcessorService");
const auth_1 = require("../utils/auth");
const logger_1 = require("../utils/logger");
const zod_1 = require("zod");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const schemas_1 = require("../database/schemas");
const paymentErrors_1 = tslib_1.__importStar(require("../errors/paymentErrors"));
// Factory to create services per-request so tests can inject mocks and resources
// are managed per request. Tests can call getServices(undefined, 'sandbox') or
// mock this exported function.
const getServices = (req, envOverride) => {
    const environment = envOverride !== null && envOverride !== void 0 ? envOverride : (process.env.NODE_ENV === 'production' ? 'production' : 'sandbox');
    const paymentMethodService = new paymentMethodService_1.PaymentMethodService(environment);
    const paymentProcessorService = new paymentProcessorService_1.PaymentProcessorService(environment);
    return { paymentMethodService, paymentProcessorService };
};
exports.getServices = getServices;
// Validation schemas
const _createPaymentMethodBase = zod_1.z.object({
    // Option 1: Client-provided token
    azulToken: zod_1.z.string().optional(),
    // Option 2: Card details
    cardNumber: zod_1.z.string().optional(),
    cvc: zod_1.z.string().optional(),
    expiryMonth: zod_1.z.number().min(1).max(12).optional(),
    expiryYear: zod_1.z.number()
        .min(new Date().getFullYear()) // Cannot be in the past
        .optional(),
    cardHolderName: zod_1.z.string().optional(),
    // Options
    setAsFavorite: zod_1.z.boolean().optional()
});
const createPaymentMethodSchema = _createPaymentMethodBase.refine((data) => Boolean(data.azulToken) || (Boolean(data.cardNumber) && Boolean(data.expiryMonth) && Boolean(data.expiryYear)), {
    message: "Either azulToken or card details (cardNumber, expiryMonth, expiryYear) must be provided"
});
const chargePaymentMethodSchema = zod_1.z.object({
    amount: zod_1.z.number().min(1),
    currency: zod_1.z.enum(['DOP', 'USD']),
    // Use TitleCase to match Azul TrxType values ('Sale' | 'Hold')
    // Backwards-compat: clients should send 'Sale' or 'Hold'
    type: zod_1.z.enum(['Sale', 'Hold']),
    jobId: zod_1.z.string().optional(),
    bidId: zod_1.z.string().optional(),
    idempotencyKey: zod_1.z.string().uuid(),
    browserInfo: zod_1.z.object({
        acceptHeader: zod_1.z.string(),
        userAgent: zod_1.z.string(),
        javaEnabled: zod_1.z.boolean(),
        language: zod_1.z.string(),
        colorDepth: zod_1.z.number(),
        screenHeight: zod_1.z.number(),
        screenWidth: zod_1.z.number(),
        timeZoneOffset: zod_1.z.number(),
        javascriptEnabled: zod_1.z.boolean()
    }).optional(),
    customerInfo: zod_1.z.object({
        email: zod_1.z.string().email().optional(),
        phone: zod_1.z.string().optional(),
        billingAddress: zod_1.z.object({
            line1: zod_1.z.string().optional(),
            line2: zod_1.z.string().optional(),
            city: zod_1.z.string().optional(),
            state: zod_1.z.string().optional(),
            postalCode: zod_1.z.string().optional(),
            countryCode: zod_1.z.string().optional()
        }).optional()
    }).optional()
});
/**
 * POST /payment-methods
 * Create a new payment method
 */
const createPaymentMethod = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const { paymentMethodService } = (0, exports.getServices)(req);
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user) {
            return res.status(401).json({
                success: false,
                error: 'Authentication required'
            });
        }
        // Validate request body
        const validation = createPaymentMethodSchema.safeParse(req.body);
        if (!validation.success) {
            return res.status(400).json({
                success: false,
                error: 'Invalid request data',
                details: validation.error.errors
            });
        }
        const request = validation.data;
        // Create payment method
        const paymentMethod = yield paymentMethodService.createPaymentMethod(user._id, request);
        res.status(201).json({
            success: true,
            data: paymentMethod
        });
    }
    catch (error) {
        console.error('Error creating payment method:', error);
        if (error instanceof paymentErrors_1.default) {
            return res.status(error.statusCode).json({ success: false, error: error.message, code: error.code });
        }
        const errorMessage = error instanceof Error ? error.message : 'Failed to create payment method';
        res.status(500).json({ success: false, error: errorMessage });
    }
});
exports.createPaymentMethod = createPaymentMethod;
/**
 * GET /payment-methods
 * Get all payment methods for the authenticated user
 */
const getPaymentMethods = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const { paymentMethodService } = (0, exports.getServices)(req);
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user) {
            return res.status(401).json({
                success: false,
                error: 'Authentication required'
            });
        }
        const paymentMethods = yield paymentMethodService.getPaymentMethods(user._id);
        res.json({
            success: true,
            data: paymentMethods
        });
    }
    catch (error) {
        console.error('Error fetching payment methods:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch payment methods'
        });
    }
});
exports.getPaymentMethods = getPaymentMethods;
/**
 * PATCH /payment-methods/:id/favorite
 * Set a payment method as favorite
 */
const setFavoritePaymentMethod = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const { paymentMethodService } = (0, exports.getServices)(req);
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user) {
            return res.status(401).json({
                success: false,
                error: 'Authentication required'
            });
        }
        const paymentMethodId = req.params.id;
        if (!paymentMethodId) {
            return res.status(400).json({
                success: false,
                error: 'Payment method ID is required'
            });
        }
        // Validate ObjectId format
        if (!mongoose_1.default.Types.ObjectId.isValid(paymentMethodId)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid payment method ID format'
            });
        }
        const paymentMethod = yield paymentMethodService.setFavoritePaymentMethod(user._id, paymentMethodId);
        res.json({
            success: true,
            data: paymentMethod
        });
    }
    catch (error) {
        console.error('Error setting favorite payment method:', error);
        if (error instanceof paymentErrors_1.default) {
            return res.status(error.statusCode).json({ success: false, error: error.message, code: error.code });
        }
        const errorMessage = error instanceof Error ? error.message : 'Failed to set favorite payment method';
        res.status(500).json({ success: false, error: errorMessage });
    }
});
exports.setFavoritePaymentMethod = setFavoritePaymentMethod;
/**
 * DELETE /payment-methods/:id
 * Delete a payment method
 */
const deletePaymentMethod = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const { paymentMethodService } = (0, exports.getServices)(req);
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user) {
            return res.status(401).json({
                success: false,
                error: 'Authentication required'
            });
        }
        const paymentMethodId = req.params.id;
        if (!paymentMethodId) {
            return res.status(400).json({
                success: false,
                error: 'Payment method ID is required'
            });
        }
        // Validate ObjectId format
        if (!mongoose_1.default.Types.ObjectId.isValid(paymentMethodId)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid payment method ID format'
            });
        }
        yield paymentMethodService.deletePaymentMethod(user._id, paymentMethodId);
        res.json({
            success: true,
            message: 'Payment method deleted successfully'
        });
    }
    catch (error) {
        console.error('Error deleting payment method:', error);
        if (error instanceof paymentErrors_1.default) {
            return res.status(error.statusCode).json({ success: false, error: error.message, code: error.code });
        }
        const errorMessage = error instanceof Error ? error.message : 'Failed to delete payment method';
        res.status(500).json({ success: false, error: errorMessage });
    }
});
exports.deletePaymentMethod = deletePaymentMethod;
/**
 * POST /payment-methods/:id/charge
 * Charge a payment method
 */
const chargePaymentMethod = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const { paymentProcessorService } = (0, exports.getServices)(req);
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user) {
            return res.status(401).json({
                success: false,
                error: 'Authentication required'
            });
        }
        const paymentMethodId = req.params.id;
        if (!paymentMethodId) {
            return res.status(400).json({
                success: false,
                error: 'Payment method ID is required'
            });
        }
        // Validate ObjectId format
        if (!mongoose_1.default.Types.ObjectId.isValid(paymentMethodId)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid payment method ID format'
            });
        }
        // Validate request body
        const validation = chargePaymentMethodSchema.safeParse(req.body);
        if (!validation.success) {
            return res.status(400).json({
                success: false,
                error: 'Invalid request data',
                details: validation.error.errors
            });
        }
        const request = validation.data;
        // Process payment
        const result = yield paymentProcessorService.chargePaymentMethod(user._id, paymentMethodId, request);
        res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        console.error('Error charging payment method:', error);
        if (error instanceof paymentErrors_1.default) {
            return res.status(error.statusCode).json({ success: false, error: error.message, code: error.code });
        }
        const errorMessage = error instanceof Error ? error.message : 'Failed to charge payment method';
        res.status(500).json({ success: false, error: errorMessage });
    }
});
exports.chargePaymentMethod = chargePaymentMethod;
/**
 * POST /payments/3ds/method-notification/:orderId
 * Handle 3DS Method notification from ACS
 */
const handle3DSMethodNotification = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d;
    try {
        const { paymentProcessorService } = (0, exports.getServices)(req);
        const azulOrderId = req.params.orderId;
        if (!azulOrderId) {
            return res.status(400).json({
                success: false,
                error: 'Order ID is required'
            });
        }
        // Find transaction by Azul Order ID
        const transaction = yield schemas_1.PaymentTransaction.findOne({
            providerReference: azulOrderId
        });
        if (!transaction) {
            return res.status(404).json({
                success: false,
                error: 'Transaction not found'
            });
        }
        const notificationData = {
            threeDSServerTransID: req.body.threeDSServerTransID || '',
            threeDSMethodData: req.body.threeDSMethodData
        };
        // Process method notification
        yield paymentProcessorService.processMethodNotification(transaction._id.toString(), notificationData);
        // Return 200 OK quickly as required by ACS
        res.status(200).json({
            success: true
        });
    }
    catch (error) {
        // High-severity logging with context so this isn't silently swallowed
        const context = {
            orderId: req.params.orderId,
            body: req.body,
            transactionId: req._transactionId || 'unknown'
        };
        // Use structured logger if available
        try {
            logger_1.logger.error('Error handling 3DS method notification', Object.assign({ error: error instanceof Error ? error.stack || error.message : String(error) }, context));
        }
        catch (logErr) {
            // Fallback to console if logger fails
            console.error('Error handling 3DS method notification (logger failed):', logErr, 'original error:', error, 'context:', context);
        }
        // Emit monitoring events if integrations are available (no-op otherwise)
        try {
            // Example: Sentry
            (_b = (_a = globalThis.Sentry) === null || _a === void 0 ? void 0 : _a.captureException) === null || _b === void 0 ? void 0 : _b.call(_a, error);
            // Example: Prometheus or custom metrics
            (_d = (_c = globalThis.metrics) === null || _c === void 0 ? void 0 : _c.increment) === null || _d === void 0 ? void 0 : _d.call(_c, 'azul_method_notification_errors');
        }
        catch (monErr) {
            console.warn('Monitoring emission failed for 3DS method notification error', monErr);
        }
        // Still return 200 to ACS as required by the specification
        res.status(200).json({ success: true });
    }
});
exports.handle3DSMethodNotification = handle3DSMethodNotification;
/**
 * POST /payments/3ds/challenge/:transactionId
 * Process 3DS Challenge response
 */
const process3DSChallenge = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const { paymentProcessorService } = (0, exports.getServices)(req);
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user) {
            return res.status(401).json({
                success: false,
                error: 'Authentication required'
            });
        }
        const transactionId = req.params.transactionId;
        if (!transactionId) {
            return res.status(400).json({
                success: false,
                error: 'Transaction ID is required'
            });
        }
        const challengeResponse = req.body.cres;
        if (!challengeResponse) {
            return res.status(400).json({
                success: false,
                error: 'Challenge response (cres) is required'
            });
        }
        // Process challenge
        const result = yield paymentProcessorService.processChallenge(transactionId, challengeResponse);
        res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        console.error('Error processing 3DS challenge:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to process 3DS challenge';
        let statusCode = 500;
        // Prefer explicit error types/codes over message matching
        if (error instanceof paymentErrors_1.NotFoundPaymentError || (error === null || error === void 0 ? void 0 : error.code) === 'NOT_FOUND' || (error === null || error === void 0 ? void 0 : error.status) === 404) {
            statusCode = 404;
        }
        res.status(statusCode).json({
            success: false,
            error: errorMessage
        });
    }
});
exports.process3DSChallenge = process3DSChallenge;
/**
 * POST /payments/:transactionId/refund
 * Refund a transaction
 */
const refundTransaction = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const { paymentProcessorService } = (0, exports.getServices)(req);
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user) {
            return res.status(401).json({
                success: false,
                error: 'Authentication required'
            });
        }
        const transactionId = req.params.transactionId;
        if (!transactionId) {
            return res.status(400).json({
                success: false,
                error: 'Transaction ID is required'
            });
        }
        const { amount, reason } = req.body;
        if (amount !== undefined && (typeof amount !== 'number' || amount <= 0)) {
            return res.status(400).json({
                success: false,
                error: 'Amount must be a positive number'
            });
        }
        yield paymentProcessorService.refundTransaction(user._id, transactionId, amount, reason || 'Customer request');
        res.json({
            success: true,
            message: 'Refund processed successfully'
        });
    }
    catch (error) {
        console.error('Error processing refund:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to process refund';
        let statusCode = 500;
        if (error instanceof paymentErrors_1.NotFoundPaymentError || (error === null || error === void 0 ? void 0 : error.code) === 'NOT_FOUND' || (error === null || error === void 0 ? void 0 : error.status) === 404) {
            statusCode = 404;
        }
        else if (error instanceof paymentErrors_1.InvalidPaymentDataError || (error === null || error === void 0 ? void 0 : error.code) === 'INVALID_PAYMENT_DATA' || (error === null || error === void 0 ? void 0 : error.status) === 400) {
            statusCode = 400;
        }
        else if (error instanceof paymentErrors_1.IdempotencyConflictError || (error === null || error === void 0 ? void 0 : error.code) === 'IDEMPOTENCY_CONFLICT' || (error === null || error === void 0 ? void 0 : error.status) === 409) {
            statusCode = 409;
        }
        res.status(statusCode).json({
            success: false,
            error: errorMessage
        });
    }
});
exports.refundTransaction = refundTransaction;
/**
 * POST /payments/:transactionId/capture
 * Capture a held transaction
 */
const captureTransaction = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const { paymentProcessorService } = (0, exports.getServices)(req);
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user) {
            return res.status(401).json({
                success: false,
                error: 'Authentication required'
            });
        }
        const transactionId = req.params.transactionId;
        if (!transactionId) {
            return res.status(400).json({
                success: false,
                error: 'Transaction ID is required'
            });
        }
        const { amount } = req.body;
        if (amount !== undefined && (typeof amount !== 'number' || amount <= 0)) {
            return res.status(400).json({
                success: false,
                error: 'Amount must be a positive number'
            });
        }
        yield paymentProcessorService.captureTransaction(user._id, transactionId, amount);
        res.json({
            success: true,
            message: 'Transaction captured successfully'
        });
    }
    catch (error) {
        console.error('Error capturing transaction:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to capture transaction';
        let statusCode = 500;
        if (error instanceof paymentErrors_1.NotFoundPaymentError || (error === null || error === void 0 ? void 0 : error.code) === 'NOT_FOUND' || (error === null || error === void 0 ? void 0 : error.status) === 404) {
            statusCode = 404;
        }
        else if (error instanceof paymentErrors_1.InvalidPaymentDataError || (error === null || error === void 0 ? void 0 : error.code) === 'INVALID_PAYMENT_DATA' || (error === null || error === void 0 ? void 0 : error.status) === 400) {
            statusCode = 400;
        }
        res.status(statusCode).json({
            success: false,
            error: errorMessage
        });
    }
});
exports.captureTransaction = captureTransaction;
