import { Request, Response } from 'express';
import { Account, OperatorApplication, OperatorProfile, NotificationLog } from '../database/schemas';
import { getAuthenticatedUser } from '../utils/auth';
import { uploadFileToS3 } from '../utils/upload';
import { v4 as uuidv4 } from 'uuid';
import { getUserNotificationToken, sendCustomNotification } from './notifications';
import jobCategoryService from '../services/jobCategoryService';
import mongoose from 'mongoose';
import { DOCUMENT_REQUIREMENT_CATEGORIES, DocumentRequirementCategory, isDocumentRequirementCategory } from '../types/documentRequirements';

// Helper: notify a single user and log it
async function notifyUser(
  userId: string,
  {
    title,
    message,
    type,
    data = {},
    soundOn = true,
    badgeCount = 1,
  }: { title: string; message: string; type: string; data?: Record<string, any>; soundOn?: boolean; badgeCount?: number }
) {
  const tokenResult = await getUserNotificationToken(userId);
  const payloadData = { type, ...data };
  let notificationSent = false;
  if (tokenResult.success && tokenResult.notificationToken) {
    try {
      await sendCustomNotification(tokenResult.notificationToken, {
        title,
        message,
        data: payloadData,
        soundOn,
        badgeCount,
      });
      notificationSent = true;
    } catch (error) {
      console.error(`Failed to send notification to user ${userId}:`, error);
    }
  }
  try {
    await NotificationLog.create({
      recipientId: userId,
      type,
      title,
      message,
      data: payloadData,
      notificationSent, // Track whether push notification was successful
    });
  } catch (error) {
    console.error(`Failed to log notification for user ${userId}:`, error);
  }
}

// Helper: notify all admins and log per admin
async function notifyAdmins(
  {
    title,
    message,
    type,
    data = {},
    soundOn = true,
    badgeCount = 1,
  }: { title: string; message: string; type: string; data?: Record<string, any>; soundOn?: boolean; badgeCount?: number }
) {
  const admins = await Account.find({ 'booleans.isAdmin': true }).select('_id notifications.expo_push_token user.name');
  await Promise.all(
    admins.map(async (adminDoc) => {
      const adminIdStr = (adminDoc as any)._id?.toString?.() || String((adminDoc as any)._id);
      await notifyUser(adminIdStr, { title, message, type, data, soundOn, badgeCount });
    })
  );
}

// Submit an operator application (existing user)
export const submitOperatorApplication = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    const userId = user?._id?.toString();

    if (!userId) {
      return res.status(401).json({ success: false, error: 'Unauthorized' });
    }

    // Disallow creating a new application if there's already a pending one
    const existingPending = await OperatorApplication.findOne({ applicantId: userId, status: 'pending' });
    if (existingPending) {
      return res.status(400).json({
        success: false,
        error: 'Ya tienes una solicitud pendiente. Debe ser rechazada antes de enviar otra.'
      });
    }

    // Disallow creating an application if the user already has an operator profile
    const existingProfile = await OperatorProfile.findOne({ accountId: userId });
    if (existingProfile) {
      return res.status(400).json({
        success: false,
        error: 'Ya tienes un perfil de operador. No puedes enviar otra solicitud.'
      });
    }

    const { authorizedCategories: rawAuthorizedCategories, description = '', skills: rawSkills = [] } = req.body;

    // Normalize fields to correct types (supports multipart/form-data)
    const parseArray = (input: any): string[] => {
      if (Array.isArray(input)) return input;
      if (typeof input === 'string') {
        try {
          const parsed = JSON.parse(input);
          if (Array.isArray(parsed)) return parsed;
        } catch (e) {
          // Invalid JSON, fall through to split logic
        }
        return input.split(',').map((s: string) => s.trim()).filter(Boolean);
      }
      return [];
    };

    const authorizedCategories = parseArray(rawAuthorizedCategories);
    const skills = parseArray(rawSkills);

    if (!authorizedCategories || !Array.isArray(authorizedCategories) || authorizedCategories.length === 0) {
      return res.status(400).json({ success: false, error: 'Se requiere al menos una categoría autorizada' });
    }

    const normalizedAuthorized = authorizedCategories.map((c: string) => String(c).trim().toLowerCase());

    // Fetch active categories and validate against them
    const activeCategories = await jobCategoryService.getActiveCategories();
    const validCategoryNames = new Set(
      activeCategories.map((cat: any) => String(cat.name).trim().toLowerCase())
    );
    const invalid = normalizedAuthorized.filter((c: string) => !validCategoryNames.has(c));
    if (invalid.length) {
      return res.status(400).json({ success: false, error: `Categorías inválidas: ${invalid.join(', ')}` });
    }

    // Determine required document categories from selected authorized categories
    const categoriesDocsRequired = await Promise.all(
      normalizedAuthorized.map(async (name) => {
        const cat = await jobCategoryService.getCategoryByName(name);
        return (cat?.documents_required as DocumentRequirementCategory[] | undefined) || ['none'];
      })
    );
    const requiredSet = new Set<DocumentRequirementCategory>();
    for (const arr of categoriesDocsRequired) {
      for (const c of arr) {
        if (c && c !== 'none' && isDocumentRequirementCategory(c)) requiredSet.add(c);
      }
    }
    const requiredCategories: DocumentRequirementCategory[] = requiredSet.size > 0 ? Array.from(requiredSet) : ['none'];

    // Upload any provided documents (optional)
    const files = (req.files as Express.Multer.File[]) || [];
    type DocOut = { name: string; url: string; type: string; category?: DocumentRequirementCategory };
    const documents: DocOut[] = [];

    // Parse document-category mapping from body: supports 'documentsMeta' or 'documentCategories'
    const rawMeta = (req.body?.documentsMeta ?? req.body?.documentCategories ?? req.body?.documents_categories) as any;
    const filenameToCategory = new Map<string, DocumentRequirementCategory>();
    const consumeCategory = (v: any): DocumentRequirementCategory | undefined => {
      if (typeof v !== 'string') return undefined;
      const key = v.trim().toLowerCase();
      return isDocumentRequirementCategory(key) ? (key as DocumentRequirementCategory) : undefined;
    };
    try {
      if (rawMeta) {
        const parsed = typeof rawMeta === 'string' ? JSON.parse(rawMeta) : rawMeta;
        if (Array.isArray(parsed)) {
          // Expect array of { filename, category }
          for (const item of parsed) {
            const cat = consumeCategory(item?.category);
            if (cat && typeof item?.filename === 'string') {
              filenameToCategory.set(String(item.filename), cat);
            }
          }
        } else if (parsed && typeof parsed === 'object') {
          // Expect map of filename -> category
          for (const [k, v] of Object.entries(parsed)) {
            const cat = consumeCategory(v);
            if (cat) filenameToCategory.set(k, cat);
          }
        }
      }
    } catch (e) {
      return res.status(400).json({ success: false, error: 'Formato inválido para documentosMeta/documentCategories' });
    }

    // Validate files before processing
    const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    const ALLOWED_MIME_TYPES = new Set<string>([
      'application/pdf',
      'image/jpeg',
      'image/png',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ]);

    for (const file of files) {
      if (file.size > MAX_FILE_SIZE) {
        return res.status(400).json({
          success: false,
          error: `El archivo '${file.originalname}' excede el tamaño máximo de 10MB`
        });
      }
      if (!ALLOWED_MIME_TYPES.has(file.mimetype)) {
        return res.status(400).json({
          success: false,
          error: `Tipo de archivo no permitido para '${file.originalname}'. Solo se permiten PDF, JPEG, PNG, DOC o DOCX`
        });
      }
      try {
         // Sanitize filename to prevent path traversal
        const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9._-]/g, '_');
        const fileName = `operator-applications/${userId}/${uuidv4()}-${sanitizedName}`;
        const url = await uploadFileToS3(file.buffer, fileName);
        const category = filenameToCategory.get(file.originalname);
        documents.push({ name: file.originalname, url, type: file.mimetype, category });
      } catch (error) {
        console.error(`Failed to upload file ${file.originalname}:`, error);
        return res.status(500).json({
          success: false,
          error: `Error al subir el archivo '${file.originalname}'. Por favor, intenta de nuevo.`
        });
      }
    }

    // If some categories are required, ensure at least one document per category has been uploaded and categorized
    if (requiredCategories.length && !(requiredCategories.length === 1 && requiredCategories[0] === 'none')) {
      const uploadedCategories = new Set(
        documents.map((d) => d.category).filter((c): c is DocumentRequirementCategory => !!c)
      );
      const missing = requiredCategories.filter((c) => !uploadedCategories.has(c));
      if (missing.length) {
        return res.status(400).json({
          success: false,
          error: `Faltan documentos requeridos para: ${missing.join(', ')}`,
          requiredCategories,
          received: Array.from(uploadedCategories),
        });
      }
    }

    const application = await OperatorApplication.create({
      applicantId: userId,
      authorizedCategories: normalizedAuthorized,
      description,
      skills,
      documents,
      documents_required: requiredCategories,
      status: 'pending',
    });

    // Notify all admins in Spanish
    const title = 'Nueva solicitud de operador';
    const message = `El usuario ha enviado una solicitud para convertirse en operador.`;
    const applicationIdStr = (application as any)._id?.toString?.() || String((application as any)._id);
    await notifyAdmins({
      title,
      message,
      type: 'operator_request_submitted',
      data: { applicationId: applicationIdStr, applicantId: userId },
      soundOn: true,
      badgeCount: 1,
    });

    // Notify applicant
    await notifyUser(userId, {
      title: 'Solicitud enviada',
      message: 'Tu solicitud para ser operador ha sido enviada y será revisada por un administrador.',
      type: 'operator_request_submitted',
      data: { applicationId: applicationIdStr },
      soundOn: true,
      badgeCount: 1,
    });

    return res.status(201).json({ success: true, application });
  } catch (error) {
    console.error('Error submitting operator application:', error);
    return res.status(500).json({ success: false, error: 'Error interno del servidor' });
  }
};

// Public: compute union of required document categories for provided job categories
export const getApplicationDocumentRequirements = async (req: Request, res: Response) => {
  try {
    const raw = (req.query.categories ?? req.body?.categories) as any;
    const parseArray = (input: any): string[] => {
      if (Array.isArray(input)) return input as string[];
      if (typeof input === 'string') {
        try {
          const p = JSON.parse(input);
          if (Array.isArray(p)) return p as string[];
        } catch {}
        return input.split(',').map((s) => s.trim()).filter(Boolean);
      }
      return [];
    };
    const categories = parseArray(raw).map((s) => s.toLowerCase());
    if (!categories.length) {
      return res.status(400).json({ success: false, error: 'Debe proporcionar categorías' });
    }
    const active = await jobCategoryService.getActiveCategories();
    const activeSet = new Set(active.map((c: any) => String(c.name).toLowerCase()));
    const invalid = categories.filter((c) => !activeSet.has(c));    
    if (invalid.length) {
      return res.status(400).json({ success: false, error: `Categorías inválidas: ${invalid.join(', ')}` });
    }
    const reqs = new Set<DocumentRequirementCategory>();
    for (const c of categories) {
      const cat = await jobCategoryService.getCategoryByName(c);
      const arr = (cat?.documents_required as DocumentRequirementCategory[] | undefined) || ['none'];
      for (const r of arr) {
        if (r !== 'none' && isDocumentRequirementCategory(r)) reqs.add(r);
      }
    }
    const result = reqs.size ? Array.from(reqs) : ['none'];
    return res.json({ success: true, categories: result });
  } catch (error) {
    console.error('Error computing application document requirements:', error);
    return res.status(500).json({ success: false, error: 'Error interno del servidor' });
  }
};

// Admin approves an operator application
export const approveOperatorApplication = async (req: Request, res: Response) => {
  try {
    const admin = getAuthenticatedUser(req);
    const adminId = admin?._id?.toString();
    if (!adminId) return res.status(401).json({ success: false, error: 'Unauthorized' });
    // Enforce admin-only access before proceeding
  if (admin?.role !== 'admin') {
      return res.status(403).json({ success: false, error: 'Admin access required' });
    }

    const { applicationId } = req.params;
    if (!mongoose.Types.ObjectId.isValid(applicationId)) {
      return res.status(400).json({ success: false, error: 'Invalid applicationId' });
    }
    const application = await OperatorApplication.findById(applicationId);
    if (!application) return res.status(404).json({ success: false, error: 'Solicitud no encontrada' });
    if (application.status !== 'pending') return res.status(400).json({ success: false, error: 'La solicitud ya fue revisada' });
    application.status = 'approved';
    application.reviewedBy = new mongoose.Types.ObjectId(adminId);
    application.reviewedAt = new Date();
    await application.save();

    // Notify applicant - approved
    const applicationIdStr = (application as any)._id?.toString?.() || String((application as any)._id);
    await notifyUser(String(application.applicantId), {
      title: 'Solicitud aprobada',
      message: '¡Felicidades! Tu solicitud para ser operador ha sido aprobada.',
      type: 'operator_request_approved',
      data: { applicationId: applicationIdStr },
    });

    // Notify all admins - approved
    await notifyAdmins({
      title: 'Solicitud de operador aprobada',
      message: 'Se ha aprobado una solicitud de operador.',
      type: 'operator_request_approved',
      data: { applicationId: applicationIdStr, applicantId: String(application.applicantId) },
    });
    return res.json({ success: true, application });
  } catch (error) {
    console.error('Error approving application:', error);
    return res.status(500).json({ success: false, error: 'Error interno del servidor' });
  }
};

// Admin rejects an operator application
export const rejectOperatorApplication = async (req: Request, res: Response) => {
  try {
    const admin = getAuthenticatedUser(req);
    const adminId = admin?._id?.toString();
    if (!adminId) return res.status(401).json({ success: false, error: 'Unauthorized' });
    // Enforce admin-only access before proceeding
  if (admin?.role !== 'admin') {
      return res.status(403).json({ success: false, error: 'Admin access required' });
    }

    const { applicationId } = req.params;
    if (!mongoose.Types.ObjectId.isValid(applicationId)) {
      return res.status(400).json({ success: false, error: 'Invalid applicationId' });
    }
    const { note = '' } = req.body;

    const application = await OperatorApplication.findById(applicationId);
    if (!application) return res.status(404).json({ success: false, error: 'Solicitud no encontrada' });
    if (application.status !== 'pending') return res.status(400).json({ success: false, error: 'La solicitud ya fue revisada' });

    application.status = 'rejected';
    application.reviewedBy = new mongoose.Types.ObjectId(adminId);
    application.reviewedAt = new Date();
    application.reviewNote = note;
    await application.save();

    // Notify applicant - rejected (use helper for consistency)
    const title = 'Solicitud rechazada';
    const message = note
      ? `Tu solicitud para ser operador fue rechazada. Motivo: ${note}`
      : 'Tu solicitud para ser operador fue rechazada.';
    const applicationIdStr = (application as any)._id?.toString?.() || String((application as any)._id);

    await notifyUser(String(application.applicantId), {
      title,
      message,
      type: 'operator_request_rejected',
      data: { applicationId: applicationIdStr },
      soundOn: true,
      badgeCount: 1,
    });

    // Also notify all admins with an admin-specific type and payload
    await notifyAdmins({
      title,
      message,
      type: 'operator_request_rejected_admin',
      data: { applicationId: applicationIdStr, note },
      soundOn: true,
      badgeCount: 1,
    });

    return res.json({ success: true, application });
  } catch (error) {
    console.error('Error rejecting application:', error);
    return res.status(500).json({ success: false, error: 'Error interno del servidor' });
  }
};

// List applications (admin)
export const listOperatorApplications = async (req: Request, res: Response) => {
  try {
    // Admin authentication and authorization
    const user = getAuthenticatedUser(req);
  if (!user || user.role !== 'admin') {
      return res.status(403).json({ success: false, error: 'Admin access required' });
    }

    const { page = 1, limit = 20, status } = req.query;
    const pageNum = Math.max(1, parseInt(String(page), 10) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(String(limit), 10) || 20)); // cap at 100
    const query: any = {};
    const validStatuses = ['pending', 'approved', 'rejected'];
    if (status && validStatuses.includes(String(status).toLowerCase())) {
      query.status = String(status).toLowerCase();
    }

    const applications = await OperatorApplication.find(query)
      .sort({ createdAt: -1 })
      .limit(limitNum)
      .skip((pageNum - 1) * limitNum)
      .populate('applicantId', 'user.name user.surname user.username email');

    const total = await OperatorApplication.countDocuments(query);

    return res.json({
      success: true,
      applications,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum),
      },
    });
  } catch (error) {
    console.error('Error listing applications:', error);
    return res.status(500).json({ success: false, error: 'Error interno del servidor' });
  }
};