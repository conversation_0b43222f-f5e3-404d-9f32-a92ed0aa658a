import React, { useRef, useEffect, useContext, useState } from 'react';
import { StyleSheet, Animated, View, RefreshControl, Text, Image, TouchableOpacity, Alert } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';


import Page from '@/components/templates/Page';
import HeaderGreen from '@/components/templates/HeaderGreen';
import { ScreenWrapper } from '@/components/atoms/blur/BlurDrawer';
import { AuthContext } from '@/context/AuthContext';
import HorizontalTwoList from '@/components/molecules/HorizontalTwoList';
import CardTileList from '@/components/atoms/cards/CardTileList';
import { useRouter } from 'expo-router';
import HorizontalScrollableList from '@/components/molecules/HorizontalScrollableList';
import CardBannerList from '@/components/atoms/cards/CardBannerList';
import CardTileListSmall from '@/components/atoms/cards/CardTileListSmall';
import { useBottomSheet } from '@/context/BottomSheetContext';
import Emergency from '@/components/organisms/dashboard/Emergency';
import BottomSheetCustom from '@/components/templates/BottomSheetCustom';





const Index = () => {
  const router = useRouter()
  const { userData } = useContext(AuthContext)
  const { handleToggleBottomSheet } = useBottomSheet()




  const ADS = [
    {
      _id: 1,
      img: require('@/assets/pictures/discount_tag.png'),
      height: 120,
      width: 100,
      right: 10,
      badge: 'Oferta',
      text: 'Descuento 20%  \nde bienvenida',
      onPress: () => {
        router.push({
          pathname: '/dashboard/offer',
          params: {
            offerId: 1
          }
        })
      }
    },
    {
      _id: 2,
      img: require('@/assets/pictures/helmet.png'),
      height: 120,
      width: 140,
      right: 30,
      text: "1'000 DOP \npara nuevos \noperadores",
      badgeColor: '#FFFFFF',
      onPress: () => {
        router.push({
          pathname: '/dashboard/offer',
          params: {
            offerId: 2
          }
        })
      }
    }
  ]
  const CATEGORIES = [
    {
      name: 'Limpieza',
      icon: require('@/assets/icons/broom.png')
    },
    {
      name: 'Construccion',
      icon: require('@/assets/icons/warnel.png')
    },
    {
      name: 'Electricidad',
      icon: require('@/assets/icons/electricity.png')
    },
    {
      name: 'Plomeria',
      icon: require('@/assets/icons/tap.png')
    },
    {
      name: 'Jardinaje',
      icon: require('@/assets/icons/wateringcan.png')
    },
    {
      name: 'Otro',
      icon: ''
    }
  ]
  const HANDY_ADS = [
    {
      _id: 202,
      img: { uri: 'https://img.icons8.com/color/96/air-conditioner.png' },
      height: 120,
      width: 110,
      right: 15,
      badge: '',
      text: '¿Aire Dañado?\nNo esperes màs.',
      badgeColor: '#FFFFFF',
      onPress: () => {
        router.push({
          pathname: '/dashboard/offer',
          params: {
            handymanId: 202,
          },
        });
      },
    },

    {
      _id: 201,
      img: { uri: 'https://img.icons8.com/color/96/bug.png' },
      height: 60,
      width: 80,
      right: 10,
      text: '¿Fumigacion?',
      onPress: () => {
        router.push({
          pathname: '/dashboard/offer',
          params: {
            handymanId: 201,
          },
        });
      },
    },


  ];


  const handleToggleEmergency = () => {
    handleToggleBottomSheet(Emergency(), 700)
  }



  return (


    <Page noPaddingTop noBottomBar alignItems="center" justifyContent="space-between" page="home" style={{ backgroundColor: '#337836' }}>
      <HeaderGreen text=' ' burgerMenu buttonLocation buttonBell />
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scroll}
      >

        {/* GreenBox w/ Header */}
        <View style={styles.box}>

          <View style={{ width: '60%', zIndex: 10 }}>
            <Text style={styles.name}>
              Hola, {userData?.user.name.charAt(0).toUpperCase()}
              {userData?.user.name.slice(1)}
              👋🏼
            </Text>
            <Text style={styles.text}>Necesitas una reparación urgente? Pídela ahora</Text>

            <TouchableOpacity
              style={styles.emergencyButton}
              activeOpacity={0.7}
              onPress={handleToggleEmergency}
            >
              <Text style={{ color: 'tomato', fontFamily: 'Montserrat', fontWeight: 700 }}>⚠️   Emergencia</Text>
            </TouchableOpacity>
          </View>


          <Image
            source={require('@/assets/pictures/toolbox.png')}
            style={{
              zIndex: 9,
              height: 260,
              width: '100%',
              resizeMode: 'cover',
              position: 'absolute',
              top: -0,
              right: -20
            }}
          />

        </View>

        {/* Services Line */}
        <HorizontalTwoList title='Con quien quieres hablar?'>
          <CardTileList onPress={() => {
            router.push('/dashboard/agent')
          }}>
            <Image
              source={require('@/assets/pictures/worker.png')}
              style={{
                width: '85%',
                height: '85%',
                resizeMode: 'stretch',
                position: 'absolute',
                top: -10,
                left: '10%'
              }}
            />
            <View style={{ transform: [{ translateY: -20 }] }}>
              <Text style={{ textAlign: 'center', width: '100%', fontFamily: "Montserrat", fontWeight: 600, fontSize: 16 }}>Agente</Text>
            </View>
          </CardTileList>
          <CardTileList onPress={() => {
            router.push('/dashboard/support')
          }}>
            <Image
              source={require('@/assets/pictures/agent.png')}
              style={{
                width: '55%',
                height: '80%',
                resizeMode: 'stretch',
                position: 'absolute',
                top: -10,
                left: '25%'
              }}
            />
            <View style={{ transform: [{ translateY: -20 }] }}>
              <Text style={{ textAlign: 'center', width: '100%', fontFamily: "Montserrat", fontWeight: 600, fontSize: 16 }}>Soporte</Text>
            </View>
          </CardTileList>
        </HorizontalTwoList>

        {/* HandyAds Bar */}
        <HorizontalScrollableList title='Soluciones rapidas'>
          {
            HANDY_ADS.map(ad => {

              return (
                <CardBannerList
                  key={ad._id}
                  onPress={ad.onPress}
                  badge={ad.badge}
                  badgeColor={ad.badgeColor}
                  text={ad.text}
                  img={
                    <Image
                      source={ad.img}
                      style={{
                        position: 'absolute',
                        right: ad.right,
                        resizeMode: 'stretch',
                        height: ad.height,
                        width: ad.width
                      }}
                    />
                  }
                />
              )
            })
          }


        </HorizontalScrollableList>

        {/* Job Categories */}
        <HorizontalScrollableList title='Selecciona por categoria'>
          <View style={{ height: '100%', width: 15 }} />
          {
            CATEGORIES.map(category => {

              return (
                <CardTileListSmall key={category.name}>
                  <Image
                    source={category.icon}
                    style={{
                      width: 50,
                      height: 50,
                      resizeMode: 'cover'
                    }}
                  />
                  <Text style={{ fontFamily: 'Montserrat', fontSize: 10 }}>{category.name}</Text>
                </CardTileListSmall>
              )
            })
          }
        </HorizontalScrollableList>

        {/* Ads Bar */}
        <HorizontalScrollableList title='Descubre estas opciónes'>
          {
            ADS.map(ad => {

              return (
                <CardBannerList
                  key={ad._id}
                  onPress={ad.onPress}
                  badge={ad.badge}
                  badgeColor={ad.badgeColor}
                  text={ad.text}
                  img={
                    <Image
                      source={ad.img}
                      style={{
                        position: 'absolute',
                        right: ad.right,
                        resizeMode: 'stretch',
                        height: ad.height,
                        width: ad.width
                      }}
                    />
                  }
                />
              )
            })
          }


        </HorizontalScrollableList>




       



        {/* Green Finisher Banner */}
        <View
          style={{
            width: '100%',
            height: 50,
            borderTopLeftRadius: 30,
            borderTopRightRadius: 30,
            backgroundColor: '#337836',
            marginTop: 40
          }}
        />
      </ScrollView>
    </Page>

  );
};

export default Index;

const styles = StyleSheet.create({

  container: {
    flex: 1,
    width: '100%',
    paddingBottom: 30,
    paddingTop: 100,
  },
  scroll: {
    width: '100%',
    backgroundColor: "#fff"
  },

  box: {
    position: 'relative',

    width: '100%',
    height: 200,
    backgroundColor: '#337836',
    borderBottomRightRadius: 85,
    borderBottomLeftRadius: 30,

    paddingTop: 20,
    paddingHorizontal: 20,

    flexDirection: 'row'
  },

  name: {
    fontFamily: "Montserrat",
    fontSize: 20,
    fontWeight: 700,
    color: 'white'
  },
  text: {
    marginTop: 5,
    fontFamily: "Montserrat",
    fontSize: 14,
    fontWeight: 400,
    color: 'white',
    lineHeight: 20,
  },
  emergencyButton: {
    marginTop: 20,
    width: 155,
    color: 'tomato',
    backgroundColor: 'white',
    borderRadius: 20,
    paddingHorizontal: 10,
    paddingVertical: 8,

    alignItems: 'center'
  }
});