import React, { useContext, useEffect } from 'react'
import { Image, StyleSheet,View } from 'react-native'

import Page from '@/components/templates/Page'
import { AuthContext } from '@/context/AuthContext'
import { LinearGradient } from 'expo-linear-gradient'
import SplashScreenLoader from '@/components/atoms/loaders/Splashscreen'


const Index = () => {
  const { handleInitialAuthentication } = useContext(AuthContext)

  const handleSplashScreen = () => {
    setTimeout(() => {
      handleCheckAuthentication()
    }, 1500)
  }

  const handleCheckAuthentication = async () => {
    const call = await handleInitialAuthentication()
  }

  useEffect(() => {

    handleSplashScreen()
  }, [])


  return (
    <Page noPaddingTop alignItems='center' justifyContent='space-between' >
      <View style={{
        flex: 1,
        height: '100%',
        width: '100%',
        flexDirection: 'column',
        justifyContent: 'space-between'
      }}>
        {/* Top Part */}
        <View style={{ width: '100%', height: '60%', position: 'relative' }}>

          <Image
            source={require('@/assets/pictures/landing_secondary.jpg')}
            style={styles.landingImage}
          />
          <LinearGradient
            colors={["#ffffff", "#ffffff00"]}
            start={{ x: 0, y: 1 }}
            end={{ x: 0, y: 0 }}
            style={{ width: '100%', height: 300, position: 'absolute', bottom: 0, left: 0 }}
          />

          <View style={{ alignItems: 'center', justifyContent: 'center', flexDirection: 'column', width: '100%', position: 'absolute', left: 0, bottom: -40 }}>
            <View style={{
              position: 'absolute',
              bottom: -80
            }}>
              <SplashScreenLoader />
            </View>

          </View>
        </View>

        {/* Bottom Part */}
        <View style={{ width: '100%', justifyContent: 'space-between', flexDirection: 'column', paddingBottom: 30, height: '40%' }}>
          <View style={{ width: '100%', alignItems: 'center', paddingTop: 50, gap: 10 }} />
          <View style={{ flexDirection: 'row', justifyContent: 'center', paddingHorizontal: 30, gap: 30 }} />
        </View>
      </View>




    </Page>
  )
}

export default Index

const styles = StyleSheet.create({
  landingImage: {

    height: '100%',
    width: '100%',
    objectFit: 'cover'
  }
})