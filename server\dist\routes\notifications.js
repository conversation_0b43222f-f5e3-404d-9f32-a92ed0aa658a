"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const express_1 = tslib_1.__importDefault(require("express"));
const router = express_1.default.Router();
const middleware_1 = require("../middleware");
const schemas_1 = require("../database/schemas");
const authentication_1 = require("../middleware/authentication");
const jobNotificationService_1 = require("../services/jobNotificationService");
const auth_1 = require("../utils/auth");
router.route("/save-token").post(authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)((req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a;
    const { token } = req.body;
    if (!token) {
        return res
            .status(400)
            .json({ success: false, message: "Token is required" });
    }
    const account = yield schemas_1.Account.findById((_a = req.user) === null || _a === void 0 ? void 0 : _a._id);
    if (!account) {
        return res
            .status(404)
            .json({ success: false, message: "Account not found" });
    }
    account.notifications.expo_push_token = token;
    yield account.save();
    res.status(200).json({
        success: true,
        message: "Notification token retrieved",
    });
})));
// Get user notifications
router.route("/").get(authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)((req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    const user = (0, auth_1.getAuthenticatedUser)(req);
    const userId = user === null || user === void 0 ? void 0 : user._id;
    const { page = 1, limit = 20, unreadOnly = 'false' } = req.query;
    if (!userId) {
        return res.status(401).json({ success: false, error: 'Unauthorized' });
    }
    const result = yield (0, jobNotificationService_1.getUserNotifications)(userId.toString(), Number(page), Number(limit), unreadOnly === 'true');
    if (!result) {
        return res.status(500).json({ success: false, error: 'Failed to get notifications' });
    }
    res.json(Object.assign({ success: true }, result));
})));
// Mark notification as read
router.route("/:notificationId/read").post(authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)((req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    const { notificationId } = req.params;
    const success = yield (0, jobNotificationService_1.markNotificationAsRead)(notificationId);
    if (!success) {
        return res.status(404).json({ success: false, error: 'Notification not found' });
    }
    res.json({
        success: true,
        message: 'Notification marked as read'
    });
})));
// Get unread notification count
router.route("/unread-count").get(authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)((req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    const user = (0, auth_1.getAuthenticatedUser)(req);
    const userId = user === null || user === void 0 ? void 0 : user._id;
    if (!userId) {
        return res.status(401).json({ success: false, error: 'Unauthorized' });
    }
    const { NotificationLog } = yield Promise.resolve().then(() => tslib_1.__importStar(require('../database/schemas')));
    const unreadCount = yield NotificationLog.countDocuments({
        recipientId: userId.toString(),
        isRead: false
    });
    res.json({
        success: true,
        unreadCount
    });
})));
exports.default = router;
