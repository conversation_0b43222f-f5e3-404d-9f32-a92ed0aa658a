import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';

interface CreditCardProps {
    name: string;
    surname: string;
    hiddenCardNumber?: string,
    cardNumber?: string;
    cardExp: Date | null;
    cardCVC: string;
}

const getCardType = (number: string) => {
    if (/^4/.test(number)) return 'visa';
    if (/^5[1-5]/.test(number)) return 'mastercard';
    return 'unknown';
};

const formatCardNumber = (number: string) => {
    return number.replace(/\s+/g, '').replace(/(\d{4})/g, '$1 ').trim();
};

const formatExpiry = (date: Date | null) => {
    if (!date) return ''
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${month}/${year}`;
};

const CreditCard: React.FC<CreditCardProps> = ({
    name,
    surname,
    hiddenCardNumber,
    cardNumber,
    cardExp,
    cardCVC,
}) => {
    const cardType = cardNumber && getCardType(cardNumber);
    const formattedNumber = cardNumber && formatCardNumber(cardNumber);
    const formattedExp = formatExpiry(cardExp);
    //const validCVC = cardCVC.replace(/\D/g, '');
    const validCVC = cardCVC;

    const cardLogos: Record<string, { uri: string }> = {
        visa: {
            uri: 'https://upload.wikimedia.org/wikipedia/commons/4/41/Visa_Logo.png',
        },
        mastercard: {
            uri: 'https://upload.wikimedia.org/wikipedia/commons/0/04/Mastercard-logo.png',
        },
        unknown: {
            uri: 'https://upload.wikimedia.org/wikipedia/commons/a/ac/No_image_available.svg', // This one is still SVG, better replace with a PNG or local icon
        },
    };
    return (
        <View style={styles.card}>
            <View style={styles.logoContainer}>
                {
                    cardType &&
                    <Image source={cardLogos[cardType]} style={styles.logo} />
                }
            </View>
            {
                cardNumber &&
                <Text style={styles.cardNumber}>{formattedNumber}</Text>
            }
            {
                hiddenCardNumber &&
                <Text style={styles.cardNumber}>{hiddenCardNumber}</Text>
            }
            <View style={styles.bottomRow}>
                <View>
                    <Text style={styles.label}>Nombre</Text>
                    <Text style={styles.text}>{name} {surname}</Text>
                </View>
                <View>
                    <Text style={styles.label}>Vencimiento</Text>
                    <Text style={styles.text}>{formattedExp}</Text>
                </View>
                <View>
                    <Text style={styles.label}>CVC</Text>
                    <Text style={styles.text}>{validCVC}</Text>
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    card: {
        width: '90%',
        height: 220,
        backgroundColor: '#1e1e2f',
        borderRadius: 16,
        padding: 20,
        justifyContent: 'space-between',
        shadowColor: '#000',
        shadowOpacity: 0.3,
        shadowRadius: 10,
        shadowOffset: { width: 0, height: 5 },
        alignSelf: 'center',
    },
    logoContainer: {
        height: 40,
        alignItems: 'flex-end',
    },
    logo: {
        width: 60,
        height: 40,
        resizeMode: 'contain',
        borderRadius: 5
    },
    cardNumber: {
        color: '#fff',
        fontSize: 22,
        letterSpacing: 2,
        fontWeight: '600',
        textAlign: 'center',
    },
    bottomRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    label: {
        color: '#aaa',
        fontSize: 10,
        marginBottom: 2,
    },
    text: {
        color: '#fff',
        fontSize: 14,
        fontWeight: '500',
    },
});

export default CreditCard;
