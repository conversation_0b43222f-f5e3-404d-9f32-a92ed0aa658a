import { StyleSheet, TouchableOpacity } from 'react-native'
import React from 'react'




interface CardTileProps {
  onPress?: () => void
  children: React.ReactNode
}

const CardTileListSmall = ({ onPress, children }: CardTileProps) => {
  return (
    <TouchableOpacity
      style={styles.component}
      activeOpacity={0.7}
      onPress={() => {
        onPress && onPress()
      }}
    >
      {children}
    </TouchableOpacity>
  )

}

export default CardTileListSmall

const styles = StyleSheet.create({
  component: {
    width: 80,
    height: 80,

    backgroundColor: '#e8e8e8ff',
    borderRadius: 20,
    zIndex:10,

    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',

    marginRight: 10    
  }
})