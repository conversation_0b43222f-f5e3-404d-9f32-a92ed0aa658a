import { Request, Response } from 'express';
import { getAuthenticatedUser } from '../utils/auth';
import { Payout, Tax } from '../database/schemas';

export const listPayouts = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (!user) return res.status(401).json({ success: false, error: 'Authentication required' });

  const pageParsed = parseInt(String(req.query.page ?? '1'), 10);
  const limitParsed = parseInt(String(req.query.limit ?? '20'), 10);
  const limitClamped = Math.min(Math.max(Number.isNaN(limitParsed) ? 20 : limitParsed, 1), 100);
  const pageClamped = Math.max(Number.isNaN(pageParsed) ? 1 : pageParsed, 1);
    const query: any = {};
    // If operator, only their payouts; if admin role in future, can see all
    if (user.role !== 'admin') {
      query.operatorId = String(user._id);
    }

    const payouts = await Payout.find(query)
      .sort({ createdAt: -1 })
      .limit(limitClamped)
      .skip((pageClamped - 1) * limitClamped)
      .lean();

    res.json({ success: true, data: payouts });
  } catch (error) {
    console.error('Error listing payouts:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

export const listTaxes = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (!user) return res.status(401).json({ success: false, error: 'Authentication required' });

  const pageParsed = parseInt(String((req.query as any).page ?? '1'), 10);
  const limitParsed = parseInt(String((req.query as any).limit ?? '20'), 10);
  const limitClamped = Math.min(Math.max(Number.isNaN(limitParsed) ? 20 : limitParsed, 1), 100);
  const pageClamped = Math.max(Number.isNaN(pageParsed) ? 1 : pageParsed, 1);
  const subjectType = String((req.query as any).subjectType || '');
    const query: any = {};
    if (subjectType && ['commission', 'operator'].includes(String(subjectType))) {
      query.subjectType = subjectType;
    }

    const taxes = await Tax.find(query)
      .sort({ createdAt: -1 })
      .limit(limitClamped)
      .skip((pageClamped - 1) * limitClamped)
      .lean();

    res.json({ success: true, data: taxes });
  } catch (error) {
    console.error('Error listing taxes:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

