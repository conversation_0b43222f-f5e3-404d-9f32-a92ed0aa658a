import mongoose, { Document } from 'mongoose';

export interface ICreditBalance extends Document {
  id: string;
  userId: string; // Reference to Account
  balance: number; // Current balance in minor units (cents/centavos)
  currency: string; // 'DOP', 'USD', etc.
  totalCredits: number; // Total credits ever received
  totalWithdrawals: number; // Total withdrawals ever made
  pendingWithdrawals: number; // Amount currently pending withdrawal
  lastTransactionAt: Date; // Last transaction timestamp
  createdAt: Date;
  updatedAt: Date;
}

export interface ICreditTransaction extends Document {
  id: string;
  userId: string; // Reference to Account
  creditBalanceId: string; // Reference to CreditBalance
  type: 'credit' | 'debit' | 'withdrawal' | 'refund' | 'penalty_compensation';
  amount: number; // Amount in minor units (positive for credits, negative for debits)
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  description: string; // Human-readable description
  metadata: {
    jobId?: string; // Related job if applicable
    bidId?: string; // Related bid if applicable
    cancellationId?: string; // Related cancellation if applicable
    paymentTransactionId?: string; // Related payment transaction
    withdrawalMethod?: 'credit_card' | 'bank_transfer'; // For withdrawals
    originalPaymentMethodId?: string; // For refunds to original payment method
    processingFee?: number; // Any processing fees applied
    reason?: string; // Additional reason/context
  };
  processedAt?: Date; // When the transaction was processed
  createdAt: Date;
  updatedAt: Date;
}

const creditBalanceSchema = new mongoose.Schema<ICreditBalance>({
  userId: {
    type: String,
    required: true,
    ref: 'Account',
    unique: true, // One credit balance per user
    index: true
  },
  balance: {
    type: Number,
    required: true,
    default: 0,
    min: 0 // Balance cannot be negative
  },
  currency: {
    type: String,
    required: true,
    enum: ['DOP', 'USD'],
    default: 'DOP'
  },
  totalCredits: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  totalWithdrawals: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  pendingWithdrawals: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  lastTransactionAt: {
    type: Date,
    default: null,
    index: true
  }
}, {
  timestamps: true
});

const creditTransactionSchema = new mongoose.Schema<ICreditTransaction>({
  userId: {
    type: String,
    required: true,
    ref: 'Account',
    index: true
  },
  creditBalanceId: {
    type: String,
    required: true,
    ref: 'CreditBalance',
    index: true
  },
  type: {
    type: String,
    required: true,
    enum: ['credit', 'debit', 'withdrawal', 'refund', 'penalty_compensation'],
    index: true
  },
  amount: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    required: true,
    enum: ['DOP', 'USD'],
    default: 'DOP'
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'completed', 'failed', 'cancelled'],
    default: 'pending',
    index: true
  },
  description: {
    type: String,
    required: true,
    maxlength: 500
  },
  metadata: {
    jobId: { type: String, ref: 'Job' },
    bidId: { type: String, ref: 'Bid' },
    cancellationId: { type: String, ref: 'CancellationRecord' },
    paymentTransactionId: { type: String, ref: 'PaymentTransaction' },
    withdrawalMethod: { 
      type: String, 
      enum: ['credit_card', 'bank_transfer'] 
    },
    originalPaymentMethodId: { type: String, ref: 'PaymentMethod' },
    processingFee: { type: Number, min: 0 },
    reason: { type: String, maxlength: 1000 }
  },
  processedAt: {
    type: Date,
    default: null,
    index: true
  }
}, {
  timestamps: true
});

// Indexes for efficient querying
creditBalanceSchema.index({ userId: 1 });
creditTransactionSchema.index({ userId: 1, createdAt: -1 });
creditTransactionSchema.index({ type: 1, status: 1 });
creditTransactionSchema.index({ 'metadata.jobId': 1 });

export const CreditBalance = mongoose.model<ICreditBalance>('CreditBalance', creditBalanceSchema);
export const CreditTransaction = mongoose.model<ICreditTransaction>('CreditTransaction', creditTransactionSchema);

export default CreditBalance;
