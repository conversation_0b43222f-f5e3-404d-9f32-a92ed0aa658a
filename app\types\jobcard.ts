import { InterfaceMessageAgent } from "./message_agent"



export interface InterfaceJobCard {
    category: string,
    chatId: {
        _id: string,
        messages: InterfaceMessageAgent[]
    },
    date: string,
    description: string,
    hour: string,
    images: string[],
    ownerId: string,
    price: number,
    status: string,
    
    createdAt: Date,
    updatedAt: Date
    _id: string
}

export interface InterfaceJob {
    biddingDeadline: string,
    category: string,
    chatId: {
        _id: string,
        messages: InterfaceMessageAgent[]
    },
    date: string,
    description: string,
    hour: string,
    images: string[],
    isOwner: boolean,
    ownerId: string,
    price: number,
    status: string,

    totalBids: number,
    userBid: any


    createdAt: Date,
    updatedAt: Date
    _id: string
}