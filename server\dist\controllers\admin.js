"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteOperator = exports.updateOperator = exports.createOperator = exports.getAllOperators = exports.deleteUser = exports.updateUser = exports.createUser = exports.getAllUsers = exports.getSystemStats = void 0;
const tslib_1 = require("tslib");
const schemas_1 = require("../database/schemas");
const auth_1 = require("../utils/auth");
const bcrypt_1 = tslib_1.__importDefault(require("bcrypt"));
/**
 * Utility function to escape regex special characters to prevent ReDoS attacks
 * @param string - The input string to escape
 * @returns Escaped string safe for use in regex patterns
 */
function escapeRegex(string) {
    // Escape all regex special characters: . * + ? ^ $ { } ( ) | [ ] \
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
/**
 * Helper function to create an account (user or operator)
 */
function createAccount(data, userType) {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        const { email, password, name, surname = '', username = '', phone = '', address = '', isVerified = false } = data;
        // Validate required fields
        if (!email || !password || !name) {
            throw new Error('Email, password, and name are required');
        }
        // Validate password complexity
        if (password.length < 8) {
            throw new Error('Password must be at least 8 characters long');
        }
        // Check for password complexity
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
        if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
            throw new Error('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');
        }
        // Check if user already exists
        const existingUser = yield schemas_1.Account.findOne({ email });
        if (existingUser) {
            throw new Error('User with this email already exists');
        }
        // Check username uniqueness if provided
        if (username) {
            const existingUsername = yield schemas_1.Account.findOne({ 'user.username': username });
            if (existingUsername) {
                throw new Error('Username is already taken');
            }
        }
        // Hash password
        const hashedPassword = yield bcrypt_1.default.hash(password, 10);
        // Create account based on type
        const accountData = {
            email,
            password: hashedPassword,
            user: {
                name,
                surname,
                username,
                type: userType
            },
            contacts: {
                phone,
                address
            },
            booleans: {
                isVerified,
                isAdmin: false,
                isOperator: userType === 'operator'
            }
        };
        // Add currentRole for operators
        if (userType === 'operator') {
            accountData.currentRole = 'operator';
        }
        const newAccount = new schemas_1.Account(accountData);
        yield newAccount.save();
        // Remove sensitive data from response
        const accountResponse = newAccount.toObject();
        accountResponse.password = undefined;
        accountResponse.tokens = undefined;
        return accountResponse;
    });
}
/**
 * Get system statistics
 */
const getSystemStats = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const [totalUsers, totalOperators, totalJobs, totalChats, totalBids] = yield Promise.all([
            schemas_1.Account.countDocuments({ 'user.type': 'user' }),
            schemas_1.Account.countDocuments({ 'user.type': 'operator' }),
            schemas_1.Job.countDocuments(),
            schemas_1.Chat.countDocuments(),
            schemas_1.Bid.countDocuments()
        ]);
        const [activeJobs, completedJobs] = yield Promise.all([
            schemas_1.Job.countDocuments({ status: { $in: ['pending', 'accepted', 'in_progress'] } }),
            schemas_1.Job.countDocuments({ status: 'completed' })
        ]);
        res.json({
            success: true,
            stats: {
                users: totalUsers,
                operators: totalOperators,
                jobs: {
                    total: totalJobs,
                    active: activeJobs,
                    completed: completedJobs
                },
                chats: totalChats,
                bids: totalBids
            }
        });
    }
    catch (error) {
        console.error('Error getting system stats:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.getSystemStats = getSystemStats;
/**
 * Get all users with pagination
 */
const getAllUsers = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 20, search = '' } = req.query;
        const pageNum = Number(page);
        const limitNum = Number(limit);
        // Validate and cap pagination parameters
        if (pageNum < 1 || isNaN(pageNum)) {
            return res.status(400).json({ success: false, error: 'Invalid page number' });
        }
        if (limitNum < 1 || limitNum > 100 || isNaN(limitNum)) {
            return res.status(400).json({ success: false, error: 'Limit must be between 1 and 100' });
        }
        let query = { 'user.type': 'user' };
        if (search) {
            const escapedSearch = escapeRegex(search);
            query = Object.assign(Object.assign({}, query), { $or: [
                    { email: { $regex: escapedSearch, $options: 'i' } },
                    { 'user.name': { $regex: escapedSearch, $options: 'i' } },
                    { 'user.surname': { $regex: escapedSearch, $options: 'i' } },
                    { 'user.username': { $regex: escapedSearch, $options: 'i' } }
                ] });
        }
        const users = yield schemas_1.Account.find(query)
            .select('-password -tokens')
            .sort({ createdAt: -1 })
            .limit(limitNum)
            .skip((pageNum - 1) * limitNum);
        const total = yield schemas_1.Account.countDocuments(query);
        res.json({
            success: true,
            users,
            pagination: {
                page: pageNum,
                limit: limitNum,
                total,
                pages: Math.ceil(total / limitNum)
            }
        });
    }
    catch (error) {
        console.error('Error getting users:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.getAllUsers = getAllUsers;
/**
 * Create new user
 */
const createUser = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const accountData = req.body;
        const newUser = yield createAccount(accountData, 'user');
        res.status(201).json({
            success: true,
            user: newUser
        });
    }
    catch (error) {
        console.error('Error creating user:', error);
        if (error instanceof Error) {
            if (error.message === 'Email, password, and name are required' ||
                error.message === 'Password must be at least 8 characters long' ||
                error.message === 'Invalid email format') {
                return res.status(400).json({ success: false, error: error.message });
            }
            if (error.message === 'User with this email already exists' ||
                error.message === 'Username is already taken') {
                return res.status(409).json({ success: false, error: error.message });
            }
        }
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.createUser = createUser;
/**
 * Update user
 */
const updateUser = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const { userId } = req.params;
        const updateData = req.body;
        // Define allowed fields for update
        const allowedFields = ['email', 'password', 'user', 'contacts', 'booleans'];
        const sanitizedData = {};
        for (const field of allowedFields) {
            if (updateData[field] !== undefined) {
                // For booleans field, validate specific properties
                if (field === 'booleans' && updateData[field]) {
                    sanitizedData[field] = {
                        isVerified: updateData[field].isVerified
                    };
                }
                else {
                    sanitizedData[field] = updateData[field];
                }
            }
        }
        // Prevent updating admin status unless the current user is admin (check raw payload to detect attempts)
        if ((updateData === null || updateData === void 0 ? void 0 : updateData.booleans) &&
            Object.prototype.hasOwnProperty.call(updateData.booleans, 'isAdmin') &&
            (user === null || user === void 0 ? void 0 : user.role) !== 'admin') {
            return res.status(403).json({ success: false, error: 'Cannot modify admin status' });
        }
        // Build update object with dot notation for nested fields
        const updateObj = {};
        if (sanitizedData.email)
            updateObj.email = sanitizedData.email;
        if (sanitizedData.password) {
            updateObj.password = yield bcrypt_1.default.hash(sanitizedData.password, 10);
        }
        if (sanitizedData.user) {
            Object.keys(sanitizedData.user).forEach(key => {
                updateObj[`user.${key}`] = sanitizedData.user[key];
            });
        }
        if (sanitizedData.contacts) {
            Object.keys(sanitizedData.contacts).forEach(key => {
                updateObj[`contacts.${key}`] = sanitizedData.contacts[key];
            });
        }
        if (sanitizedData.booleans) {
            updateObj['booleans.isVerified'] = sanitizedData.booleans.isVerified;
        }
        const updatedUser = yield schemas_1.Account.findByIdAndUpdate(userId, updateObj, { new: true, runValidators: true }).select('-password -tokens');
        if (!updatedUser) {
            return res.status(404).json({ success: false, error: 'User not found' });
        }
        res.json({
            success: true,
            user: updatedUser
        });
    }
    catch (error) {
        console.error('Error updating user:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.updateUser = updateUser;
/**
 * Delete user (hard delete)
 */
const deleteUser = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if ((user === null || user === void 0 ? void 0 : user.role) !== 'admin') {
            return res.status(403).json({
                success: false,
                error: 'Only admins can delete users'
            });
        }
        const { userId } = req.params;
        // Fetch the user first
        const targetUser = yield schemas_1.Account.findById(userId);
        if (!targetUser) {
            return res.status(404).json({ success: false, error: 'User not found' });
        }
        if (targetUser.booleans.isAdmin) {
            return res.status(403).json({
                success: false,
                error: 'Cannot delete admin users'
            });
        }
        // Hard delete the user account
        yield schemas_1.Account.findByIdAndDelete(userId);
        res.json({
            success: true,
            message: 'User deleted successfully'
        });
    }
    catch (error) {
        console.error('Error deleting user:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.deleteUser = deleteUser;
/**
 * Get all operators
 */
const getAllOperators = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 20, search = '' } = req.query;
        const pageNum = Number(page);
        const limitNum = Number(limit);
        // Validate and cap pagination parameters
        if (pageNum < 1 || isNaN(pageNum)) {
            return res.status(400).json({ success: false, error: 'Invalid page number' });
        }
        if (limitNum < 1 || limitNum > 100 || isNaN(limitNum)) {
            return res.status(400).json({ success: false, error: 'Limit must be between 1 and 100' });
        }
        const baseOperator = {
            $or: [
                { 'user.type': 'operator' },
                { 'booleans.isOperator': true }
            ]
        };
        let query = baseOperator;
        if (search) {
            const escapedSearch = escapeRegex(search);
            query = {
                $and: [
                    baseOperator,
                    {
                        $or: [
                            { email: { $regex: escapedSearch, $options: 'i' } },
                            { 'user.name': { $regex: escapedSearch, $options: 'i' } },
                            { 'user.surname': { $regex: escapedSearch, $options: 'i' } },
                            { 'user.username': { $regex: escapedSearch, $options: 'i' } }
                        ]
                    }
                ]
            };
        }
        const operators = yield schemas_1.Account.find(query)
            .select('-password -tokens')
            .sort({ createdAt: -1 })
            .limit(limitNum)
            .skip((pageNum - 1) * limitNum);
        const total = yield schemas_1.Account.countDocuments(query);
        res.json({
            success: true,
            operators,
            pagination: {
                page: pageNum,
                limit: limitNum,
                total,
                pages: Math.ceil(total / limitNum)
            }
        });
    }
    catch (error) {
        console.error('Error getting operators:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.getAllOperators = getAllOperators;
/**
 * Create new operator
 */
const createOperator = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const accountData = req.body;
        const newOperator = yield createAccount(accountData, 'operator');
        res.status(201).json({
            success: true,
            operator: newOperator
        });
    }
    catch (error) {
        console.error('Error creating operator:', error);
        if (error instanceof Error) {
            if (error.message === 'Email, password, and name are required' ||
                error.message === 'Password must be at least 8 characters long' ||
                error.message === 'Invalid email format') {
                return res.status(400).json({ success: false, error: error.message });
            }
            if (error.message === 'User with this email already exists' ||
                error.message === 'Username is already taken') {
                return res.status(409).json({ success: false, error: error.message });
            }
        }
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.createOperator = createOperator;
/**
 * Update operator
 */
const updateOperator = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if ((user === null || user === void 0 ? void 0 : user.role) !== 'admin') {
            return res.status(403).json({
                success: false,
                error: 'Only admins can update operators'
            });
        }
        const { operatorId } = req.params;
        const updateData = req.body;
        // Define allowed fields for update
        const allowedFields = ['email', 'user', 'contacts', 'booleans'];
        const sanitizedData = {};
        for (const field of allowedFields) {
            if (updateData[field] !== undefined) {
                // Restrict booleans updates similar to updateUser
                if (field === 'booleans' && updateData[field]) {
                    sanitizedData[field] = {
                        isVerified: updateData[field].isVerified
                    };
                }
                else {
                    sanitizedData[field] = updateData[field];
                }
            }
        }
        // Hash password separately if provided
        if (updateData.password) {
            sanitizedData.password = yield bcrypt_1.default.hash(updateData.password, 10);
        }
        // Build update object with dot notation for nested fields to avoid overwriting entire subdocuments
        const updateObj = {};
        const flatten = (obj, basePath = '') => {
            const out = {};
            Object.entries(obj || {}).forEach(([k, v]) => {
                if (v === undefined)
                    return;
                const path = basePath ? `${basePath}.${k}` : k;
                if (v && typeof v === 'object' && !Array.isArray(v)) {
                    Object.assign(out, flatten(v, path));
                }
                else {
                    out[path] = v;
                }
            });
            return out;
        };
        if (sanitizedData.email !== undefined)
            updateObj.email = sanitizedData.email;
        if (sanitizedData.password !== undefined)
            updateObj.password = sanitizedData.password;
        if (sanitizedData.user)
            Object.assign(updateObj, flatten(sanitizedData.user, 'user'));
        if (sanitizedData.contacts)
            Object.assign(updateObj, flatten(sanitizedData.contacts, 'contacts'));
        if (sanitizedData.booleans && 'isVerified' in sanitizedData.booleans) {
            updateObj['booleans.isVerified'] = sanitizedData.booleans.isVerified;
        }
        const updatedOperator = yield schemas_1.Account.findByIdAndUpdate(operatorId, updateObj, { new: true, runValidators: true }).select('-password -tokens');
        if (!updatedOperator) {
            return res.status(404).json({ success: false, error: 'Operator not found' });
        }
        res.json({
            success: true,
            operator: updatedOperator
        });
    }
    catch (error) {
        console.error('Error updating operator:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.updateOperator = updateOperator;
/**
 * Delete operator (hard delete)
 */
const deleteOperator = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if ((user === null || user === void 0 ? void 0 : user.role) !== 'admin') {
            return res.status(403).json({
                success: false,
                error: 'Only admins can delete operators'
            });
        }
        const { operatorId } = req.params;
        // Fetch the operator first
        const operator = yield schemas_1.Account.findById(operatorId);
        if (!operator) {
            return res.status(404).json({ success: false, error: 'Operator not found' });
        }
        // Hard delete the operator account
        yield schemas_1.Account.findByIdAndDelete(operatorId);
        res.json({
            success: true,
            message: 'Operator deleted successfully'
        });
    }
    catch (error) {
        console.error('Error deleting operator:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.deleteOperator = deleteOperator;
