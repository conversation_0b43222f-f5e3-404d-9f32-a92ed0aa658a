import express from 'express';
import multer from 'multer';
import { AuthenticateTokenOAuth } from '../middleware/authentication';
import { tryCatch, validateJobId } from '../middleware';
import {
  getUserJobs,
  getJob,
  updateJobStatus,
  deleteJob,
  getAvailableJobs,
  createJobDirect,
  uploadJobImages,
  uploadCompletionPhotos,
  rateClientForJob,
  requestMaterialsQuote,
  approveMaterialOrder,
  rejectMaterialOrder,
} from '../controllers/job';

const router = express.Router();

// Configure multer for image uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB per file
    files: 5 // Maximum 5 files per request
  },
  fileFilter: (req, file, cb) => {
    // Only allow image files
    const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Invalid file type. Allowed types: ${allowedMimeTypes.join(', ')}`));
    }
  }
});

// Create job directly (without chat)
router.post('/direct', AuthenticateTokenOAuth, tryCatch(createJobDirect));

// Upload images for a job
router.post('/:jobId/images',
  validateJobId,
  AuthenticateTokenOAuth,
  upload.array('images', 5),
  tryCatch(uploadJobImages)
);

// Upload completion photos for a job
router.post('/:jobId/completion-photos',
  validateJobId,
  AuthenticateTokenOAuth,
  upload.array('photos', 10),
  tryCatch(uploadCompletionPhotos)
);

// Operator rates client for a job
router.post('/:jobId/client-rating',
  validateJobId,
  AuthenticateTokenOAuth,
  tryCatch(rateClientForJob)
);

// Operator requests materials quote for a job
router.post('/:jobId/material-orders',
  validateJobId,
  AuthenticateTokenOAuth,
  tryCatch(requestMaterialsQuote)
);

// Client approves/rejects a material order
router.post('/material-orders/:orderId/approve', AuthenticateTokenOAuth, tryCatch(approveMaterialOrder));
router.post('/material-orders/:orderId/reject', AuthenticateTokenOAuth, tryCatch(rejectMaterialOrder));

// Get available jobs for operators
router.get('/available', AuthenticateTokenOAuth, tryCatch(getAvailableJobs));

// Get user's jobs
router.get('/', AuthenticateTokenOAuth, tryCatch(getUserJobs));

// Get a specific job
router.get('/:jobId', validateJobId, AuthenticateTokenOAuth, tryCatch(getJob));

// Update job status
router.patch('/:jobId/status', validateJobId, AuthenticateTokenOAuth, tryCatch(updateJobStatus));

// Delete a job
router.delete('/:jobId', validateJobId, AuthenticateTokenOAuth, tryCatch(deleteJob));

export default router;
