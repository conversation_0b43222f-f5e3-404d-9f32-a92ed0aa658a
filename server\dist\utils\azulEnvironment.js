"use strict";
/**
 * Environment Configuration Validation for Azul Payment Gateway
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateAzulEnvironment = validateAzulEnvironment;
exports.getAzulEnvironment = getAzulEnvironment;
exports.validatePCICompliance = validatePCICompliance;
exports.validate3DSConfiguration = validate3DSConfiguration;
exports.checkSecurityBestPractices = checkSecurityBestPractices;
function validateAzulEnvironment() {
    const requiredSandboxVars = [
        'AZUL_SANDBOX_MERCHANT_ID',
        'AZUL_SANDBOX_AUTH1',
        'AZUL_SANDBOX_AUTH2'
    ];
    const requiredProductionVars = [
        'AZUL_PROD_MERCHANT_ID',
        'AZUL_PROD_AUTH1',
        'AZUL_PROD_AUTH2'
    ];
    // Check sandbox configuration
    const missingSandboxVars = requiredSandboxVars.filter(varName => !process.env[varName]);
    if (missingSandboxVars.length > 0) {
        console.warn('Missing Azul sandbox environment variables:', missingSandboxVars);
    }
    // Check production configuration
    const missingProductionVars = requiredProductionVars.filter(varName => !process.env[varName]);
    if (missingProductionVars.length > 0) {
        console.warn('Missing Azul production environment variables:', missingProductionVars);
    }
    // If we're in production and missing production vars, throw error
    if (process.env.NODE_ENV === 'production' && missingProductionVars.length > 0) {
        throw new Error(`Missing required Azul production environment variables: ${missingProductionVars.join(', ')}`);
    }
    return {
        sandbox: {
            merchantId: process.env.AZUL_SANDBOX_MERCHANT_ID || '',
            auth1: process.env.AZUL_SANDBOX_AUTH1 || '',
            auth2: process.env.AZUL_SANDBOX_AUTH2 || '',
            primaryEndpoint: process.env.AZUL_SANDBOX_PRIMARY_ENDPOINT || 'https://pruebas.azul.com.do/WebServices/JSON',
            secondaryEndpoint: process.env.AZUL_SANDBOX_SECONDARY_ENDPOINT
        },
        production: {
            merchantId: process.env.AZUL_PROD_MERCHANT_ID || '',
            auth1: process.env.AZUL_PROD_AUTH1 || '',
            auth2: process.env.AZUL_PROD_AUTH2 || '',
            primaryEndpoint: process.env.AZUL_PROD_PRIMARY_ENDPOINT || 'https://pagos.azul.com.do/WebServices/JSON',
            secondaryEndpoint: process.env.AZUL_PROD_SECONDARY_ENDPOINT
        }
    };
}
function getAzulEnvironment() {
    return process.env.NODE_ENV === 'production' ? 'production' : 'sandbox';
}
/**
 * PCI Compliance Helper
 */
function validatePCICompliance() {
    var _a;
    const warnings = [];
    // Check if we're handling card data server-side
    if (process.env.AZUL_ACCEPT_CARD_DATA === 'true') {
        warnings.push('Server is configured to accept card data. Ensure PCI-DSS compliance (SAQ D).');
    }
    // Check HTTPS in production
    if (process.env.NODE_ENV === 'production' && !((_a = process.env.FRONTEND_URL) === null || _a === void 0 ? void 0 : _a.startsWith('https://'))) {
        warnings.push('Frontend URL should use HTTPS in production for PCI compliance.');
    }
    // Check secure logging
    if (process.env.LOG_CARD_DATA === 'true') {
        warnings.push('Card data logging is enabled. This violates PCI-DSS requirements.');
    }
    return warnings;
}
/**
 * 3DS Configuration Validation
 */
function validate3DSConfiguration() {
    // Prefer a backend callback base for 3DS callbacks to avoid leaking
    // callbacks to the frontend and to ensure correct host/origin.
    const callbackBaseRaw = process.env.AZUL_3DS_CALLBACK_BASE_URL || process.env.BACKEND_URL || process.env.FRONTEND_URL;
    if (!callbackBaseRaw) {
        throw new Error('One of AZUL_3DS_CALLBACK_BASE_URL, BACKEND_URL or FRONTEND_URL must be set for 3DS redirects');
    }
    let baseUrl;
    try {
        baseUrl = new URL(callbackBaseRaw);
    }
    catch (err) {
        throw new Error(`Invalid 3DS callback base URL: ${callbackBaseRaw}`);
    }
    // Enforce HTTPS for production deployments
    if (process.env.NODE_ENV === 'production' && baseUrl.protocol !== 'https:') {
        throw new Error('3DS callback base URL must use https:// in production');
    }
    // If BACKEND_URL is provided, ensure the chosen callback base is same-origin as BACKEND_URL
    if (process.env.BACKEND_URL) {
        try {
            const backendOrigin = new URL(process.env.BACKEND_URL).origin;
            if (baseUrl.origin !== backendOrigin) {
                throw new Error(`3DS callback base URL origin (${baseUrl.origin}) does not match BACKEND_URL origin (${backendOrigin})`);
            }
        }
        catch (err) {
            // Re-throw with a clearer message
            throw new Error(`Invalid BACKEND_URL or mismatch with 3DS callback base: ${err.message}`);
        }
    }
    // Use URL constructor to join paths safely and avoid double slashes
    const termUrl = new URL('/api/payments/3ds/return', baseUrl).toString();
    const methodNotificationUrl = new URL('/api/payments/3ds/method-notification', baseUrl).toString();
    console.log('3DS Configuration:');
    console.log('- Callback base:', baseUrl.toString());
    console.log('- Term URL:', termUrl);
    console.log('- Method Notification URL:', methodNotificationUrl);
    return {
        termUrl,
        methodNotificationUrl
    };
}
/**
 * Payment Security Best Practices Check
 */
function checkSecurityBestPractices() {
    var _a;
    const recommendations = [];
    // Check JWT secret strength
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret || jwtSecret.length < 32) {
        recommendations.push('JWT_SECRET should be at least 32 characters long for security.');
    }
    // Check database encryption
    if (!((_a = process.env.MONGO_URI) === null || _a === void 0 ? void 0 : _a.includes('ssl=true'))) {
        recommendations.push('Consider enabling SSL for MongoDB connections in production.');
    }
    // Check rate limiting
    if (!process.env.ENABLE_RATE_LIMITING || process.env.ENABLE_RATE_LIMITING !== 'true') {
        recommendations.push('Consider enabling rate limiting for payment endpoints.');
    }
    return recommendations;
}
