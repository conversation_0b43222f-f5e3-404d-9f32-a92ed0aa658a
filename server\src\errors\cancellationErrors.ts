export class CancellationError extends Error {
    public readonly statusCode: number;
    public readonly code?: string;
    
    constructor(message: string, statusCode = 500, code?: string, cause?: unknown) {
        super(message);
        this.name = 'CancellationError';
        
        const ctor = (new.target || CancellationError) as Function;
        Object.setPrototypeOf(this, ctor.prototype);
        
        if (typeof (Error as any).captureStackTrace === 'function') {
            (Error as any).captureStackTrace(this, ctor as any);
        }
        
        this.statusCode = statusCode;
        this.code = code;
        if (cause !== undefined) {
            try {
                (this as any).cause = cause;
            } catch {
                // ignore
            }
        }
    }
}

export class InvalidCancellationError extends CancellationError {
    constructor(message = 'Invalid cancellation request') {
        super(message, 400, 'INVALID_CANCELLATION');
        this.name = 'InvalidCancellationError';
    }
}

export class CancellationNotAllowedError extends CancellationError {
    constructor(message = 'Cancellation not allowed at this time') {
        super(message, 403, 'CANCELLATION_NOT_ALLOWED');
        this.name = 'CancellationNotAllowedError';
    }
}

export class CancellationAlreadyProcessedError extends CancellationError {
    constructor(message = 'Cancellation has already been processed') {
        super(message, 409, 'CANCELLATION_ALREADY_PROCESSED');
        this.name = 'CancellationAlreadyProcessedError';
    }
}

export class InsufficientCreditBalanceError extends CancellationError {
    constructor(message = 'Insufficient credit balance') {
        super(message, 400, 'INSUFFICIENT_CREDIT_BALANCE');
        this.name = 'InsufficientCreditBalanceError';
    }
}

export class WithdrawalNotAllowedError extends CancellationError {
    constructor(message = 'Withdrawal not allowed') {
        super(message, 403, 'WITHDRAWAL_NOT_ALLOWED');
        this.name = 'WithdrawalNotAllowedError';
    }
}

export class WithdrawalProcessingError extends CancellationError {
    constructor(message = 'Withdrawal processing failed') {
        super(message, 502, 'WITHDRAWAL_PROCESSING_ERROR');
        this.name = 'WithdrawalProcessingError';
    }
}

export class PenaltyProcessingError extends CancellationError {
    constructor(message = 'Penalty processing failed') {
        super(message, 500, 'PENALTY_PROCESSING_ERROR');
        this.name = 'PenaltyProcessingError';
    }
}

export class JobNotCancellableError extends CancellationError {
    constructor(message = 'Job cannot be cancelled in its current state') {
        super(message, 400, 'JOB_NOT_CANCELLABLE');
        this.name = 'JobNotCancellableError';
    }
}

export class UnauthorizedCancellationError extends CancellationError {
    constructor(message = 'Not authorized to cancel this job') {
        super(message, 403, 'UNAUTHORIZED_CANCELLATION');
        this.name = 'UnauthorizedCancellationError';
    }
}

export class CancellationTimeoutError extends CancellationError {
    constructor(message = 'Cancellation request timed out') {
        super(message, 408, 'CANCELLATION_TIMEOUT');
        this.name = 'CancellationTimeoutError';
    }
}

export class RatingUpdateError extends CancellationError {
    constructor(message = 'Failed to update operator rating') {
        super(message, 500, 'RATING_UPDATE_ERROR');
        this.name = 'RatingUpdateError';
    }
}

export class NotificationError extends CancellationError {
    constructor(message = 'Failed to send cancellation notification') {
        super(message, 500, 'NOTIFICATION_ERROR');
        this.name = 'NotificationError';
    }
}

export default CancellationError;
