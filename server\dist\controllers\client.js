"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getClientReputation = void 0;
const tslib_1 = require("tslib");
const mongoose_1 = require("mongoose");
const schemas_1 = require("../database/schemas");
const getClientReputation = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { clientId } = req.params;
        const threshold = Math.max(1, Number(req.query.threshold) || 2);
        if (!clientId)
            return res.status(400).json({ success: false, error: 'clientId required' });
        if (!(0, mongoose_1.isValidObjectId)(clientId))
            return res.status(400).json({ success: false, error: 'invalid clientId' });
        const ratings = yield schemas_1.ClientRating.find({ clientId }).lean();
        if (!ratings || ratings.length === 0) {
            return res.json({ success: true, data: { averageStars: null, totalRatings: 0, positiveAdjectives: [], negativeAdjectives: [] } });
        }
        const totalRatings = ratings.length;
        const averageStars = ratings.reduce((acc, r) => acc + (r.stars || 0), 0) / totalRatings;
        const posCounts = {};
        const negCounts = {};
        for (const r of ratings) {
            (((_a = r.adjectives) === null || _a === void 0 ? void 0 : _a.positive) || []).forEach(adj => { posCounts[adj] = (posCounts[adj] || 0) + 1; });
            (((_b = r.adjectives) === null || _b === void 0 ? void 0 : _b.negative) || []).forEach(adj => { negCounts[adj] = (negCounts[adj] || 0) + 1; });
        }
        const positiveAdjectives = Object.entries(posCounts)
            .map(([adjective, count]) => ({ adjective, count }))
            .sort((a, b) => b.count - a.count);
        const negativeAdjectives = Object.entries(negCounts)
            .map(([adjective, count]) => ({ adjective, count }))
            .filter(item => item.count >= threshold)
            .sort((a, b) => b.count - a.count);
        res.json({
            success: true,
            data: {
                averageStars: Number(averageStars.toFixed(2)),
                totalRatings,
                positiveAdjectives,
                negativeAdjectives,
                threshold,
            }
        });
    }
    catch (error) {
        console.error('Error getting client reputation:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.getClientReputation = getClientReputation;
