import NodeCache from 'node-cache';

// In-memory cache with TTL
class CacheService {
  private cache: NodeCache;
  
  constructor() {
    // TTL of 30 minutes for chat objects
    this.cache = new NodeCache({ 
      stdTTL: 1800, // 30 minutes
      checkperiod: 600, // Check for expired keys every 10 minutes
      useClones: false // Don't clone objects for better performance
    });
  }

  // Chat-specific cache methods
  getChatKey(chatId: string): string {
    return `chat:${chatId}`;
  }

  getUserChatsKey(userId: string, page: number, limit: number): string {
    return `user_chats:${userId}:${page}:${limit}`;
  }

  // Generic cache operations
  set(key: string, value: any, ttl?: number): boolean {
    if (ttl !== undefined) {
      return this.cache.set(key, value, ttl);
    }
    return this.cache.set(key, value);
  }

  get<T>(key: string): T | undefined {
    return this.cache.get<T>(key);
  }

  del(key: string): number {
    return this.cache.del(key);
  }

  // Cache invalidation methods
  invalidateChat(chatId: string): void {
    this.del(this.getChatKey(chatId));
  }

  invalidateUserChats(userId: string): void {
    // Get all keys that match the pattern and delete them
    const keys = this.cache.keys();
    const userChatKeys = keys.filter(key => key.startsWith(`user_chats:${userId}:`));
    if (userChatKeys.length > 0) {
      this.cache.del(userChatKeys);
    }
  }

  // Cache chat object
  setChat(chatId: string, chat: any): boolean {
    return this.set(this.getChatKey(chatId), chat, 1800); // 30 minutes
  }

  getChat(chatId: string): any {
    return this.get(this.getChatKey(chatId));
  }

  // Cache user chats list
  setUserChats(userId: string, page: number, limit: number, chats: any, ttl: number = 300): boolean {
    return this.set(this.getUserChatsKey(userId, page, limit), chats, ttl); // 5 minutes for lists
  }

  getUserChats(userId: string, page: number, limit: number): any {
    return this.get(this.getUserChatsKey(userId, page, limit));
  }

  // Clear all cache
  flushAll(): void {
    this.cache.flushAll();
  }

  // Get cache statistics
  getStats() {
    return this.cache.getStats();
  }
}

// Export singleton instance
export default new CacheService();
