"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JOB_STATUSES = void 0;
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
// Centralized job status constants and type
exports.JOB_STATUSES = ['pending', 'accepted', 'in_progress', 'completed', 'pending_refund', 'cancelled'];
const jobSchema = new mongoose_1.default.Schema({
    ownerId: {
        type: String,
        required: true,
        ref: 'Account'
    },
    chatId: {
        type: String,
        ref: 'Chat'
    },
    category: {
        type: String,
        required: true
    },
    description: {
        type: String,
        required: true
    },
    images: [{
            type: String, // S3 URLs
            //required: true
        }],
    price: {
        type: Number,
        //required: true,
        default: 0
    },
    date: {
        type: String,
        required: true,
        match: /^\d{2}\/\d{2}\/\d{4}$/ // DD/MM/YYYY format
    },
    hour: {
        type: String,
        required: true,
        match: /^\d{2}:\d{2}$/ // HH:MM format
    },
    // Computed UTC timestamp for the scheduled job. This should be set when creating/updating jobs
    scheduledAt: {
        type: Date,
        default: null,
        index: true
    },
    status: {
        type: String,
        enum: exports.JOB_STATUSES,
        default: 'pending'
    },
    acceptedBidId: {
        type: String,
        ref: 'Bid'
    },
    assignedOperatorId: {
        type: String,
        ref: 'Account'
    },
    finalPrice: {
        type: Number
    },
    biddingDeadline: {
        type: Date
    },
    // Geolocation for job site
    location: {
        type: new mongoose_1.default.Schema({
            latitude: {
                type: Number,
                required: true,
                min: -90,
                max: 90,
                validate: {
                    validator: Number.isFinite,
                    message: 'latitude must be a finite number'
                }
            },
            longitude: {
                type: Number,
                required: true,
                min: -180,
                max: 180,
                validate: {
                    validator: Number.isFinite,
                    message: 'longitude must be a finite number'
                }
            },
        }, { _id: false }),
        required: false,
        default: null,
        validate: {
            validator: function (v) {
                // Allow null (not provided)
                if (v === null || v === undefined)
                    return true;
                // Must be an object with numeric latitude and longitude within ranges
                if (typeof v !== 'object')
                    return false;
                const lat = v.latitude;
                const lon = v.longitude;
                return (typeof lat === 'number' && Number.isFinite(lat) && lat >= -90 && lat <= 90 &&
                    typeof lon === 'number' && Number.isFinite(lon) && lon >= -180 && lon <= 180);
            },
            message: 'location must be null or an object with latitude (-90..90) and longitude (-180..180) as finite numbers'
        }
    },
    geofenceRadiusMeters: {
        type: Number,
        default: 100, // 100m default radius
        min: 10,
        max: 5000
    },
    // Completion photos metadata
    completionPhotos: { type: [String], default: [] },
    minCompletionPhotos: {
        type: Number,
        default: 3,
        min: 0,
        max: 20
    },
    cancelledAt: {
        type: Date,
        default: null
    },
    cancelReason: {
        type: String,
        default: null
    },
    totalBids: {
        type: Number,
        default: 0
    },
    createdBy: {
        type: String,
        enum: ['chat', 'direct'],
        default: 'chat'
    }
}, {
    timestamps: true
});
// Index for efficient querying
jobSchema.index({ ownerId: 1, createdAt: -1 });
jobSchema.index({ status: 1 });
jobSchema.index({ category: 1 });
exports.default = mongoose_1.default.model('Job', jobSchema);
