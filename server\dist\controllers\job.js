"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.rejectMaterialOrder = exports.approveMaterialOrder = exports.requestMaterialsQuote = exports.rateClientForJob = exports.uploadCompletionPhotos = exports.uploadJobImages = exports.createJobDirect = exports.getAvailableJobs = exports.createJobFromChat = exports.deleteJob = exports.updateJobStatus = exports.getJob = exports.getUserJobs = void 0;
const tslib_1 = require("tslib");
const schemas_1 = require("../database/schemas");
const _job_1 = require("../database/schemas/_job");
const auth_1 = require("../utils/auth");
const parseJobDateTime_1 = require("../utils/parseJobDateTime");
const jobNotificationService_1 = require("../services/jobNotificationService");
const operatorProfile_1 = require("./operatorProfile");
const jobCategoryService_1 = tslib_1.__importDefault(require("../services/jobCategoryService"));
const upload_1 = require("../utils/upload");
const uuid_1 = require("uuid");
const dateValidation_1 = require("../utils/dateValidation");
// Haversine distance in meters
function haversineDistanceMeters(a, b) {
    const toRad = (deg) => (deg * Math.PI) / 180;
    const R = 6371000; // Earth radius in meters
    const dLat = toRad(b.latitude - a.latitude);
    const dLon = toRad(b.longitude - a.longitude);
    const lat1 = toRad(a.latitude);
    const lat2 = toRad(b.latitude);
    const sinDLat = Math.sin(dLat / 2);
    const sinDLon = Math.sin(dLon / 2);
    const h = sinDLat * sinDLat + Math.cos(lat1) * Math.cos(lat2) * sinDLon * sinDLon;
    const c = 2 * Math.atan2(Math.sqrt(h), Math.sqrt(1 - h));
    return R * c;
}
// Helper function to safely compare IDs regardless of their type representation
function isSameId(id1, id2) {
    if (!id1 || !id2)
        return false;
    // Convert both to string and trim to ensure consistent comparison
    return String(id1).trim() === String(id2).trim();
}
const getUserJobs = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { page = 1, limit = 10, status, role } = req.query;
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        let query = {};
        // If role is 'operator', get jobs assigned to this operator
        // If role is 'user' or not specified, get jobs owned by this user
        if (role === 'operator') {
            query.assignedOperatorId = String(userId);
        }
        else {
            query.ownerId = String(userId);
        }
        if (status) {
            query.status = status;
        }
        const jobs = yield schemas_1.Job.find(query)
            .sort({ createdAt: -1 })
            .limit(Number(limit))
            .skip((Number(page) - 1) * Number(limit))
            .populate({
            path: 'chatId',
            select: 'messages status',
            match: { _id: { $exists: true } } // Only populate if chatId exists
        })
            .populate('acceptedBidId')
            .populate('assignedOperatorId', 'user.name user.surname user.username user.profile_picture');
        // Add bid count and current user's bid status for each job
        const jobsWithBidInfo = yield Promise.all(jobs.map((job) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
            const jobObj = job.toObject();
            // Get total bids count
            const totalBids = yield schemas_1.Bid.countDocuments({ jobId: job._id });
            // If user is viewing as operator, check if they have a bid on this job
            if (role === 'operator') {
                const userBid = yield schemas_1.Bid.findOne({
                    jobId: job._id,
                    operatorId: userId
                });
                jobObj.userBid = userBid;
            }
            jobObj.totalBids = totalBids;
            return jobObj;
        })));
        const total = yield schemas_1.Job.countDocuments(query);
        res.json({
            success: true,
            jobs: jobsWithBidInfo,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total,
                pages: Math.ceil(total / Number(limit))
            }
        });
    }
    catch (error) {
        console.error('Error getting user jobs:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.getUserJobs = getUserJobs;
const getJob = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { jobId } = req.params;
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        const job = yield schemas_1.Job.findById(jobId)
            .populate({
            path: 'chatId',
            select: 'messages status',
            match: { _id: { $exists: true } } // Only populate if chatId exists
        })
            .populate('acceptedBidId')
            .populate('assignedOperatorId', 'user.name user.surname user.username user.profile_picture');
        if (!job) {
            return res.status(404).json({ success: false, error: 'Job not found' });
        }
        // Check access permissions
        const isOwner = isSameId(job.ownerId, userId.toString());
        const isAssignedOperator = job.assignedOperatorId && isSameId(job.assignedOperatorId.toString(), userId.toString());
        if (!isOwner && !isAssignedOperator) {
            return res.status(403).json({ success: false, error: 'Access denied' });
        }
        // Get additional bid information
        const totalBids = yield schemas_1.Bid.countDocuments({ jobId });
        const userBid = yield schemas_1.Bid.findOne({ jobId, operatorId: userId });
        const jobResponse = Object.assign(Object.assign({}, job.toObject()), { totalBids, userBid: userBid || null, isOwner,
            isAssignedOperator });
        res.json({
            success: true,
            job: jobResponse
        });
    }
    catch (error) {
        console.error('Error getting job:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.getJob = getJob;
const updateJobStatus = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { jobId } = req.params;
        const { status, rating, currentLocation } = req.body; // rating optional, location used for GPS validation
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        const validStatuses = _job_1.JOB_STATUSES;
        if (!status || !validStatuses.includes(status)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid status. Must be one of: ' + validStatuses.join(', ')
            });
        }
        const job = yield schemas_1.Job.findById(jobId);
        if (!job) {
            return res.status(404).json({ success: false, error: 'Job not found' });
        }
        // Check permissions based on status change
        const isOwner = isSameId(job.ownerId, userId.toString());
        const isAssignedOperator = job.assignedOperatorId && isSameId(job.assignedOperatorId.toString(), userId.toString());
        // Only job owner can cancel
        if (status === 'cancelled' && !isOwner) {
            return res.status(403).json({ success: false, error: 'Only job owner can cancel the job' });
        }
        // Only assigned operator can start the job
        if (status === 'in_progress' && !isAssignedOperator) {
            return res.status(403).json({ success: false, error: 'Only assigned operator can start the job' });
        }
        // Only owner or assigned operator can mark as completed
        if (status === 'completed' && !(isOwner || isAssignedOperator)) {
            return res.status(403).json({ success: false, error: 'Only job owner or assigned operator can mark completed' });
        }
        // Validate status transitions
        const validTransitions = {
            'pending': ['accepted', 'cancelled'],
            'accepted': ['in_progress', 'cancelled'],
            'in_progress': ['completed', 'cancelled'],
            'completed': [], // Final state
            'cancelled': [] // Final state
        };
        if (!job.status || !validTransitions[job.status].includes(status)) {
            return res.status(400).json({
                success: false,
                error: `Cannot change status from ${job.status || 'null'} to ${status}`
            });
        }
        // GPS validation when starting the job
        if (status === 'in_progress' && job.location) {
            // Accept on-body location until WebSocket presence is wired; if not provided, instruct client
            if (!currentLocation || typeof currentLocation.latitude !== 'number' || typeof currentLocation.longitude !== 'number') {
                return res.status(400).json({
                    success: false,
                    error: 'Current location required to start the job',
                    code: 'GPS_REQUIRED'
                });
            }
            const distance = haversineDistanceMeters({ latitude: currentLocation.latitude, longitude: currentLocation.longitude }, { latitude: job.location.latitude, longitude: job.location.longitude });
            const radius = (_a = job.geofenceRadiusMeters) !== null && _a !== void 0 ? _a : 100;
            if (distance > radius) {
                return res.status(403).json({
                    success: false,
                    error: `Too far from job site to start. Distance: ${Math.round(distance)}m, required ${radius}m`,
                    code: 'OUT_OF_GEOFENCE',
                    data: { distance, radius }
                });
            }
        }
        // Enforce minimum completion photos before marking job completed
        if (status === 'completed') {
            const minPhotos = (_b = job.minCompletionPhotos) !== null && _b !== void 0 ? _b : 3;
            const count = Array.isArray(job.completionPhotos) ? job.completionPhotos.length : 0;
            if (count < minPhotos) {
                return res.status(400).json({
                    success: false,
                    error: `At least ${minPhotos} completion photos are required before completing the job`,
                    code: 'INSUFFICIENT_COMPLETION_PHOTOS',
                    data: { required: minPhotos, uploaded: count }
                });
            }
        }
        job.status = status;
        yield job.save();
        // Handle job completion (operator rating update backward-compat)
        if (status === 'completed' && job.assignedOperatorId) {
            if (rating && rating >= 1 && rating <= 5) {
                yield (0, operatorProfile_1.updateOperatorRating)(job.assignedOperatorId.toString(), rating);
            }
        }
        res.json({
            success: true,
            message: 'Job status updated successfully',
            job
        });
    }
    catch (error) {
        console.error('Error updating job status:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.updateJobStatus = updateJobStatus;
const deleteJob = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { jobId } = req.params;
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        const job = yield schemas_1.Job.findById(jobId);
        if (!job) {
            return res.status(404).json({ success: false, error: 'Job not found' });
        }
        if (!isSameId(job.ownerId, userId.toString())) {
            return res.status(403).json({ success: false, error: 'Access denied' });
        }
        // Don't allow deletion if job has been accepted or is in progress
        if (job.status && ['accepted', 'in_progress'].includes(job.status)) {
            return res.status(400).json({
                success: false,
                error: 'Cannot delete job that has been accepted or is in progress'
            });
        }
        // Delete associated bids first
        yield schemas_1.Bid.deleteMany({ jobId });
        // Delete the job
        yield schemas_1.Job.findByIdAndDelete(jobId);
        res.json({
            success: true,
            message: 'Job deleted successfully'
        });
    }
    catch (error) {
        console.error('Error deleting job:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.deleteJob = deleteJob;
// Create a new job (called after chat status becomes 'done')
const createJobFromChat = (chatId, ownerId) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { Chat } = yield Promise.resolve().then(() => tslib_1.__importStar(require('../database/schemas')));
        // Get chat data
        const chat = yield Chat.findById(chatId);
        if (!chat) {
            throw new Error('Chat not found');
        }
        if (chat.status !== 'done') {
            throw new Error('Chat must be in done status to create job');
        }
        // Validate required fields
        if (!chat.category || !chat.description || !chat.price || !chat.date || !chat.hour) {
            throw new Error('Missing required job details in chat');
        }
        // Get user's timezone for scheduledAt calculation
        const userAccount = yield schemas_1.Account.findById(ownerId).select('settings.timezone').lean();
        const timezone = ((_a = userAccount === null || userAccount === void 0 ? void 0 : userAccount.settings) === null || _a === void 0 ? void 0 : _a.timezone) || 'Europe/Paris';
        // Calculate scheduledAt from date/hour in user's timezone
        let scheduledAt = null;
        try {
            scheduledAt = (0, parseJobDateTime_1.parseJobDateTime)(chat.date, chat.hour, timezone);
        }
        catch (error) {
            console.warn(`Failed to parse scheduledAt for job from chat ${chatId}:`, error);
            // Continue job creation without scheduledAt - it can be computed later
        }
        // Set bidding deadline (e.g., 7 days from now)
        const biddingDeadline = new Date();
        biddingDeadline.setDate(biddingDeadline.getDate() + 7);
        // Create the job
        const job = new schemas_1.Job({
            ownerId,
            chatId,
            category: chat.category,
            description: chat.description,
            images: chat.images || [],
            price: chat.price,
            date: chat.date,
            hour: chat.hour,
            scheduledAt,
            biddingDeadline,
            status: 'pending'
        });
        yield job.save();
        // Trigger background notification to operators
        const jobData = {
            jobId: job._id.toString(),
            category: job.category,
            description: job.description,
            price: job.price,
            ownerId: job.ownerId
        };
        // Run notification in background (don't await to avoid blocking)
        setImmediate(() => {
            (0, jobNotificationService_1.notifyOperatorsAboutNewJob)(jobData).catch(error => {
                console.error('Background notification failed:', error);
            });
        });
        return {
            success: true,
            job,
            message: 'Job created successfully and operators notified'
        };
    }
    catch (error) {
        console.error('Error creating job from chat:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to create job'
        };
    }
});
exports.createJobFromChat = createJobFromChat;
// Get available jobs for operators
const getAvailableJobs = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { page = 1, limit = 10, category } = req.query;
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        // Check if user is an operator
        const account = yield schemas_1.Account.findById(userId);
        if (!account || !(account.booleans.isOperator || (user === null || user === void 0 ? void 0 : user.role) === 'operator' || (user === null || user === void 0 ? void 0 : user.role) === 'admin')) {
            return res.status(403).json({ success: false, error: 'Only operators can view available jobs' });
        }
        // Get operator's authorized categories
        const { OperatorProfile } = yield Promise.resolve().then(() => tslib_1.__importStar(require('../database/schemas')));
        const operatorProfile = yield OperatorProfile.findOne({ accountId: userId });
        if (!operatorProfile) {
            return res.status(400).json({ success: false, error: 'Operator profile not found' });
        }
        // Build query for available jobs
        let categoryFilter = operatorProfile.authorizedCategories;
        if (category && operatorProfile.authorizedCategories.includes(category)) {
            categoryFilter = [category];
        }
        const query = {
            status: 'pending',
            category: { $in: categoryFilter },
            ownerId: { $ne: userId }, // Exclude operator's own jobs
            biddingDeadline: { $gt: new Date() } // Only jobs with active bidding
        };
        const jobs = yield schemas_1.Job.find(query)
            .sort({ createdAt: -1 })
            .limit(Number(limit))
            .skip((Number(page) - 1) * Number(limit))
            .populate('ownerId', 'user.name user.surname');
        // Add bid information for each job
        const jobsWithBidInfo = yield Promise.all(jobs.map((job) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
            const totalBids = yield schemas_1.Bid.countDocuments({ jobId: job._id });
            const userBid = yield schemas_1.Bid.findOne({ jobId: job._id, operatorId: userId });
            return Object.assign(Object.assign({}, job.toObject()), { totalBids, userBid: userBid || null, hasUserBid: !!userBid });
        })));
        const total = yield schemas_1.Job.countDocuments(query);
        res.json({
            success: true,
            jobs: jobsWithBidInfo,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total,
                pages: Math.ceil(total / Number(limit))
            }
        });
    }
    catch (error) {
        console.error('Error getting available jobs:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.getAvailableJobs = getAvailableJobs;
/**
 * Create a job directly (without chat)
 */
const createJobDirect = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        const { category, description, price, date, hour } = req.body;
        // Validate required fields
        if (!category || !description || !date || !hour) {
            return res.status(400).json({
                success: false,
                error: 'Category, description, date, and hour are required'
            });
        }
        // Validate field lengths
        if (description.length > 2000) {
            return res.status(400).json({
                success: false,
                error: 'Description cannot exceed 2000 characters'
            });
        }
        // Validate category exists and is active
        const isValidCategory = yield jobCategoryService_1.default.isCategoryValid(category);
        if (!isValidCategory) {
            return res.status(400).json({
                success: false,
                error: 'Invalid or inactive category'
            });
        }
        // Validate date and time
        const dateTimeValidation = (0, dateValidation_1.validateDateTime)(date, hour);
        if (!dateTimeValidation.isValid) {
            return res.status(400).json({
                success: false,
                error: `Invalid date/time: ${dateTimeValidation.error}`
            });
        }
        // Validate price if provided
        let jobPrice = 0;
        if (price !== undefined) {
            jobPrice = Number(price);
            if (isNaN(jobPrice) || jobPrice < 0) {
                return res.status(400).json({
                    success: false,
                    error: 'Price must be a valid non-negative number'
                });
            }
        }
        // Get user's timezone for scheduledAt calculation
        const userAccount = yield schemas_1.Account.findById(userId).select('settings.timezone').lean();
        const timezone = ((_a = userAccount === null || userAccount === void 0 ? void 0 : userAccount.settings) === null || _a === void 0 ? void 0 : _a.timezone) || 'Europe/Paris';
        // Calculate scheduledAt from date/hour in user's timezone
        let scheduledAt = null;
        try {
            scheduledAt = (0, parseJobDateTime_1.parseJobDateTime)(date, hour, timezone);
        }
        catch (error) {
            console.warn(`Failed to parse scheduledAt for direct job:`, error);
            // Continue job creation without scheduledAt - it can be computed later
        }
        // Set bidding deadline (7 days from now)
        const biddingDeadline = new Date();
        biddingDeadline.setDate(biddingDeadline.getDate() + 7);
        // Create new job
        const newJob = new schemas_1.Job({
            ownerId: userId.toString(),
            category,
            description,
            images: [], // Will be populated if images are uploaded
            price: jobPrice,
            date,
            hour,
            scheduledAt,
            biddingDeadline,
            status: 'pending',
            createdBy: 'direct'
        });
        yield newJob.save();
        // Trigger background notification to operators
        const jobData = {
            jobId: newJob._id.toString(),
            category: newJob.category,
            description: newJob.description,
            price: newJob.price,
            ownerId: newJob.ownerId
        };
        // Run notification in background (don't await to avoid blocking)
        setImmediate(() => {
            (0, jobNotificationService_1.notifyOperatorsAboutNewJob)(jobData).catch(error => {
                console.error('Background notification failed for direct job:', newJob._id, error);
            });
        });
        res.status(201).json({
            success: true,
            job: newJob,
            message: 'Job created successfully and operators notified'
        });
    }
    catch (error) {
        console.error('Error creating direct job:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.createJobDirect = createJobDirect;
/**
 * Upload images for a job
 */
const uploadJobImages = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { jobId } = req.params;
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'No images uploaded. Please include image files with field name "images"'
            });
        }
        // Validate file types and sizes
        const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
        const maxFileSize = 10 * 1024 * 1024; // 10MB per file
        const maxTotalSize = 50 * 1024 * 1024; // 50MB total upload size
        let totalUploadSize = 0;
        const files = req.files;
        // Validate each file
        for (const file of files) {
            // Check MIME type
            if (!allowedMimeTypes.includes(file.mimetype)) {
                return res.status(400).json({
                    success: false,
                    error: `File "${file.originalname}" has invalid type. Allowed types: ${allowedMimeTypes.join(', ')}`
                });
            }
            // Check individual file size
            if (file.size > maxFileSize) {
                return res.status(400).json({
                    success: false,
                    error: `File "${file.originalname}" exceeds maximum size of ${maxFileSize / (1024 * 1024)}MB`
                });
            }
            totalUploadSize += file.size;
        }
        // Check total upload size
        if (totalUploadSize > maxTotalSize) {
            return res.status(400).json({
                success: false,
                error: `Total upload size (${(totalUploadSize / (1024 * 1024)).toFixed(2)}MB) exceeds maximum of ${maxTotalSize / (1024 * 1024)}MB`
            });
        }
        // Find the job
        const job = yield schemas_1.Job.findById(jobId);
        if (!job) {
            return res.status(404).json({ success: false, error: 'Job not found' });
        }
        // Check if user owns the job using isSameId helper
        if (!isSameId(job.ownerId, userId.toString())) {
            return res.status(403).json({ success: false, error: 'Access denied' });
        }
        // Check if job is still editable
        if (job.status !== 'pending') {
            return res.status(400).json({
                success: false,
                error: 'Cannot upload images for jobs that are not pending'
            });
        }
        // Limit to maximum 5 images total
        const maxImages = 5;
        const currentImageCount = job.images.length;
        const newImageCount = files.length;
        if (currentImageCount + newImageCount > maxImages) {
            return res.status(400).json({
                success: false,
                error: `Maximum ${maxImages} images allowed per job. Current: ${currentImageCount}, trying to add: ${newImageCount}`
            });
        }
        // Upload images to S3 with individual error handling
        const imageUrls = [];
        const failedUploads = [];
        for (const file of files) {
            try {
                // Sanitize the original filename to prevent path traversal or special-char issues
                const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9.\-]/g, '_');
                const fileName = `job-images/${jobId}/${(0, uuid_1.v4)()}-${sanitizedName}`;
                const imageUrl = yield (0, upload_1.uploadFileToS3)(file.buffer, fileName);
                imageUrls.push(imageUrl);
            }
            catch (uploadError) {
                console.error(`Failed to upload file "${file.originalname}":`, uploadError);
                failedUploads.push(file.originalname);
            }
        }
        // Handle partial failures
        if (failedUploads.length > 0) {
            const message = failedUploads.length === files.length
                ? 'All image uploads failed. Please try again.'
                : `${failedUploads.length} of ${files.length} images failed to upload: ${failedUploads.join(', ')}`;
            // If some uploads succeeded, save them
            if (imageUrls.length > 0) {
                // Use an atomic update to avoid race conditions under concurrent requests.
                // If this operation were part of a larger multi-step transaction, pass a session here.
                const updatedJob = yield schemas_1.Job.findByIdAndUpdate(jobId, { $push: { images: { $each: imageUrls } } }, { new: true });
                return res.status(207).json({
                    success: false,
                    message,
                    uploadedImages: imageUrls,
                    failedFiles: failedUploads,
                    totalImages: updatedJob ? updatedJob.images.length : undefined,
                    maxImages
                });
            }
            else {
                return res.status(500).json({
                    success: false,
                    error: message
                });
            }
        }
        // All uploads succeeded - save to job
        try {
            // Atomic update to append images and retrieve updated doc to avoid race conditions
            const updatedJob = yield schemas_1.Job.findByIdAndUpdate(jobId, { $push: { images: { $each: imageUrls } } }, { new: true });
            if (!updatedJob) {
                throw new Error('Job not found when saving images');
            }
            // replace job variable's images for the response
            job.images = updatedJob.images;
        }
        catch (saveError) {
            console.error('Failed to save job with new images:', saveError);
            return res.status(500).json({
                success: false,
                error: 'Images uploaded but failed to save to job. Please try again.'
            });
        }
        res.json({
            success: true,
            message: `${imageUrls.length} images uploaded successfully`,
            images: imageUrls,
            totalImages: job.images.length,
            maxImages
        });
    }
    catch (error) {
        console.error('Error uploading job images:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.uploadJobImages = uploadJobImages;
/**
 * Upload completion photos (operator evidence)
 * Expects multipart form with field name "photos"
 */
const uploadCompletionPhotos = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { jobId } = req.params;
        if (!userId)
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        const job = yield schemas_1.Job.findById(jobId);
        if (!job)
            return res.status(404).json({ success: false, error: 'Job not found' });
        const isAssignedOperator = job.assignedOperatorId && isSameId(job.assignedOperatorId.toString(), userId.toString());
        if (!isAssignedOperator) {
            return res.status(403).json({ success: false, error: 'Only assigned operator can upload completion photos' });
        }
        if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
            return res.status(400).json({ success: false, error: 'No photos uploaded. Please include files with field name "photos"' });
        }
        const files = req.files;
        const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        const uploaded = [];
        for (const file of files) {
            if (!allowedMimeTypes.includes(file.mimetype)) {
                return res.status(400).json({ success: false, error: `Invalid file type for ${file.originalname}` });
            }
            const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9.\-]/g, '_');
            const key = `job-completion-photos/${jobId}/${(0, uuid_1.v4)()}-${sanitizedName}`;
            const url = yield (0, upload_1.uploadFileToS3)(file.buffer, key);
            uploaded.push(url);
        }
        yield schemas_1.Job.findByIdAndUpdate(jobId, { $push: { completionPhotos: { $each: uploaded } } });
        const updated = yield schemas_1.Job.findById(jobId).lean();
        res.json({ success: true, message: `${uploaded.length} photos uploaded`, photos: uploaded, total: ((_a = updated === null || updated === void 0 ? void 0 : updated.completionPhotos) === null || _a === void 0 ? void 0 : _a.length) || 0, required: (updated === null || updated === void 0 ? void 0 : updated.minCompletionPhotos) || 3 });
    }
    catch (error) {
        console.error('Error uploading completion photos:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.uploadCompletionPhotos = uploadCompletionPhotos;
/**
 * Operator rates client for a job
 */
const rateClientForJob = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { jobId } = req.params;
        const { stars, adjectives } = req.body;
        if (!userId)
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        const job = yield schemas_1.Job.findById(jobId);
        if (!job)
            return res.status(404).json({ success: false, error: 'Job not found' });
        const isAssignedOperator = job.assignedOperatorId && isSameId(job.assignedOperatorId.toString(), userId.toString());
        if (!isAssignedOperator)
            return res.status(403).json({ success: false, error: 'Only assigned operator can rate client' });
        if (!stars || stars < 1 || stars > 5) {
            return res.status(400).json({ success: false, error: 'stars must be between 1 and 5' });
        }
        const payload = {
            clientId: String(job.ownerId),
            operatorId: String(userId),
            jobId: String(job._id),
            stars,
            adjectives: {
                positive: (adjectives === null || adjectives === void 0 ? void 0 : adjectives.positive) || [],
                negative: (adjectives === null || adjectives === void 0 ? void 0 : adjectives.negative) || [],
            },
        };
        // Upsert by jobId to ensure one rating per job
        const ratingDoc = yield schemas_1.ClientRating.findOneAndUpdate({ jobId: String(job._id) }, { $set: payload }, { upsert: true, new: true });
        res.json({ success: true, rating: ratingDoc });
    }
    catch (error) {
        console.error('Error rating client:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.rateClientForJob = rateClientForJob;
/**
 * Operator requests materials quote for a job
 */
const requestMaterialsQuote = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { jobId } = req.params;
        const { items, deliveryLocation } = req.body;
        if (!userId)
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        const job = yield schemas_1.Job.findById(jobId);
        if (!job)
            return res.status(404).json({ success: false, error: 'Job not found' });
        const isAssignedOperator = job.assignedOperatorId && isSameId(job.assignedOperatorId.toString(), userId.toString());
        if (!isAssignedOperator)
            return res.status(403).json({ success: false, error: 'Only assigned operator can request materials' });
        if (!Array.isArray(items) || items.length === 0) {
            return res.status(400).json({ success: false, error: 'items required' });
        }
        let subtotal = 0;
        for (const it of items) {
            // Validate item shape and numeric fields: quantity and unitPrice must be finite numbers >= 0
            if (!it ||
                typeof it.quantity !== 'number' || !Number.isFinite(it.quantity) || it.quantity < 0 ||
                typeof it.unitPrice !== 'number' || !Number.isFinite(it.unitPrice) || it.unitPrice < 0 ||
                !it.name || !it.sku) {
                return res.status(400).json({
                    success: false,
                    error: 'Invalid item format: quantity and unitPrice must be finite numbers >= 0; name and sku are required'
                });
            }
            // Safe to compute subtotal after validation
            subtotal += it.quantity * it.unitPrice;
        }
        const deliveryFee = 0; // placeholder, could be dynamic
        const total = subtotal + deliveryFee;
        const order = yield schemas_1.MaterialOrder.create({
            jobId: String(job._id),
            ownerId: String(job.ownerId),
            operatorId: String(userId),
            items,
            subtotal,
            deliveryFee,
            total,
            currency: 'DOP',
            status: 'pending',
            deliveryLocation: deliveryLocation || null,
        });
        // TODO: emit invoice via WebSocket to client (owner)
        res.status(201).json({ success: true, order });
    }
    catch (error) {
        console.error('Error requesting materials quote:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.requestMaterialsQuote = requestMaterialsQuote;
const approveMaterialOrder = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { orderId } = req.params;
        if (!userId)
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        const order = yield schemas_1.MaterialOrder.findById(orderId);
        if (!order)
            return res.status(404).json({ success: false, error: 'Order not found' });
        if (!isSameId(order.ownerId, String(userId)))
            return res.status(403).json({ success: false, error: 'Only client can approve' });
        if (order.status !== 'pending')
            return res.status(400).json({ success: false, error: `Order is ${order.status}` });
        order.status = 'approved';
        yield order.save();
        // TODO: trigger delivery and notify operator via WebSocket
        res.json({ success: true, order });
    }
    catch (error) {
        console.error('Error approving material order:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.approveMaterialOrder = approveMaterialOrder;
const rejectMaterialOrder = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { orderId } = req.params;
        if (!userId)
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        const order = yield schemas_1.MaterialOrder.findById(orderId);
        if (!order)
            return res.status(404).json({ success: false, error: 'Order not found' });
        if (!isSameId(order.ownerId, String(userId)))
            return res.status(403).json({ success: false, error: 'Only client can reject' });
        if (order.status !== 'pending')
            return res.status(400).json({ success: false, error: `Order is ${order.status}` });
        order.status = 'rejected';
        yield order.save();
        // TODO: notify operator via WebSocket
        res.json({ success: true, order });
    }
    catch (error) {
        console.error('Error rejecting material order:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.rejectMaterialOrder = rejectMaterialOrder;
