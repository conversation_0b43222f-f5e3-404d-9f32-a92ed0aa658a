import React, { useContext, useEffect, useState } from 'react'
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { signInWithApple } from '@/utils/AppleAuth'
import { GoogleSignin, isSuccessResponse } from '@react-native-google-signin/google-signin'

import Page from '@/components/templates/Page'
import ButtonIcon from '@/components/atoms/buttons/ButtonIcon'
import AppleIcon from '@/components/atoms/icons/Apple'
import GoogleIcon from '@/components/atoms/icons/Google'
import Toast from 'react-native-toast-message'

import { router } from 'expo-router'
import { AuthContext } from '@/context/AuthContext'
import { appleLogin, googleLogin, setToken } from '@/services/api'
import ScreenSpinnerLoader from '@/components/atoms/loaders/ScreenSpinner'
import { Ionicons } from '@expo/vector-icons'
import { LinearGradient } from 'expo-linear-gradient'
import Input from '@/components/atoms/inputs/Input'
import Header from '@/components/templates/Header'


const Login = () => {
  const { sessionAuthentication } = useContext(AuthContext)




  return (
    <Page noPaddingTop alignItems='center' justifyContent='space-between' >

      <Header buttonBack/>
      <View style={{
        flex:1,
        height: '100%',
        width: '100%',
        flexDirection: 'column',
        justifyContent: 'space-between'
      }}>
        {/* Top Part */}      
        <View style={{ width: '100%', height: '60%', position: 'relative' }}>

          <Image
            source={require('@/assets/pictures/landing.jpg')}
            style={styles.landingImage}
          />
          <LinearGradient
            colors={["#ffffff", "#ffffff00"]}
            start={{ x: 0, y: 1 }}
            end={{ x: 0, y: 0 }}
            style={{ width: '100%', height: 300, position: 'absolute', bottom: 0, left: 0 }}
          />

          <View style={{ alignItems: 'center', justifyContent: 'center', flexDirection: 'column', width: '100%', position: 'absolute', left: 0, bottom: -40 }}>

            <Image
              source={require('@/assets/pictures/manito.png')}
              style={{ width: 100, height: 100, resizeMode: 'contain' }}
            />

            <Text style={{ fontSize: 50, color: '#337836', fontWeight: 600, transform: [{translateY: -15}], fontFamily: 'Montserrat' }}>Manito</Text>

          </View>
        </View>
        
        {/* Bottom Part */}
        <View style={{ width: '100%',  justifyContent: 'space-between', flexDirection: 'column',  paddingBottom: 30, height: '40%' }}>

    
          <View style={{width: '100%', alignItems: 'center', paddingTop: 50, gap: 10}}>

            <Input
              value=''
              onChangeText={() => {
                
              }}
              placeholder='Correo Electronico'
              style={{ width: '90%',  backgroundColor: 'white' }}
            />
            <Input
              value=''
              onChangeText={() => {
                
              }}
              placeholder='Constraseña'
              style={{ width: '90%',  backgroundColor: 'white' }}
            />
            
            <ButtonIcon
              
              text='Acceder'
              onPress={() => { }}
              style={{ width: '90%', backgroundColor: 'white' }}
              styleText={{ fontSize: 15, color: 'black', fontWeight: '600' }}
            />

            <TouchableOpacity 
              style={{marginTop: 10,width: '90%', alignItems: 'center'}}
              onPress={()=>{
                router.push('/auth/register')
              }}
            >
              <Text style={{fontFamily: 'Montserrat'}}>Crea una cuenta</Text>
            </TouchableOpacity>
          </View>


          <View style={{ flexDirection: 'row', justifyContent: 'center', paddingHorizontal: 30, gap: 30 }}>
            <Text
              style={{ color: '#000000a9', fontFamily: 'Montserrat' }}
              onPress={() => router.push('/settings/terms')}
            >
              Terms of Service
            </Text>

            <Text
              style={{ color: '#000000a9', fontFamily: 'Montserrat' }}
              onPress={() => router.push('/settings/policy')}
            >
              Privacy Policy
            </Text>
          </View>

        </View>
      </View>


      

    </Page>
  )
}

export default Login

const styles = StyleSheet.create({
  landingImage: {

    height: '100%',
    width: '100%',
    objectFit: 'cover'
  }
})