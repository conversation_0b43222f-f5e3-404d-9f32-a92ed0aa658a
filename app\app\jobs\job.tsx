import React, { useRef, useEffect, useState } from 'react';
import { Image, ScrollView, StyleSheet, Text, View } from 'react-native';

import Page from '@/components/templates/Page';
import Header from '@/components/templates/Header';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { viewJob } from '@/services/api';
import Toast from 'react-native-toast-message';
import { InterfaceJob } from '@/types/jobcard';
import { Ionicons } from '@expo/vector-icons';
import Map from '@/components/templates/Map';

const Index = () => {
  const router = useRouter()
  const { jobId } = useLocalSearchParams()

  const [data, setData] = useState<InterfaceJob | null>(null)

  const handleFetchJob = async () => {
    const call = await viewJob(jobId.toString())
    if (!call.success) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Error cargando el trabajo'
      })

      router.back()
      return
    }

    setData(call.job)
  }



  const formatTime = (date: any) => {
    return date.toLocaleTimeString('it', { hour: '2-digit', minute: '2-digit' });
  };
  const formatDate = (date: any) => {
    return date.toLocaleDateString('it', { month: 'short', day: 'numeric' });
  };
  const timeFrom = (date: any) => {
    const now = new Date();
    const diffMinutes = Math.round((date.getTime() - now.getTime()) / (1000 * 60));

    if (diffMinutes < 60) {
      return `${diffMinutes} min`;
    } else {
      const hours = Math.floor(diffMinutes / 60);
      const mins = diffMinutes % 60;
      return `${hours}h ${mins}m`;
    }
  };
  const getTimeColor = (date: any) => {
    const now = new Date();
    const diffMinutes = Math.round((date.getTime() - now.getTime()) / (1000 * 60));

    if (diffMinutes < 15) {
      return '#ef4444'; // Urgent (red)
    } else if (diffMinutes < 30) {
      return '#f97316'; // Soon (orange)
    } else {
      return '#10b981'; // Plenty of time (green)
    }
  };
  const renderStars = (rating: any) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Ionicons
        key={index}
        name={index < Math.floor(rating) ? "star" : (index < rating ? "star-half" : "star-outline")}
        size={16}
        color="#fbbf24"
      />
    ));
  };


  useEffect(() => {
    handleFetchJob()
  }, [])

  return (

    <Page noPaddingTop noBottomBar alignItems="center" justifyContent="space-between" page="home">
      {
        data &&
        <Header buttonBack text={`${data.category.charAt(0).toUpperCase()}${data.category.slice(1)}`} />
      }

    
      {
        data &&
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Map Section */}
          <View style={styles.mapContainer}>
            <Map />
          </View>

          {/* Ride Info Card */}
          <View style={styles.rideInfoCard}>

            <View>
              {data.images?.length > 0 && (
                <>
                  <ScrollView horizontal style={styles.imageScroll}>
                    {data.images.map((img, idx) => (
                      <Image key={idx} source={{ uri: img }} style={styles.image} />
                    ))}
                  </ScrollView>
                </>
              )}


            </View>

            {/* Pickup Time */}
            <View style={styles.timeSection}>
              <View style={styles.timeHeader}>
                <Ionicons name="time-outline" size={20} color="#3b82f6" />
                <Text style={styles.timeHeaderText}>Data & Orario</Text>
              </View>
              <View style={styles.timeDetails}>
                <Text style={styles.timeValue}>10</Text>
                <Text style={styles.dateValue}>11</Text>
              </View>
              <View style={styles.timeCountdown}>
                <Ionicons name="alarm-outline" size={16} />
                <Text style={[styles.countdownText]}>
                  {/* {timeFrom(rideRequest.scheduledTime)} */} minuti da adesso
                </Text>
              </View>
            </View>

            {/* Route Information */}
            <View style={styles.routeSection}>
              <View style={styles.sectionHeader}>
                <Ionicons name="map-outline" size={20} color="#3b82f6" />
                <Text style={styles.sectionHeaderText}>Percorso</Text>
              </View>

              <View style={styles.routeContainer}>
                <View style={styles.routeIconColumn}>
                  <View style={styles.pickupDot} />
                  <View style={styles.routeLine} />
                  <View style={styles.dropoffDot} />
                </View>

                <View style={styles.routeDetails}>
                  <View style={styles.routePoint}>
                    <Text style={styles.routeLocationName}>12312</Text>
                    <Text style={styles.routeAddress}>123123</Text>
                  </View>


                  <View style={styles.routePoint}>
                    <Text style={styles.routeLocationName}>4444</Text>
                    <Text style={styles.routeAddress}>5345563453</Text>
                  </View>
                </View>
              </View>

              <View style={styles.tripMetrics}>
                <View style={styles.metricItem}>
                  <Ionicons name="speedometer-outline" size={16} color="#64748b" />
                  <Text style={styles.metricValue}>11 km</Text>
                  <Text style={styles.metricLabel}>Distanza</Text>
                </View>
                <View style={styles.metricDivider} />
                <View style={styles.metricItem}>
                  <Ionicons name="time-outline" size={16} color="#64748b" />
                  <Text style={styles.metricValue}>1 min</Text>
                  <Text style={styles.metricLabel}>Duratata</Text>
                </View>
                <View style={styles.metricDivider} />
                <View style={styles.metricItem}>
                  <Ionicons name="cash-outline" size={16} color="#64748b" />
                  <Text style={styles.metricValue}>
                    &euro;

                  </Text>
                  <Text style={styles.metricLabel}>Guadagno</Text>
                </View>
              </View>
            </View>

            {/* Passenger Information */}
            <View style={styles.passengerSection}>
              <View style={styles.sectionHeader}>
                <Ionicons name="person-outline" size={20} color="#3b82f6" />
                <Text style={styles.sectionHeaderText}>Passeggero</Text>
              </View>

              <View style={styles.passengerInfo}>
                {/* <Image
                  source={{ uri: rideRequest.passengerPhoto }}
                  style={styles.passengerPhoto}
                /> */}
                <View style={styles.passengerDetails}>
                  <Text style={styles.passengerName}>federico</Text>
                  <View style={styles.passengerRating}>
                    <View style={styles.starsContainer}>
                      {renderStars(2)}
                    </View>
                    {/* <Text style={styles.ratingText}>{rideRequest.passengerRating}</Text>
                        <Text style={styles.tripsText}>• {rideRequest.passengerTrips} viaggi</Text> */}
                  </View>
                </View>
              </View>
            </View>

            {/* Ride Details */}
            <View style={styles.detailsSection}>
              <View style={styles.sectionHeader}>
                <Ionicons name="information-circle-outline" size={20} color="#3b82f6" />
                <Text style={styles.sectionHeaderText}>Dettagli Corsa</Text>
              </View>

              <View style={styles.detailsGrid}>
                <View style={styles.detailItem}>
                  <Text style={styles.detailLabel}>Tipo Corsa</Text>
                  <View style={styles.detailValue}>
                    <Ionicons
                      name={'car'}
                      size={14}
                      color={'#3b82f6'}
                    />
                    <Text style={[
                      styles.detailValueText,
                      { color: '#3b82f6' }
                    ]}>
                      Standard
                    </Text>
                  </View>
                </View>

                <View style={styles.detailItem}>
                  <Text style={styles.detailLabel}>Passeggero</Text>
                  <View style={styles.detailValue}>
                    <Ionicons name="people-outline" size={14} color="#64748b" />
                    <Text style={styles.detailValueText}>312312</Text>
                  </View>
                </View>

                <View style={styles.detailItem}>
                  <Text style={styles.detailLabel}>Valigie</Text>
                  <View style={styles.detailValue}>
                    <Ionicons name="briefcase-outline" size={14} color="#64748b" />
                    <Text style={styles.detailValueText}>1</Text>
                  </View>
                </View>

                <View style={styles.detailItem}>
                  <Text style={styles.detailLabel}>Pagamento</Text>
                  <View style={styles.detailValue}>
                    <Ionicons name="card-outline" size={14} color="#64748b" />
                    <Text style={styles.detailValueText}>carta</Text>
                  </View>
                </View>
              </View>



            </View>
          </View>

          {/* Extra space at bottom */}
          <View style={{ height: 100 }} />
        </ScrollView>
      }

    </Page>

  );
};

export default Index;

const styles = StyleSheet.create({
  paddingH: {
    paddingHorizontal: 15
  },
  imageScroll: {
    marginTop: 10,
    flexDirection: 'row',
  },
  image: {
    width: 300,
    height: 280,
    marginRight: 10,
    borderRadius: 8,
    resizeMode: 'cover',
    marginLeft: 15
  },
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    textAlign: 'center',
  },
  rideId: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
  },
  moreButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  mapContainer: {
    height: 400,
    width: '100%',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  pickupMarker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#10b981',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  stopMarker: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#f97316',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  dropoffMarker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#ef4444',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  rideInfoCard: {
    backgroundColor: 'white',
    borderRadius: 24,
    marginTop: -24,
    paddingTop: 24,
    paddingBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 8,
  },
  timeSection: {
    marginBottom: 24,
  },
  timeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  timeHeaderText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
  },
  timeDetails: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 8,
  },
  timeValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1e293b',
  },
  dateValue: {
    fontSize: 16,
    color: '#64748b',
  },
  timeCountdown: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 6,
    backgroundColor: '#f8fafc',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  countdownText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#f97316',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionHeaderText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
  },
  routeSection: {
    marginBottom: 24,
  },
  routeContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  routeIconColumn: {
    width: 20,
    alignItems: 'center',
    marginRight: 12,
  },
  pickupDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#10b981',
  },
  routeLine: {
    width: 2,
    height: 40,
    backgroundColor: '#e2e8f0',
    marginVertical: 4,
  },
  stopDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#f97316',
  },
  dropoffDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#ef4444',
  },
  routeDetails: {
    flex: 1,
    justifyContent: 'space-between',
  },
  routePoint: {
    marginBottom: 16,
  },
  routeLocationName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1e293b',
    marginBottom: 2,
  },
  routeAddress: {
    fontSize: 14,
    color: '#64748b',
  },
  tripMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    padding: 16,
  },
  metricItem: {
    alignItems: 'center',
    flex: 1,
  },
  metricValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    marginTop: 4,
    marginBottom: 2,
  },
  metricLabel: {
    fontSize: 12,
    color: '#64748b',
  },
  metricDivider: {
    width: 1,
    height: '80%',
    backgroundColor: '#e2e8f0',
    alignSelf: 'center',
  },
  passengerSection: {
    marginBottom: 24,
  },
  passengerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  passengerPhoto: {
    width: 56,
    height: 56,
    borderRadius: 28,
    marginRight: 16,
    backgroundColor: "#00000010"
  },
  passengerDetails: {
    flex: 1,
  },
  passengerName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 4,
  },
  passengerRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  starsContainer: {
    flexDirection: 'row',
    marginRight: 6,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748b',
  },
  tripsText: {
    fontSize: 14,
    color: '#64748b',
    marginLeft: 6,
  },
  detailsSection: {
    marginBottom: 16,
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  detailItem: {
    width: '50%',
    marginBottom: 16,
  },
  detailLabel: {
    fontSize: 12,
    color: '#64748b',
    marginBottom: 4,
  },
  detailValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  detailValueText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1e293b',
  },
  extraServices: {
    marginBottom: 16,
  },
  extraServicesLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1e293b',
    marginBottom: 8,
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
    gap: 8,
  },
  serviceText: {
    fontSize: 14,
    color: '#64748b',
  },
  specialRequests: {
    marginBottom: 16,
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    padding: 12,
  },
  specialRequestsLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1e293b',
    marginBottom: 6,
  },
  specialRequestsText: {
    fontSize: 14,
    color: '#64748b',
    fontStyle: 'italic',
  },
  notes: {
    backgroundColor: '#f0fdf4',
    borderRadius: 12,
    padding: 12,
  },
  notesLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1e293b',
    marginBottom: 6,
  },
  notesText: {
    fontSize: 14,
    color: '#64748b',
  },
  takeRideContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#f1f5f9',
  },
  fareContainer: {
    flex: 1,
  },
  fareLabel: {
    fontSize: 14,
    color: '#64748b',
  },
  fareAmount: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
  },
  takeRideButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3b82f6',
    paddingHorizontal: 24,
    paddingVertical: 14,
    borderRadius: 12,
    gap: 8,
  },
  takeRideText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1e293b',
    marginTop: 8,
  },
  modalMessage: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f1f5f9',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#64748b',
  },
  confirmButton: {
    flex: 1,
    flexDirection: 'row',
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3b82f6',
    gap: 8,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
});