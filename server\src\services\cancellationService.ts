import mongoose from 'mongoose';
import { DateTime } from 'luxon';
import { 
  Job, 
  Bid, 
  Account, 
  PaymentTransaction, 
  OperatorProfile,
  CancellationRecord, 
  OperatorPenalty,
  CreditBalance 
} from '../database/schemas';
import { CreditBalanceService } from './creditBalanceService';
import { PaymentProcessorService } from './paymentProcessorService';
import { parseJobDateTime } from '../utils/parseJobDateTime';
import { logger } from '../utils/logger';
import {
  CancellationError,
  InvalidCancellationError,
  CancellationNotAllowedError,
  JobNotCancellableError,
  UnauthorizedCancellationError
} from '../errors/cancellationErrors';

export interface CancellationRequest {
  jobId: string;
  initiatedBy: string;
  reason: string;
  explanation?: string;
  evidenceUrls?: string[];
}

export interface CancellationResult {
  cancellationId: string;
  refundAmount: number;
  penaltyAmount: number;
  creditAmount: number;
  status: string;
  message: string;
}

export interface PenaltyConfiguration {
  // Client cancellation penalties (less than 24h)
  clientCancellationWorkerCompensation: number; // Percentage (30-50%)
  clientCancellationPlatformFee: number; // Percentage (10-15%)
  
  // Operator penalties
  operatorCancellationFinancialPenalty: number; // Fixed amount or percentage
  operatorCancellationRatingPenalty: number; // Rating points (0.1-1.0)
  operatorAfterArrivalFinancialPenalty: number; // Higher penalty for after arrival
  operatorAfterArrivalRatingPenalty: number; // Higher rating penalty
  
  // Configuration flags
  ratingPenaltyConfigurable: boolean; // Whether admin can configure rating impact
  enableAutomaticPenalties: boolean; // Whether to apply penalties automatically
}

export class CancellationService {
  private static instance: CancellationService;
  private creditBalanceService: CreditBalanceService;
  private paymentProcessor: PaymentProcessorService;
  
  // Default penalty configuration
  private penaltyConfig: PenaltyConfiguration = {
    clientCancellationWorkerCompensation: 40, // 40%
    clientCancellationPlatformFee: 12, // 12%
    operatorCancellationFinancialPenalty: 500, // 500 DOP fixed
    operatorCancellationRatingPenalty: 0.2, // 0.2 points
    operatorAfterArrivalFinancialPenalty: 1000, // 1000 DOP fixed
    operatorAfterArrivalRatingPenalty: 0.5, // 0.5 points
    ratingPenaltyConfigurable: true,
    enableAutomaticPenalties: true
  };

  private constructor() {
    this.creditBalanceService = CreditBalanceService.getInstance();
    this.paymentProcessor = PaymentProcessorService.getInstance();
  }

  public static getInstance(): CancellationService {
    if (!CancellationService.instance) {
      CancellationService.instance = new CancellationService();
    }
    return CancellationService.instance;
  }

  /**
   * Process operator cancellation before job start
   */
  async processOperatorCancellationBeforeStart(request: CancellationRequest): Promise<CancellationResult> {
    const session = await mongoose.startSession();
    
    try {
      return await session.withTransaction(async () => {
        // Validate request and get job details
        const { job, bid, escrowTx } = await this.validateCancellationRequest(request, 'operator');
        
        // Check timing - must be before job start
        const scheduledDateTime = parseJobDateTime(job.date, job.hour, 'America/Santo_Domingo');
        const now = new Date();
        
        if (now >= scheduledDateTime) {
          throw new CancellationNotAllowedError('Cannot cancel after job start time');
        }

        // Calculate penalty amounts
        const penaltyAmount = this.penaltyConfig.operatorCancellationFinancialPenalty;
        const refundAmount = escrowTx.amount; // Full refund to client
        
        // Create cancellation record
        const cancellationRecord = await CancellationRecord.create([{
          jobId: request.jobId,
          bidId: bid ? String(bid._id) : undefined,
          initiatedBy: request.initiatedBy,
          initiatorRole: 'operator',
          type: 'operator_before_start',
          status: 'processing',
          scheduledStartTime: scheduledDateTime,
          cancellationTime: now,
          hoursBeforeStart: (scheduledDateTime.getTime() - now.getTime()) / (1000 * 60 * 60),
          originalAmount: escrowTx.amount,
          penaltyAmount,
          refundAmount,
          adminShare: 0,
          operatorShare: 0,
          currency: escrowTx.currency,
          reason: request.reason,
          explanation: request.explanation,
          evidenceUrls: request.evidenceUrls || [],
          paymentTransactionIds: [String(escrowTx._id)],
          creditTransactionIds: [],
          metadata: {
            clientNotified: false,
            operatorNotified: false,
            ratingImpactApplied: false,
            jobReposted: false,
            rescheduleOffered: true
          }
        }], { session });

        // Process financial penalty for operator
        if (penaltyAmount > 0) {
          const operatorPenalty = await this.createOperatorPenalty({
            operatorId: request.initiatedBy,
            jobId: request.jobId,
            bidId: bid ? String(bid._id) : undefined,
            cancellationId: String(cancellationRecord[0]._id),
            type: 'cancellation_before_start',
            severity: 'moderate',
            financialPenalty: penaltyAmount,
            ratingPenalty: this.penaltyConfig.operatorCancellationRatingPenalty,
            reason: request.reason,
            description: `Operator cancelled job before start time. Penalty: ${penaltyAmount} ${escrowTx.currency}`,
            session
          });

          await CancellationRecord.findByIdAndUpdate(
            cancellationRecord[0]._id,
            { operatorPenaltyId: String(operatorPenalty._id) },
            { session }
          );
        }

        // Process refund to client's credit balance
        const creditTransaction = await this.creditBalanceService.addCredit({
          userId: String(job.ownerId),
          type: 'refund',
          amount: refundAmount,
          currency: escrowTx.currency,
          description: `Refund for job cancellation by operator`,
          metadata: {
            jobId: request.jobId,
            bidId: bid ? String(bid._id) : undefined,
            cancellationId: String(cancellationRecord[0]._id),
            paymentTransactionId: String(escrowTx._id)
          }
        });

        // Update job status
        await Job.findByIdAndUpdate(
          request.jobId,
          {
            status: 'cancelled',
            cancelledAt: now,
            cancelReason: request.reason
          },
          { session }
        );

        // Update cancellation record
        await CancellationRecord.findByIdAndUpdate(
          cancellationRecord[0]._id,
          {
            status: 'completed',
            processedAt: now,
            creditTransactionIds: [String(creditTransaction._id)],
            'metadata.clientNotified': true,
            'metadata.operatorNotified': true,
            'metadata.rescheduleOffered': true
          },
          { session }
        );

        logger.info(`Processed operator cancellation before start for job ${request.jobId}`);

        return {
          cancellationId: String(cancellationRecord[0]._id),
          refundAmount,
          penaltyAmount,
          creditAmount: refundAmount,
          status: 'completed',
          message: 'Cancellation processed successfully. Client has been refunded and can repost or reschedule the job.'
        };
      });
    } finally {
      await session.endSession();
    }
  }

  /**
   * Process client cancellation less than 24 hours before start
   */
  async processClientCancellationLessThan24h(request: CancellationRequest): Promise<CancellationResult> {
    const session = await mongoose.startSession();
    
    try {
      return await session.withTransaction(async () => {
        // Validate request and get job details
        const { job, bid, escrowTx } = await this.validateCancellationRequest(request, 'client');
        
        // Check timing - must be less than 24h before start
        const scheduledDateTime = parseJobDateTime(job.date, job.hour, 'America/Santo_Domingo');
        const now = new Date();
        const hoursBeforeStart = (scheduledDateTime.getTime() - now.getTime()) / (1000 * 60 * 60);
        
        if (hoursBeforeStart >= 24) {
          throw new CancellationNotAllowedError('Use regular cancellation for cancellations more than 24 hours before start');
        }

        if (now >= scheduledDateTime) {
          throw new CancellationNotAllowedError('Job has already started. Use after-start cancellation.');
        }

        // Calculate penalty fees
        const workerCompensation = Math.floor((escrowTx.amount * this.penaltyConfig.clientCancellationWorkerCompensation) / 100);
        const platformFee = Math.floor((escrowTx.amount * this.penaltyConfig.clientCancellationPlatformFee) / 100);
        const totalPenalty = workerCompensation + platformFee;
        const refundAmount = Math.max(0, escrowTx.amount - totalPenalty);

        // Create cancellation record
        const cancellationRecord = await CancellationRecord.create([{
          jobId: request.jobId,
          bidId: bid ? String(bid._id) : undefined,
          initiatedBy: request.initiatedBy,
          initiatorRole: 'client',
          type: 'client_less_than_24h',
          status: 'processing',
          scheduledStartTime: scheduledDateTime,
          cancellationTime: now,
          hoursBeforeStart,
          originalAmount: escrowTx.amount,
          penaltyAmount: totalPenalty,
          refundAmount,
          adminShare: platformFee,
          operatorShare: workerCompensation,
          currency: escrowTx.currency,
          reason: request.reason,
          explanation: request.explanation,
          evidenceUrls: request.evidenceUrls || [],
          paymentTransactionIds: [String(escrowTx._id)],
          creditTransactionIds: [],
          metadata: {
            clientNotified: false,
            operatorNotified: false,
            ratingImpactApplied: false,
            jobReposted: false,
            rescheduleOffered: false
          }
        }], { session });

        // Process refund to client's credit balance
        let creditTransactionId = null;
        if (refundAmount > 0) {
          const creditTransaction = await this.creditBalanceService.addCredit({
            userId: String(job.ownerId),
            type: 'refund',
            amount: refundAmount,
            currency: escrowTx.currency,
            description: `Partial refund for late cancellation (${hoursBeforeStart.toFixed(1)}h before start)`,
            metadata: {
              jobId: request.jobId,
              bidId: bid ? String(bid._id) : undefined,
              cancellationId: String(cancellationRecord[0]._id),
              paymentTransactionId: String(escrowTx._id)
            }
          });
          creditTransactionId = String(creditTransaction._id);
        }

        // Compensate operator if job was assigned
        if (job.assignedOperatorId && workerCompensation > 0) {
          const operatorCreditTransaction = await this.creditBalanceService.addCredit({
            userId: String(job.assignedOperatorId),
            type: 'penalty_compensation',
            amount: workerCompensation,
            currency: escrowTx.currency,
            description: `Compensation for client late cancellation`,
            metadata: {
              jobId: request.jobId,
              bidId: bid ? String(bid._id) : undefined,
              cancellationId: String(cancellationRecord[0]._id),
              paymentTransactionId: String(escrowTx._id)
            }
          });
        }

        // Update job status
        await Job.findByIdAndUpdate(
          request.jobId,
          {
            status: 'cancelled',
            cancelledAt: now,
            cancelReason: request.reason
          },
          { session }
        );

        // Update cancellation record
        const updateData: any = {
          status: 'completed',
          processedAt: now,
          'metadata.clientNotified': true,
          'metadata.operatorNotified': true
        };

        if (creditTransactionId) {
          updateData.creditTransactionIds = [creditTransactionId];
        }

        await CancellationRecord.findByIdAndUpdate(
          cancellationRecord[0]._id,
          updateData,
          { session }
        );

        logger.info(`Processed client late cancellation for job ${request.jobId}, penalty: ${totalPenalty}`);

        return {
          cancellationId: String(cancellationRecord[0]._id),
          refundAmount,
          penaltyAmount: totalPenalty,
          creditAmount: refundAmount,
          status: 'completed',
          message: `Cancellation processed. Penalty applied: ${totalPenalty} ${escrowTx.currency}. Remaining amount credited to your balance.`
        };
      });
    } finally {
      await session.endSession();
    }
  }

  /**
   * Validate cancellation request and return job details
   */
  private async validateCancellationRequest(request: CancellationRequest, expectedRole: 'client' | 'operator') {
    // Get job
    const job = await Job.findById(request.jobId);
    if (!job) {
      throw new InvalidCancellationError('Job not found');
    }

    // Check if job can be cancelled
    if (job.status === 'cancelled') {
      throw new JobNotCancellableError('Job is already cancelled');
    }

    if (job.status === 'completed') {
      throw new JobNotCancellableError('Cannot cancel completed job');
    }

    // Check authorization
    if (expectedRole === 'client' && job.ownerId !== request.initiatedBy) {
      throw new UnauthorizedCancellationError('Only job owner can cancel');
    }

    if (expectedRole === 'operator' && job.assignedOperatorId !== request.initiatedBy) {
      throw new UnauthorizedCancellationError('Only assigned operator can cancel');
    }

    // Get bid if job is accepted
    let bid = null;
    if (job.acceptedBidId) {
      bid = await Bid.findById(job.acceptedBidId);
    }

    // Get escrow transaction
    const escrowTx = await PaymentTransaction.findOne({ 
      jobId: request.jobId, 
      type: 'Hold',
      status: { $in: ['escrow', 'successful'] }
    });

    if (!escrowTx) {
      throw new InvalidCancellationError('No escrow transaction found for this job');
    }

    return { job, bid, escrowTx };
  }

  /**
   * Create operator penalty record
   */
  private async createOperatorPenalty(options: {
    operatorId: string;
    jobId: string;
    bidId?: string;
    cancellationId: string;
    type: string;
    severity: string;
    financialPenalty: number;
    ratingPenalty: number;
    reason: string;
    description: string;
    session: any;
  }) {
    // Check for previous penalties to determine recurrence
    const previousPenalties = await OperatorPenalty.find({
      operatorId: options.operatorId,
      type: options.type,
      status: { $in: ['active', 'served'] }
    }).session(options.session);

    const isRecurring = previousPenalties.length > 0;
    const recurrenceMultiplier = Math.min(1.0 + (previousPenalties.length * 0.5), 3.0);

    // Create penalty record
    const penalty = await OperatorPenalty.create([{
      operatorId: options.operatorId,
      jobId: options.jobId,
      bidId: options.bidId,
      cancellationId: options.cancellationId,
      type: options.type,
      status: 'active',
      severity: options.severity,
      financialPenalty: {
        amount: Math.floor(options.financialPenalty * recurrenceMultiplier),
        currency: 'DOP',
        deductedFrom: 'future_earnings'
      },
      ratingImpact: {
        pointsDeducted: options.ratingPenalty * recurrenceMultiplier,
        isConfigurable: this.penaltyConfig.ratingPenaltyConfigurable
      },
      availabilityRestriction: {
        restrictionType: 'none'
      },
      reason: options.reason,
      description: options.description,
      issuedBy: 'system',
      issuedByRole: 'system',
      isRecurring,
      previousPenaltyIds: previousPenalties.map(p => String(p._id)),
      recurrenceMultiplier,
      metadata: {
        automaticallyIssued: true,
        escalationLevel: previousPenalties.length + 1
      }
    }], { session: options.session });

    return penalty[0];
  }

  /**
   * Process client cancellation after job has started
   */
  async processClientCancellationAfterStart(request: CancellationRequest): Promise<CancellationResult> {
    const session = await mongoose.startSession();

    try {
      return await session.withTransaction(async () => {
        // Validate request and get job details
        const { job, bid, escrowTx } = await this.validateCancellationRequest(request, 'client');

        // Check timing - must be after job start
        const scheduledDateTime = parseJobDateTime(job.date, job.hour, 'America/Santo_Domingo');
        const now = new Date();

        if (now < scheduledDateTime) {
          throw new CancellationNotAllowedError('Job has not started yet. Use pre-start cancellation.');
        }

        // Require explanation for after-start cancellations
        if (!request.explanation || request.explanation.trim().length < 10) {
          throw new InvalidCancellationError('Detailed explanation required for after-start cancellations');
        }

        // Calculate retention based on job progress (50-70% suggested)
        const retentionPercentage = 60; // Default 60% retention
        const retainedAmount = Math.floor((escrowTx.amount * retentionPercentage) / 100);
        const refundAmount = escrowTx.amount - retainedAmount;

        // Create cancellation record
        const cancellationRecord = await CancellationRecord.create([{
          jobId: request.jobId,
          bidId: bid ? String(bid._id) : undefined,
          initiatedBy: request.initiatedBy,
          initiatorRole: 'client',
          type: 'client_after_start',
          status: 'processing',
          scheduledStartTime: scheduledDateTime,
          cancellationTime: now,
          hoursBeforeStart: (scheduledDateTime.getTime() - now.getTime()) / (1000 * 60 * 60), // Negative value
          originalAmount: escrowTx.amount,
          penaltyAmount: retainedAmount,
          refundAmount,
          adminShare: Math.floor(retainedAmount * 0.3), // 30% to platform
          operatorShare: Math.floor(retainedAmount * 0.7), // 70% to operator
          currency: escrowTx.currency,
          reason: request.reason,
          explanation: request.explanation,
          evidenceUrls: request.evidenceUrls || [],
          paymentTransactionIds: [String(escrowTx._id)],
          creditTransactionIds: [],
          metadata: {
            clientNotified: false,
            operatorNotified: false,
            ratingImpactApplied: false,
            jobReposted: false,
            rescheduleOffered: false
          }
        }], { session });

        // Process refund to client's credit balance
        let creditTransactionId = null;
        if (refundAmount > 0) {
          const creditTransaction = await this.creditBalanceService.addCredit({
            userId: String(job.ownerId),
            type: 'refund',
            amount: refundAmount,
            currency: escrowTx.currency,
            description: `Partial refund for after-start cancellation`,
            metadata: {
              jobId: request.jobId,
              bidId: bid ? String(bid._id) : undefined,
              cancellationId: String(cancellationRecord[0]._id),
              paymentTransactionId: String(escrowTx._id)
            }
          });
          creditTransactionId = String(creditTransaction._id);
        }

        // Compensate operator for work done
        if (job.assignedOperatorId && Math.floor(retainedAmount * 0.7) > 0) {
          await this.creditBalanceService.addCredit({
            userId: String(job.assignedOperatorId),
            type: 'penalty_compensation',
            amount: Math.floor(retainedAmount * 0.7),
            currency: escrowTx.currency,
            description: `Compensation for after-start cancellation`,
            metadata: {
              jobId: request.jobId,
              bidId: bid ? String(bid._id) : undefined,
              cancellationId: String(cancellationRecord[0]._id),
              paymentTransactionId: String(escrowTx._id)
            }
          });
        }

        // Update job status
        await Job.findByIdAndUpdate(
          request.jobId,
          {
            status: 'cancelled',
            cancelledAt: now,
            cancelReason: request.reason
          },
          { session }
        );

        // Update cancellation record
        const updateData: any = {
          status: 'completed',
          processedAt: now,
          'metadata.clientNotified': true,
          'metadata.operatorNotified': true
        };

        if (creditTransactionId) {
          updateData.creditTransactionIds = [creditTransactionId];
        }

        await CancellationRecord.findByIdAndUpdate(
          cancellationRecord[0]._id,
          updateData,
          { session }
        );

        logger.info(`Processed client after-start cancellation for job ${request.jobId}`);

        return {
          cancellationId: String(cancellationRecord[0]._id),
          refundAmount,
          penaltyAmount: retainedAmount,
          creditAmount: refundAmount,
          status: 'completed',
          message: `After-start cancellation processed. ${retentionPercentage}% retained for work done. Remaining amount credited to your balance.`
        };
      });
    } finally {
      await session.endSession();
    }
  }

  /**
   * Process operator cancellation after arriving at location
   */
  async processOperatorCancellationAfterArrival(request: CancellationRequest): Promise<CancellationResult> {
    const session = await mongoose.startSession();

    try {
      return await session.withTransaction(async () => {
        // Validate request and get job details
        const { job, bid, escrowTx } = await this.validateCancellationRequest(request, 'operator');

        // This type of cancellation can happen at any time after operator accepts
        const scheduledDateTime = parseJobDateTime(job.date, job.hour, 'America/Santo_Domingo');
        const now = new Date();

        // Higher penalties for after-arrival cancellation
        const penaltyAmount = this.penaltyConfig.operatorAfterArrivalFinancialPenalty;
        const refundAmount = escrowTx.amount; // Full refund to client

        // Create cancellation record
        const cancellationRecord = await CancellationRecord.create([{
          jobId: request.jobId,
          bidId: bid ? String(bid._id) : undefined,
          initiatedBy: request.initiatedBy,
          initiatorRole: 'operator',
          type: 'operator_after_arrival',
          status: 'processing',
          scheduledStartTime: scheduledDateTime,
          cancellationTime: now,
          hoursBeforeStart: (scheduledDateTime.getTime() - now.getTime()) / (1000 * 60 * 60),
          originalAmount: escrowTx.amount,
          penaltyAmount,
          refundAmount,
          adminShare: 0,
          operatorShare: 0,
          currency: escrowTx.currency,
          reason: request.reason,
          explanation: request.explanation,
          evidenceUrls: request.evidenceUrls || [],
          paymentTransactionIds: [String(escrowTx._id)],
          creditTransactionIds: [],
          metadata: {
            clientNotified: false,
            operatorNotified: false,
            ratingImpactApplied: false,
            jobReposted: false,
            rescheduleOffered: true
          }
        }], { session });

        // Process severe financial penalty for operator
        if (penaltyAmount > 0) {
          const operatorPenalty = await this.createOperatorPenalty({
            operatorId: request.initiatedBy,
            jobId: request.jobId,
            bidId: bid ? String(bid._id) : undefined,
            cancellationId: String(cancellationRecord[0]._id),
            type: 'cancellation_after_arrival',
            severity: 'major',
            financialPenalty: penaltyAmount,
            ratingPenalty: this.penaltyConfig.operatorAfterArrivalRatingPenalty,
            reason: request.reason,
            description: `Operator cancelled after arriving at location. Severe penalty: ${penaltyAmount} ${escrowTx.currency}`,
            session
          });

          await CancellationRecord.findByIdAndUpdate(
            cancellationRecord[0]._id,
            { operatorPenaltyId: String(operatorPenalty._id) },
            { session }
          );
        }

        // Process full refund to client's credit balance
        const creditTransaction = await this.creditBalanceService.addCredit({
          userId: String(job.ownerId),
          type: 'refund',
          amount: refundAmount,
          currency: escrowTx.currency,
          description: `Full refund for operator cancellation after arrival`,
          metadata: {
            jobId: request.jobId,
            bidId: bid ? String(bid._id) : undefined,
            cancellationId: String(cancellationRecord[0]._id),
            paymentTransactionId: String(escrowTx._id)
          }
        });

        // Update job status
        await Job.findByIdAndUpdate(
          request.jobId,
          {
            status: 'cancelled',
            cancelledAt: now,
            cancelReason: request.reason
          },
          { session }
        );

        // Update cancellation record
        await CancellationRecord.findByIdAndUpdate(
          cancellationRecord[0]._id,
          {
            status: 'completed',
            processedAt: now,
            creditTransactionIds: [String(creditTransaction._id)],
            'metadata.clientNotified': true,
            'metadata.operatorNotified': true,
            'metadata.rescheduleOffered': true
          },
          { session }
        );

        logger.info(`Processed operator after-arrival cancellation for job ${request.jobId}`);

        return {
          cancellationId: String(cancellationRecord[0]._id),
          refundAmount,
          penaltyAmount,
          creditAmount: refundAmount,
          status: 'completed',
          message: 'Cancellation processed successfully. Severe penalty applied to operator. Client has been fully refunded.'
        };
      });
    } finally {
      await session.endSession();
    }
  }

  /**
   * Update penalty configuration
   */
  updatePenaltyConfiguration(config: Partial<PenaltyConfiguration>): void {
    this.penaltyConfig = { ...this.penaltyConfig, ...config };
    logger.info('Penalty configuration updated', config);
  }

  /**
   * Get current penalty configuration
   */
  getPenaltyConfiguration(): PenaltyConfiguration {
    return { ...this.penaltyConfig };
  }
}
