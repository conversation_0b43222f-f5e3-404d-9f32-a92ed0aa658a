import {
  ProcessDataVaultResponse,
  ProcessPaymentResponse,
  ProcessThreeDSMethodResponse,
  ProcessThreeDSChallengeResponse,
  RefundResponse,
  ProcessPostResponse
} from '../types/azul';
import { getCardBrand } from '../utils/cardBrandDetection';

/**
 * Mock Azul responses for development when sandbox is unavailable
 * Only use this when AZUL_MOCK_MODE=true in environment
 */
export class AzulMockService {
  // Runtime guard to prevent accidental use of the mock in non-dev environments
  private static ensureMockMode(): void {
    if (process.env.AZUL_MOCK_MODE !== 'true') {
      throw new Error('AzulMockService: AZUL_MOCK_MODE must be set to "true" to use the mock service');
    }
  }

  static createDataVaultToken(cardNumber: string): ProcessDataVaultResponse {
    this.ensureMockMode();
    const last4 = cardNumber.slice(-4);
    const brand = getCardBrand(cardNumber);

    return {
      ResponseCode: '00',
      ResponseMessage: 'APPROVED',
      DataVaultToken: `DV_${Date.now()}_${Math.random().toString(36).substring(2, 8)}_${Math.random().toString(36).substring(2, 8)}`,
      CardNumber: `${brand.toUpperCase()}************${last4}`,
      HasCVV: true,
      Expiration: '1225',
      CardType: 'CREDIT'
    };
  }

  static deleteDataVaultToken(token: string): ProcessDataVaultResponse {
    this.ensureMockMode();
    return {
      ResponseCode: '00',
      ResponseMessage: 'TOKEN DELETED',
      DataVaultToken: token
    };
  }

  static processPayment(amount: number, currency: string = 'DOP'): ProcessPaymentResponse {
    this.ensureMockMode();
    const orderId = `ORD_${Date.now()}`;
    const authCode = `${Math.floor(Math.random() * 900000) + 100000}`;

    return {
      ResponseCode: '00',
      ResponseMessage: 'APPROVED',
      IsoCode: '00',
      AzulOrderId: orderId,
      AuthorizationCode: authCode,
      RRN: `RRN_${Date.now()}`,
      OriginalAmount: amount
    };
  }

  static processPaymentDeclined(): ProcessPaymentResponse {
    this.ensureMockMode();
    return {
      ResponseCode: '05',
      ResponseMessage: 'DECLINED',
      IsoCode: '05',
      AzulOrderId: `ORD_${Date.now()}`,
      ErrorDescription: 'Card declined by issuer'
    };
  }

  static processPaymentWith3DS(): ProcessPaymentResponse {
    this.ensureMockMode();
    return {
      ResponseCode: '00',
      ResponseMessage: 'APPROVED',
      IsoCode: '3D',
      AzulOrderId: `ORD_${Date.now()}`,
      ThreeDSMethod: {
        MethodForm: '<form id="threeDSMethodForm" method="POST" action="https://mock.3ds.com/method"><input type="hidden" name="threeDSMethodData" value="mockData"/></form>', ThreeDSServerTransID: `3DS_${Date.now()}`
      }
    };
  }

  static processThreeDSMethod(): ProcessThreeDSMethodResponse {
    this.ensureMockMode();
    return {
      ResponseCode: '00',
      ResponseMessage: 'METHOD COMPLETED',
      IsoCode: '00',
      AzulOrderId: `ORD_${Date.now()}`
    };
  }

  static processThreeDSChallenge(): ProcessThreeDSChallengeResponse {
    this.ensureMockMode();
    return {
      ResponseCode: '00',
      ResponseMessage: 'AUTHENTICATION SUCCESSFUL',
      IsoCode: '00',
      AzulOrderId: `ORD_${Date.now()}`,
      AuthorizationCode: `${Math.floor(Math.random() * 900000) + 100000}`,
      RRN: `RRN_${Date.now()}`
    };
  }

  static processRefund(amount: number): RefundResponse {
    this.ensureMockMode();
    return {
      ResponseCode: '00',
      ResponseMessage: 'REFUND APPROVED',
      IsoCode: '00',
      AzulOrderId: `ORD_${Date.now()}`,
      AuthorizationCode: `${Math.floor(Math.random() * 900000) + 100000}`,
      RRN: `RRN_${Date.now()}`,
      OriginalAmount: amount
    };
  }

  static processPost(operation: 'CAPTURE' | 'VOID'): ProcessPostResponse {
    this.ensureMockMode();
    return {
      ResponseCode: '00',
      ResponseMessage: `${operation} APPROVED`,
      IsoCode: '00',
      AzulOrderId: `ORD_${Date.now()}`,
      AuthorizationCode: `${Math.floor(Math.random() * 900000) + 100000}`,
      RRN: `RRN_${Date.now()}`
    };
  }

  /**
   * Simulate network delay
   */
  static async delay(ms: number = 1000): Promise<void> {
    this.ensureMockMode();
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export default AzulMockService;
