import React from 'react'
import { StyleSheet } from 'react-native'
import Svg, { <PERSON>, <PERSON> } from 'react-native-svg'

const WrenchIcon = () => {
    return (

        <Svg fill="#000000"id="Capa_1" height="50" viewBox="0 0 511.732 511.732" width="50">
            <G>
                <Path d="M338.196,146.196l-7.999,8c-2.929,2.929-2.929,7.678,0,10.606c1.464,1.465,3.384,2.197,5.303,2.197   s3.839-0.732,5.303-2.197l8-8c2.929-2.929,2.928-7.678,0-10.606C345.875,143.267,341.126,143.268,338.196,146.196z" />
                <Path d="M314.197,170.196l-168,168c-2.929,2.929-2.929,7.678,0,10.606c1.464,1.464,3.384,2.197,5.303,2.197   s3.839-0.732,5.303-2.197l168-168c2.929-2.929,2.929-7.678,0-10.606C321.875,167.268,317.126,167.268,314.197,170.196z" />
                <Path d="M485.286,72.095c-5.79-2.346-12.382-1.01-16.797,3.406l-32.876,32.876l-38.08-10.203l-10.203-38.08l33.552-33.552   c4.431-4.431,5.758-10.799,3.465-16.621c-2.276-5.779-7.768-9.654-13.99-9.872c-15.153-0.53-30.085,3.23-44.376,11.177   c-16.95,9.427-30.566,24.162-38.34,41.493c-7.68,17.122-9.568,34.962-5.63,53.095L75.692,352.132   C74.263,352.044,72.874,352,71.5,352C32.075,352,0,384.074,0,423.5S32.075,495,71.5,495s71.5-32.075,71.5-71.5   c0-1.374-0.044-2.764-0.133-4.192l246.318-246.318c18.161,3.945,36.031,2.043,53.183-5.669   c17.329-7.792,32.034-21.367,41.406-38.223c7.662-13.78,11.436-28.188,11.217-42.823C494.897,80.013,491.088,74.448,485.286,72.095   z M470.665,121.808c-7.813,14.051-20.046,25.355-34.448,31.832c-15.477,6.959-30.991,8.211-47.426,3.825   c-2.585-0.689-5.344,0.051-7.237,1.943L129.792,411.169c-1.608,1.607-2.399,3.858-2.152,6.118c0.242,2.216,0.36,4.248,0.36,6.212   c0,31.154-25.346,56.5-56.5,56.5S15,454.654,15,423.5S40.346,367,71.5,367c1.964,0,3.996,0.118,6.212,0.36   c2.259,0.245,4.51-0.544,6.118-2.152l251.761-251.761c1.893-1.893,2.633-4.65,1.943-7.237c-4.379-16.411-3.139-31.9,3.792-47.352   c6.458-14.397,17.802-26.658,31.943-34.522c12.039-6.695,23.992-9.74,36.562-9.295c0.166,0.005,0.416,0.014,0.558,0.377   c0.114,0.289,0.013,0.39-0.115,0.518L373.66,52.549c-1.895,1.895-2.635,4.656-1.941,7.244l12.445,46.445   c0.694,2.588,2.715,4.61,5.304,5.304l46.445,12.445c2.588,0.693,5.35-0.047,7.244-1.941l35.938-35.938h0   c0.123-0.123,0.239-0.24,0.554-0.111c0.337,0.137,0.34,0.332,0.343,0.503C480.174,98.643,477.123,110.192,470.665,121.808z" />
                <Path d="M71.5,376C45.309,376,24,397.308,24,423.5S45.309,471,71.5,471s47.5-21.309,47.5-47.5S97.691,376,71.5,376z M71.5,456   C53.58,456,39,441.42,39,423.5S53.58,391,71.5,391s32.5,14.58,32.5,32.5S89.42,456,71.5,456z" />
            </G>
        </Svg>
    )
}

export default WrenchIcon

const styles = StyleSheet.create({})
