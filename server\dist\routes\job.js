"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const express_1 = tslib_1.__importDefault(require("express"));
const multer_1 = tslib_1.__importDefault(require("multer"));
const authentication_1 = require("../middleware/authentication");
const middleware_1 = require("../middleware");
const job_1 = require("../controllers/job");
const router = express_1.default.Router();
// Configure multer for image uploads
const upload = (0, multer_1.default)({
    storage: multer_1.default.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB per file
        files: 5 // Maximum 5 files per request
    },
    fileFilter: (req, file, cb) => {
        // Only allow image files
        const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
        if (allowedMimeTypes.includes(file.mimetype)) {
            cb(null, true);
        }
        else {
            cb(new Error(`Invalid file type. Allowed types: ${allowedMimeTypes.join(', ')}`));
        }
    }
});
// Create job directly (without chat)
router.post('/direct', authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(job_1.createJobDirect));
// Upload images for a job
router.post('/:jobId/images', middleware_1.validateJobId, authentication_1.AuthenticateTokenOAuth, upload.array('images', 5), (0, middleware_1.tryCatch)(job_1.uploadJobImages));
// Upload completion photos for a job
router.post('/:jobId/completion-photos', middleware_1.validateJobId, authentication_1.AuthenticateTokenOAuth, upload.array('photos', 10), (0, middleware_1.tryCatch)(job_1.uploadCompletionPhotos));
// Operator rates client for a job
router.post('/:jobId/client-rating', middleware_1.validateJobId, authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(job_1.rateClientForJob));
// Operator requests materials quote for a job
router.post('/:jobId/material-orders', middleware_1.validateJobId, authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(job_1.requestMaterialsQuote));
// Client approves/rejects a material order
router.post('/material-orders/:orderId/approve', authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(job_1.approveMaterialOrder));
router.post('/material-orders/:orderId/reject', authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(job_1.rejectMaterialOrder));
// Get available jobs for operators
router.get('/available', authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(job_1.getAvailableJobs));
// Get user's jobs
router.get('/', authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(job_1.getUserJobs));
// Get a specific job
router.get('/:jobId', middleware_1.validateJobId, authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(job_1.getJob));
// Update job status
router.patch('/:jobId/status', middleware_1.validateJobId, authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(job_1.updateJobStatus));
// Delete a job
router.delete('/:jobId', middleware_1.validateJobId, authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(job_1.deleteJob));
exports.default = router;
