import LottieView from 'lottie-react-native'
import React from 'react'
import { StyleSheet, View } from 'react-native'

interface ScreenProps {
  noBackground?: boolean
}
const ScreenSpinnerLoader = ({noBackground}: ScreenProps) => {

  return (
    <View style={[styles.container, {backgroundColor: noBackground ? 'transparent': '#00000040'}]}>
      <View style={styles.viewport}>
        <LottieView
          source={require('@/assets/animations/wrench.json')}
          style={{ height: 130, aspectRatio: 1 }}
          speed={1}
          autoPlay
          loop
        />
      </View>
    </View>

  )
}

export default ScreenSpinnerLoader


const styles = StyleSheet.create({
  container: {
    height: '100%',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',

    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 100000,
    
  },
  viewport: {
    zIndex: 100,
    width: 150,
    height: 150,
    borderRadius: 30,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: "white"
  },

})