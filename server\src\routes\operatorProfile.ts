import express from "express";
const router = express.Router();

import { Request, Response } from "express";
import { tryCatch } from "../middleware";
import { AuthenticateTokenOAuth } from "../middleware/authentication";
import {
  createOrUpdateOperatorProfile,
  getOperatorProfile,
  updateOperatorAvailability,
  switchUserRole,
  getOperatorsByCategory
} from "../controllers/operatorProfile";

// Create or update operator profile
router.route("/profile").post(
  AuthenticateTokenOAuth,
  tryCatch(createOrUpdateOperatorProfile)
);

// Get operator profile (own or by ID)
router.route("/profile").get(
  AuthenticateTokenOAuth,
  tryCatch(getOperatorProfile)
);

router.route("/profile/:operatorId").get(
  AuthenticateTokenOAuth,
  tryCatch(getOperatorProfile)
);

// Update operator availability
router.route("/availability").patch(
  AuthenticateTokenOAuth,
  tryCatch(updateOperatorAvailability)
);

// Switch user role between 'user' and 'operator'
router.route("/switch-role").post(
  AuthenticateTokenOAuth,
  tryCatch(switchUserRole)
);

// Get operators by category (for browsing)
router.route("/category/:category").get(
  tryCatch(getOperatorsByCategory)
);

export default router;
