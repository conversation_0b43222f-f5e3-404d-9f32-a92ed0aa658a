"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const express_1 = tslib_1.__importDefault(require("express"));
const middleware_1 = require("../middleware");
const client_1 = require("../controllers/client");
const router = express_1.default.Router();
// Public reputation summary for a client
router.get('/:clientId/reputation', (req, res, next) => {
    const { clientId } = req.params;
    if (!/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|[0-9a-f]{24})$/i.test(clientId)) {
        return res.status(400).json({ error: 'Invalid clientId' });
    }
    next();
}, (0, middleware_1.tryCatch)(client_1.getClientReputation));
exports.default = router;
