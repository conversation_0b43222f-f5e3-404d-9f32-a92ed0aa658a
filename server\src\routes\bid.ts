import express from "express";
const router = express.Router();
import { tryCatch } from "../middleware";
import { AuthenticateTokenOAuth } from "../middleware/authentication";
import {
  createBid,
  getJobBids,
  acceptBid,
  getOperatorBids,
  withdrawBid
} from "../controllers/bid";

// Create a bid for a job
router.route("/jobs/:jobId/bids").post(
  AuthenticateTokenOAuth,
  tryCatch(createBid)
);

// Get all bids for a specific job (for job owners)
router.route("/jobs/:jobId/bids").get(
  AuthenticateTokenOAuth,
  tryCatch(getJobBids)
);

// Accept a specific bid (for job owners)
router.route("/bids/:bidId/accept").post(
  AuthenticateTokenOAuth,
  tryCatch(acceptBid)
);

// Get all bids made by the current operator
router.route("/my-bids").get(
  AuthenticateTokenOAuth,
  tryCatch(getOperatorBids)
);

// Withdraw a bid (for operators)
router.route("/bids/:bidId/withdraw").post(
  AuthenticateTokenOAuth,
  tryCatch(withdrawBid)
);

export default router;
