"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseJobDateTime = parseJobDateTime;
const luxon_1 = require("luxon");
// Parse job date/time strings safely and return a Date object representing the specified local time.// dateStr: DD/MM/YYYY, timeStr: HH:MM
function parseJobDateTime(dateStr, timeStr, timezone = 'America/Santo_Domingo') {
    try {
        if (typeof dateStr !== 'string' || typeof timeStr !== 'string') {
            throw new Error('dateStr and timeStr must be strings in formats DD/MM/YYYY and HH:MM');
        }
        const dateParts = dateStr.trim().split('/').map(s => s.trim());
        const timeParts = timeStr.trim().split(':').map(s => s.trim());
        if (dateParts.length !== 3)
            throw new Error('date must be in DD/MM/YYYY format');
        if (timeParts.length !== 2)
            throw new Error('time must be in HH:MM format');
        const [dStr, mStr, yStr] = dateParts;
        const [hhStr, mmStr] = timeParts;
        const toInt = (s, label) => {
            if (!/^\d+$/.test(s)) {
                throw new Error(`${label} must be an integer without signs or decimals`);
            }
            return parseInt(s, 10);
        };
        const d = toInt(dStr, 'day');
        const m = toInt(mStr, 'month');
        const y = toInt(yStr, 'year');
        const hh = toInt(hhStr, 'hour');
        const mm = toInt(mmStr, 'minute');
        // Values are validated as integers above.
        if (d < 1 || d > 31)
            throw new Error('day out of range (1-31)');
        if (m < 1 || m > 12)
            throw new Error('month out of range (1-12)');
        if (y < 1970 || y > 2100)
            throw new Error('year out of supported range (1970-2100)');
        if (hh < 0 || hh > 23)
            throw new Error('hour out of range (0-23)');
        if (mm < 0 || mm > 59)
            throw new Error('minute out of range (0-59)');
        // Use Luxon to create a timezone-aware DateTime and validate the constructed fields
        const localDateTime = luxon_1.DateTime.fromObject({ year: y, month: m, day: d, hour: hh, minute: mm, second: 0 }, { zone: timezone });
        if (!localDateTime.isValid) {
            throw new Error(`invalid date/time for timezone ${timezone}: ${localDateTime.invalidReason || localDateTime.invalidExplanation || 'unknown'}`);
        }
        if (localDateTime.year !== y || localDateTime.month !== m || localDateTime.day !== d || localDateTime.hour !== hh || localDateTime.minute !== mm) {
            throw new Error('date/time does not correspond to a valid calendar instant (possible out-of-range day for month or DST gap)');
        }
        return localDateTime.toJSDate();
    }
    catch (err) {
        if (err instanceof Error)
            throw err;
        throw new Error('failed to parse job date/time');
    }
}
