"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentProcessorService = void 0;
const tslib_1 = require("tslib");
const schemas_1 = require("../database/schemas");
const azulClient_1 = require("./azulClient");
const paymentMethodService_1 = require("./paymentMethodService");
const crypto_1 = tslib_1.__importDefault(require("crypto"));
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const paymentErrors_1 = require("../errors/paymentErrors");
class PaymentProcessorService {
    constructor(environment = 'sandbox') {
        this.azulClient = new azulClient_1.AzulClient(environment);
        this.paymentMethodService = new paymentMethodService_1.PaymentMethodService(environment);
    }
    /**
     * Process a payment charge
     */
    chargePaymentMethod(userId, paymentMethodId, request) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
            try {
                // Validate payment method ownership
                const paymentMethod = yield this.paymentMethodService.getPaymentMethodById(userId, paymentMethodId);
                if (!paymentMethod) {
                    throw new Error('Payment method not found or does not belong to user');
                }
                // Validate amount
                if (request.amount <= 0) {
                    throw new Error('Amount must be greater than 0');
                }
                // Validate idempotency key presence
                if (!request.idempotencyKey) {
                    throw new paymentErrors_1.InvalidPaymentDataError('Missing idempotencyKey');
                }
                // Scope idempotency lookup by idempotencyKey + userId (+ paymentMethodId when present) to avoid cross-user collisions
                // Always include paymentMethodId in query to avoid undefined vs null mismatches
                const idempotencyQuery = { idempotencyKey: request.idempotencyKey, userId, paymentMethodId: paymentMethodId !== null && paymentMethodId !== void 0 ? paymentMethodId : null };
                const existingTransaction = yield schemas_1.PaymentTransaction.findOne(idempotencyQuery);
                if (existingTransaction) {
                    // Verify invariant fields match the current request to prevent idempotency replays with different parameters
                    const mismatches = [];
                    if (existingTransaction.amount !== request.amount)
                        mismatches.push('amount');
                    if (existingTransaction.currency !== request.currency)
                        mismatches.push('currency');
                    if (existingTransaction.type !== request.type)
                        mismatches.push('type');
                    if ((existingTransaction.jobId || null) !== (request.jobId || null))
                        mismatches.push('jobId');
                    if ((existingTransaction.bidId || null) !== (request.bidId || null))
                        mismatches.push('bidId');
                    // Compare using null-coalescing on both sides to treat undefined and null consistently
                    if ((((_a = existingTransaction.paymentMethodId) !== null && _a !== void 0 ? _a : null) !== ((_b = idempotencyQuery.paymentMethodId) !== null && _b !== void 0 ? _b : null)))
                        mismatches.push('paymentMethodId');
                    if (mismatches.length > 0) {
                        throw new paymentErrors_1.IdempotencyConflictError(`Idempotency key conflict: differing fields: ${mismatches.join(', ')}`);
                    }
                    // If invariants match, return the stored result
                    return this.formatTransactionResponse(existingTransaction);
                }
                // Generate unique Azul Order ID
                const azulOrderId = azulClient_1.AzulClient.generateOrderId();
                // Atomically insert transaction if not exists for this user + idempotencyKey.
                // Use $setOnInsert to avoid overwriting existing records.
                const upsertFilter = { userId, idempotencyKey: request.idempotencyKey };
                if (paymentMethodId)
                    upsertFilter.paymentMethodId = paymentMethodId;
                const setOnInsert = {
                    jobId: request.jobId,
                    bidId: request.bidId,
                    userId,
                    paymentMethodId,
                    amount: request.amount,
                    currency: request.currency,
                    status: 'initiated',
                    type: request.type,
                    providerReference: azulOrderId,
                    idempotencyKey: request.idempotencyKey
                };
                let transaction;
                try {
                    transaction = yield schemas_1.PaymentTransaction.findOneAndUpdate(upsertFilter, { $setOnInsert: setOnInsert }, { new: true, upsert: true, setDefaultsOnInsert: true }).exec();
                }
                catch (err) {
                    // Handle duplicate key race (e.g., providerReference unique constraint) by fetching existing transaction
                    if ((err === null || err === void 0 ? void 0 : err.code) === 11000 && ((_c = err === null || err === void 0 ? void 0 : err.keyPattern) === null || _c === void 0 ? void 0 : _c.providerReference)) {
                        transaction = yield schemas_1.PaymentTransaction.findOne(upsertFilter).exec();
                        if (!transaction)
                            throw err; // rethrow if not found
                    }
                    else if ((err === null || err === void 0 ? void 0 : err.code) === 11000) {
                        // Log which unique constraint was violated for debugging
                        console.error('Duplicate key error on unexpected field:', err.keyPattern);
                        throw err;
                    }
                    else {
                        throw err;
                    }
                }
                if (!transaction) {
                    throw new paymentErrors_1.InvalidPaymentDataError('Transaction creation failed unexpectedly');
                }
                // Prepare Azul payment request
                const azulPaymentRequest = {
                    MerchantId: '', // Will be set by AzulClient
                    AzulOrderId: azulOrderId,
                    Amount: request.amount,
                    CurrencyCode: request.currency,
                    TrxType: request.type === 'Sale' ? 'Sale' : 'Hold',
                    paymentMethod: 'token',
                    DataVaultToken: paymentMethod.dataVaultToken,
                    SaveToDataVault: 0, // Token already saved
                    ECommerceUrl: process.env.FRONTEND_URL || 'https://app.manito.do'
                };
                // Add 3DS authentication if browser info is provided
                if (request.browserInfo) {
                    azulPaymentRequest.ThreeDSAuth = this.build3DSAuthRequest(request, azulOrderId);
                }
                else {
                    // Force no 3DS if browser info not provided (server-to-server)
                    azulPaymentRequest.ForceNo3DS = 1;
                }
                // Process payment with Azul
                const azulResponse = yield this.azulClient.processPayment(azulPaymentRequest);
                // Update transaction with Azul response
                transaction.azulResponse = {
                    isoCode: azulResponse.IsoCode,
                    responseMessage: azulResponse.ResponseMessage,
                    authCode: azulResponse.AuthorizationCode,
                    rrn: azulResponse.RRN,
                    originalAmount: azulResponse.OriginalAmount
                };
                // Handle different response scenarios
                if (azulResponse.IsoCode === '00') {
                    // Payment approved
                    transaction.status = request.type === 'Hold' ? 'escrow' : 'successful';
                    yield transaction.save();
                    return {
                        transactionId: String(transaction._id),
                        status: 'successful',
                        authCode: azulResponse.AuthorizationCode,
                        rrn: azulResponse.RRN
                    };
                }
                else if (azulResponse.IsoCode === '3D2METHOD' || ((_d = azulResponse.ThreeDSMethod) === null || _d === void 0 ? void 0 : _d.MethodForm)) {
                    // 3DS Method required
                    // Persist status and ThreeDS method payload so retries or subsequent fetches can obtain MethodForm and server trans id
                    transaction.status = 'initiated';
                    if (azulResponse.ThreeDSMethod) {
                        // Persist the whole ThreeDSMethod object (it contains MethodForm and ThreeDSServerTransID)
                        transaction.threeDSMethod = azulResponse.ThreeDSMethod;
                        if (azulResponse.ThreeDSMethod.ThreeDSServerTransID) {
                            transaction.threeDSServerTransID = azulResponse.ThreeDSMethod.ThreeDSServerTransID;
                        }
                    }
                    yield transaction.save();
                    return {
                        transactionId: String(transaction._id),
                        status: 'requires_action',
                        threeDSAction: {
                            type: 'method',
                            methodForm: (_e = azulResponse.ThreeDSMethod) === null || _e === void 0 ? void 0 : _e.MethodForm
                        }
                    };
                }
                else if (azulResponse.IsoCode === '3D' || ((_f = azulResponse.ThreeDSChallenge) === null || _f === void 0 ? void 0 : _f.ChallengeForm)) {
                    // 3DS Challenge required
                    transaction.status = 'requires_action';
                    // Persist challenge details so subsequent polls/requests can retrieve the action
                    transaction.threeDS = {
                        type: 'challenge',
                        challengeUrl: (_g = azulResponse.ThreeDSChallenge) === null || _g === void 0 ? void 0 : _g.RedirectPostUrl,
                        challengeForm: (_h = azulResponse.ThreeDSChallenge) === null || _h === void 0 ? void 0 : _h.ChallengeForm
                    };
                    if ((_j = azulResponse.ThreeDSChallenge) === null || _j === void 0 ? void 0 : _j.ThreeDSServerTransID) {
                        transaction.threeDSServerTransID = azulResponse.ThreeDSChallenge.ThreeDSServerTransID;
                    }
                    yield transaction.save();
                    return {
                        transactionId: transaction._id.toString(),
                        status: 'requires_action',
                        threeDSAction: {
                            type: 'challenge',
                            challengeUrl: (_k = azulResponse.ThreeDSChallenge) === null || _k === void 0 ? void 0 : _k.RedirectPostUrl,
                            challengeForm: (_l = azulResponse.ThreeDSChallenge) === null || _l === void 0 ? void 0 : _l.ChallengeForm
                        }
                    };
                }
                else {
                    // Payment failed
                    transaction.status = 'failed';
                    yield transaction.save();
                    const validation = azulClient_1.AzulClient.validateResponse(azulResponse);
                    return {
                        transactionId: transaction._id.toString(),
                        status: 'failed',
                        error: {
                            code: azulResponse.IsoCode || 'UNKNOWN',
                            message: validation.errorMessage || 'Payment failed'
                        }
                    };
                }
            }
            catch (error) {
                console.error('Error processing payment charge:', error);
                throw error;
            }
        });
    }
    /**
     * Process 3DS Method notification
     */
    processMethodNotification(transactionId, notificationData) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const transaction = yield schemas_1.PaymentTransaction.findById(transactionId);
                if (!transaction) {
                    throw new paymentErrors_1.NotFoundPaymentError('Transaction not found');
                }
                if (!transaction.threeDSServerTransID) {
                    throw new Error('Transaction does not have 3DS Server Trans ID');
                }
                // Verify correlation
                if (notificationData.threeDSServerTransID !== transaction.threeDSServerTransID) {
                    throw new Error('Mismatched ThreeDSServerTransID in method notification');
                }
                // Determine method notification status
                const methodNotificationStatus = notificationData.threeDSMethodData ?
                    'RECEIVED' : 'EXPECTED_BUT_NOT_RECEIVED';
                // Call Azul ProcessThreeDSMethod
                const azulResponse = yield this.azulClient.processThreeDSMethod({
                    MerchantId: '', // Will be set by AzulClient
                    AzulOrderId: transaction.providerReference,
                    ThreeDSServerTransID: transaction.threeDSServerTransID,
                    methodNotificationStatus
                });
                // Update transaction with response
                transaction.azulResponse = Object.assign(Object.assign({}, transaction.azulResponse), { isoCode: azulResponse.IsoCode, responseMessage: azulResponse.ResponseMessage, authCode: azulResponse.AuthorizationCode, rrn: azulResponse.RRN });
                // Handle response
                if (azulResponse.IsoCode === '00') {
                    transaction.status = 'successful';
                }
                else if (azulResponse.ThreeDSChallenge) {
                    // Challenge required - update status and persist challenge data
                    transaction.status = 'challenge_required';
                    transaction.threeDSChallenge = azulResponse.ThreeDSChallenge;
                    // Also store challenge data in a more accessible format for client handling
                    const challenge = azulResponse.ThreeDSChallenge;
                    transaction.challengeData = {
                        challengeForm: challenge.ChallengeForm || (typeof challenge === 'string' ? challenge : ''),
                        challengeUrl: challenge.RedirectPostUrl || challenge.ChallengeUrl || '',
                        challengeWindowSize: challenge.ChallengeWindowSize || '02', // Default window size
                        threeDSServerTransID: transaction.threeDSServerTransID,
                        createdAt: new Date()
                    };
                }
                else {
                    transaction.status = 'failed';
                }
                yield transaction.save();
                // Notify client about transaction status update
                // For challenge_required status, client should poll/check transaction status
                // and handle the 3DS challenge using the persisted challengeData
                // TODO: Implement WebSocket notification or polling endpoint for real-time updates
            }
            catch (error) {
                console.error('Error processing 3DS method notification:', error);
                throw error;
            }
        });
    }
    /**
     * Process 3DS Challenge response
     */
    processChallenge(transactionId, challengeResponse) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const transaction = yield schemas_1.PaymentTransaction.findById(transactionId);
                if (!transaction) {
                    throw new paymentErrors_1.NotFoundPaymentError('Transaction not found');
                }
                if (!transaction.threeDSServerTransID) {
                    throw new Error('Transaction does not have 3DS Server Trans ID');
                }
                // Call Azul ProcessThreeDSChallenge
                const azulResponse = yield this.azulClient.processThreeDSChallenge({
                    MerchantId: '', // Will be set by AzulClient
                    AzulOrderId: transaction.providerReference,
                    ThreeDSServerTransID: transaction.threeDSServerTransID,
                    CRes: challengeResponse
                });
                // Update transaction with final response
                transaction.azulResponse = Object.assign(Object.assign({}, transaction.azulResponse), { isoCode: azulResponse.IsoCode, responseMessage: azulResponse.ResponseMessage, authCode: azulResponse.AuthorizationCode, rrn: azulResponse.RRN });
                if (azulResponse.IsoCode === '00') {
                    transaction.status = 'successful';
                    yield transaction.save();
                    return {
                        transactionId: String(transaction._id),
                        status: 'successful',
                        authCode: azulResponse.AuthorizationCode,
                        rrn: azulResponse.RRN
                    };
                }
                else {
                    transaction.status = 'failed';
                    yield transaction.save();
                    const validation = azulClient_1.AzulClient.validateResponse(azulResponse);
                    return {
                        transactionId: String(transaction._id),
                        status: 'failed',
                        error: {
                            code: azulResponse.IsoCode || 'UNKNOWN',
                            message: validation.errorMessage || 'Payment failed'
                        }
                    };
                }
            }
            catch (error) {
                console.error('Error processing 3DS challenge:', error);
                throw error;
            }
        });
    }
    /**
     * Refund a transaction
     */
    refundTransaction(userId_1, transactionId_1, amount_1) {
        return tslib_1.__awaiter(this, arguments, void 0, function* (userId, transactionId, amount, reason = 'Customer request') {
            var _a;
            try {
                // Atomic reserve pattern to avoid races when issuing concurrent refunds simple idea:
                // - Atomically increment `refundedAmount` and push a pending refund entry only if refundedAmount + refundAmount <= amount
                // - Call Azul to process the refund
                // - On success, mark the refund entry processed and set transaction status if fully refunded
                // - On failure, rollback the reservation (decrement refundedAmount and remove pending refund)
                // Load transaction basic check (ownership & status)
                const tx = yield schemas_1.PaymentTransaction.findOne({ _id: transactionId, userId }).lean();
                if (!tx) {
                    throw new Error('Transaction not found or does not belong to user');
                }
                if (tx.status !== 'successful') {
                    throw new Error('Can only refund successful transactions');
                }
                const refundAmount = amount || tx.amount;
                if (refundAmount > tx.amount) {
                    throw new Error('Refund amount cannot exceed original transaction amount');
                }
                // Generate refund order ID (used as our local refund identifier)
                const refundOrderId = azulClient_1.AzulClient.generateOrderId();
                const now = new Date();
                // Attempt atomic reserve: increment refundedAmount and push a pending refund entry only when sufficient balance exists
                const reserveResult = yield schemas_1.PaymentTransaction.findOneAndUpdate({
                    _id: transactionId,
                    userId,
                    status: 'successful',
                    $expr: {
                        $lte: [
                            { $add: [{ $ifNull: ['$refundedAmount', 0] }, refundAmount] },
                            '$amount'
                        ]
                    }
                }, {
                    $inc: { refundedAmount: refundAmount },
                    $push: { refunds: { amount: refundAmount, reason, refundId: refundOrderId, status: 'pending', createdAt: now } }
                }, { new: true }).exec();
                if (!reserveResult) {
                    throw new Error('Total refunds cannot exceed original transaction amount');
                }
                // Call Azul to process the refund
                const azulResponse = yield this.azulClient.processRefund({
                    MerchantId: '', // Will be set by AzulClient
                    AzulOrderId: refundOrderId,
                    OriginalAzulOrderId: reserveResult.providerReference,
                    Amount: refundAmount,
                    CurrencyCode: reserveResult.currency
                });
                const validation = azulClient_1.AzulClient.validateResponse(azulResponse);
                if (!validation.isSuccess) {
                    // Rollback reservation with proper error handling and atomicity
                    const session = yield mongoose_1.default.startSession();
                    try {
                        yield session.withTransaction(() => tslib_1.__awaiter(this, void 0, void 0, function* () {
                            var _a, _b;
                            const rollbackResult = yield schemas_1.PaymentTransaction.findOneAndUpdate({ _id: transactionId, 'refunds.refundId': refundOrderId }, { $inc: { refundedAmount: -refundAmount }, $pull: { refunds: { refundId: refundOrderId } } }, { new: true, session }).exec();
                            if (!rollbackResult) {
                                console.error('Critical: Rollback failed - transaction document not found or refund entry missing', {
                                    transactionId: String(transactionId),
                                    refundOrderId,
                                    refundAmount,
                                    originalError: validation.errorMessage
                                });
                                throw new Error('Rollback failed: transaction document not found during rollback - manual reconciliation required');
                            }
                            // Verify rollback was successful by checking the document state
                            const verificationDoc = yield schemas_1.PaymentTransaction.findById(transactionId).select('refundedAmount refunds').session(session).exec();
                            if (verificationDoc) {
                                const hasFailedRefund = (_a = verificationDoc.refunds) === null || _a === void 0 ? void 0 : _a.some((r) => r.refundId === refundOrderId);
                                if (hasFailedRefund) {
                                    console.error('Critical: Rollback verification failed - refund entry still exists after rollback', {
                                        transactionId: String(transactionId),
                                        refundOrderId,
                                        documentState: { refundedAmount: verificationDoc.refundedAmount, refundCount: (_b = verificationDoc.refunds) === null || _b === void 0 ? void 0 : _b.length }
                                    });
                                    throw new Error('Rollback verification failed - inconsistent state detected');
                                }
                            }
                        }));
                    }
                    catch (rollbackError) {
                        console.error('Rollback transaction failed:', rollbackError);
                        // Mark transaction for manual reconciliation if rollback fails
                        try {
                            yield schemas_1.PaymentTransaction.findOneAndUpdate({ _id: transactionId }, {
                                $set: {
                                    'metadata.reconciliationRequired': true,
                                    'metadata.reconciliationReason': `Rollback failed for refund ${refundOrderId}: ${rollbackError instanceof Error ? rollbackError.message : String(rollbackError)}`,
                                    'metadata.reconciliationTimestamp': new Date()
                                }
                            }).exec();
                        }
                        catch (markError) {
                            console.error('Failed to mark transaction for reconciliation:', markError);
                        }
                        throw new Error(`Refund processing failed and rollback encountered errors: ${rollbackError instanceof Error ? rollbackError.message : String(rollbackError)}. Transaction marked for manual reconciliation.`);
                    }
                    finally {
                        yield session.endSession();
                    }
                    throw new Error(`Refund failed: ${validation.errorMessage}`);
                }
                // Finalize refund entry: mark processed and set processedAt with proper error handling
                const finalized = yield schemas_1.PaymentTransaction.findOneAndUpdate({ _id: transactionId, 'refunds.refundId': refundOrderId }, {
                    $set: { 'refunds.$.status': 'processed', 'refunds.$.processedAt': new Date(), 'refunds.$.azulResponse': azulResponse }
                }, { new: true }).exec();
                if (!finalized) {
                    console.error('Critical: Finalization failed - could not mark refund as processed', {
                        transactionId: String(transactionId),
                        refundOrderId,
                        refundAmount
                    });
                    // Mark for manual reconciliation since Azul processed the refund but we couldn't update our records
                    try {
                        yield schemas_1.PaymentTransaction.findOneAndUpdate({ _id: transactionId }, {
                            $set: {
                                'metadata.reconciliationRequired': true,
                                'metadata.reconciliationReason': `Refund processed by Azul (${refundOrderId}) but finalization update failed`,
                                'metadata.reconciliationTimestamp': new Date(),
                                'metadata.azulProcessedRefund': { refundOrderId, amount: refundAmount, azulResponse }
                            }
                        }).exec();
                    }
                    catch (markError) {
                        console.error('Failed to mark transaction for reconciliation after finalization failure:', markError);
                    }
                    throw new Error('Refund was processed by payment provider but local record update failed - transaction marked for manual reconciliation');
                }
                // If fully refunded, update transaction status
                const refundedAmt = ((_a = finalized.refundedAmount) !== null && _a !== void 0 ? _a : 0);
                if (refundedAmt >= finalized.amount) {
                    const statusUpdateResult = yield schemas_1.PaymentTransaction.updateOne({ _id: transactionId }, { $set: { status: 'refunded' } }).exec();
                    if (statusUpdateResult.matchedCount === 0) {
                        console.warn('Warning: Could not update transaction status to refunded', {
                            transactionId: String(transactionId),
                            refundedAmount: refundedAmt,
                            originalAmount: finalized.amount
                        });
                    }
                }
            }
            catch (error) {
                console.error('Error processing refund:', error);
                throw error;
            }
        });
    }
    /**
     * Capture a held transaction
     */
    captureTransaction(userId, transactionId, amount) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const transaction = yield schemas_1.PaymentTransaction.findOne({
                    _id: transactionId,
                    userId
                });
                if (!transaction) {
                    throw new Error('Transaction not found or does not belong to user');
                }
                if (transaction.type !== 'Hold') {
                    throw new Error('Can only capture held transactions');
                }
                if (transaction.status !== 'escrow') {
                    throw new Error('Transaction must be in escrow to capture');
                }
                const captureAmount = amount || transaction.amount;
                if (captureAmount > transaction.amount) {
                    throw new Error('Capture amount cannot exceed original transaction amount');
                }
                // Process capture with Azul
                const azulResponse = yield this.azulClient.processPost({
                    MerchantId: '', // Will be set by AzulClient
                    AzulOrderId: transaction.providerReference,
                    TrxType: 'Capture',
                    Amount: captureAmount
                });
                const validation = azulClient_1.AzulClient.validateResponse(azulResponse);
                if (!validation.isSuccess) {
                    throw new Error(`Capture failed: ${validation.errorMessage}`);
                }
                // Update transaction status
                transaction.status = 'successful'; // Or create a new 'captured' status
                yield transaction.save();
            }
            catch (error) {
                console.error('Error capturing transaction:', error);
                throw error;
            }
        });
    }
    /**
     * Void a held (escrow) transaction
     */
    voidTransaction(userId, transactionId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const transaction = yield schemas_1.PaymentTransaction.findOne({ _id: transactionId, userId });
                if (!transaction) {
                    throw new Error('Transaction not found or does not belong to user');
                }
                if (transaction.type !== 'Hold') {
                    throw new Error('Can only void held transactions');
                }
                if (transaction.status !== 'escrow') {
                    // Already captured or voided/refunded; treat as idempotent no-op
                    return;
                }
                const azulResponse = yield this.azulClient.processPost({
                    MerchantId: '',
                    AzulOrderId: transaction.providerReference,
                    TrxType: 'Void'
                });
                const validation = azulClient_1.AzulClient.validateResponse(azulResponse);
                if (!validation.isSuccess) {
                    throw new Error(`Void failed: ${validation.errorMessage}`);
                }
                transaction.status = 'voided';
                yield transaction.save();
            }
            catch (error) {
                console.error('Error voiding transaction:', error);
                throw error;
            }
        });
    }
    /**
     * Build 3DS authentication request
     */
    build3DSAuthRequest(request, azulOrderId) {
        var _a, _b, _c, _d, _e, _f, _g;
        const baseUrl = process.env.FRONTEND_URL || 'https://app.manito.do';
        // Defensive handling for browserInfo: use provided values or sensible defaults
        const bi = (_a = request.browserInfo) !== null && _a !== void 0 ? _a : {
            acceptHeader: '',
            userAgent: '',
            javaEnabled: false,
            language: '',
            colorDepth: 24,
            screenHeight: 0,
            screenWidth: 0,
            timeZoneOffset: 0,
            javascriptEnabled: false
        };
        return {
            ThreeDS2: 1,
            BrowserInfo: {
                AcceptHeader: (_b = bi.acceptHeader) !== null && _b !== void 0 ? _b : '',
                UserAgent: (_c = bi.userAgent) !== null && _c !== void 0 ? _c : '',
                JavaEnabled: Boolean(bi.javaEnabled),
                Language: (_d = bi.language) !== null && _d !== void 0 ? _d : '',
                ColorDepth: typeof bi.colorDepth === 'number' ? bi.colorDepth : 24,
                ScreenHeight: typeof bi.screenHeight === 'number' ? bi.screenHeight : 0,
                ScreenWidth: typeof bi.screenWidth === 'number' ? bi.screenWidth : 0,
                TimeZoneOffset: typeof bi.timeZoneOffset === 'number' ? bi.timeZoneOffset : 0,
                JavascriptEnabled: Boolean(bi.javascriptEnabled)
            },
            CardHolderInfo: {
                Email: (_e = request.customerInfo) === null || _e === void 0 ? void 0 : _e.email,
                MobilePhoneNumber: (_f = request.customerInfo) === null || _f === void 0 ? void 0 : _f.phone,
                BillingAddress: ((_g = request.customerInfo) === null || _g === void 0 ? void 0 : _g.billingAddress) ? {
                    Line1: request.customerInfo.billingAddress.line1,
                    Line2: request.customerInfo.billingAddress.line2,
                    City: request.customerInfo.billingAddress.city,
                    State: request.customerInfo.billingAddress.state,
                    PostalCode: request.customerInfo.billingAddress.postalCode,
                    CountryCode: request.customerInfo.billingAddress.countryCode
                } : undefined
            },
            PurchaseInfo: {
                PurchaseAmount: request.amount,
                PurchaseCurrency: request.currency,
                PurchaseExponent: 2, // Assuming 2 decimal places for DOP/USD
                PurchaseDate: new Date().toISOString().replace(/[-T:]/g, '').slice(0, 14) // YYYYMMDDHHMMSS
            },
            MerchantInfo: {
                MerchantName: 'Manito Services',
                MerchantCategoryCode: '7299' // Personal Services
            },
            TermUrl: `${baseUrl}/api/payments/3ds/return/${azulOrderId}`,
            MethodNotificationUrl: `${baseUrl}/api/payments/3ds/method-notification/${azulOrderId}`
        };
    }
    /**
     * Format transaction response
     */
    formatTransactionResponse(transaction) {
        var _a, _b, _c, _d;
        if (transaction.status === 'successful') {
            return {
                transactionId: String(transaction._id),
                status: 'successful',
                authCode: (_a = transaction.azulResponse) === null || _a === void 0 ? void 0 : _a.authCode,
                rrn: (_b = transaction.azulResponse) === null || _b === void 0 ? void 0 : _b.rrn
            };
        }
        else if (transaction.status === 'failed') {
            return {
                transactionId: String(transaction._id),
                status: 'failed',
                error: {
                    code: ((_c = transaction.azulResponse) === null || _c === void 0 ? void 0 : _c.isoCode) || 'UNKNOWN',
                    message: ((_d = transaction.azulResponse) === null || _d === void 0 ? void 0 : _d.responseMessage) || 'Payment failed'
                }
            };
        }
        else {
            return {
                transactionId: String(transaction._id),
                status: 'requires_action'
            };
        }
    }
    /**
     * Get transaction by ID
     */
    getTransaction(userId, transactionId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            return yield schemas_1.PaymentTransaction.findOne({
                _id: transactionId,
                userId
            });
        });
    }
    /**
     * Generate idempotency key
     */
    static generateIdempotencyKey() {
        return crypto_1.default.randomUUID();
    }
}
exports.PaymentProcessorService = PaymentProcessorService;
exports.default = PaymentProcessorService;
