"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importStar(require("mongoose"));
const materialOrderSchema = new mongoose_1.default.Schema({
    jobId: { type: String, required: true, ref: 'Job', index: true },
    ownerId: { type: String, required: true, ref: 'Account', index: true },
    operatorId: { type: String, required: true, ref: 'Account', index: true },
    items: {
        type: [
            {
                sku: { type: String, required: true },
                name: { type: String, required: true },
                quantity: {
                    type: Number, required: true, min: 1,
                    validate: {
                        validator: Number.isInteger,
                        message: 'quantity must be an integer'
                    }
                },
                unitPrice: {
                    type: Number, required: true, min: 0,
                    validate: {
                        validator: Number.isInteger,
                        message: 'unitPrice must be integer minor units (cents)'
                    }
                }
            }
        ],
        validate: {
            validator: (v) => Array.isArray(v) && v.length > 0,
            message: 'At least one item is required',
        },
    },
    subtotal: {
        type: Number, required: true, min: 0,
        validate: {
            validator: Number.isInteger,
            message: 'subtotal must be integer minor units (cents)'
        }
    },
    deliveryFee: {
        type: Number, default: 0, min: 0,
        validate: {
            validator: Number.isInteger,
            message: 'deliveryFee must be integer minor units (cents)'
        }
    },
    total: {
        type: Number, required: true, min: 0,
        validate: {
            validator: Number.isInteger,
            message: 'total must be integer minor units (cents)'
        }
    },
    currency: { type: String, enum: ['DOP', 'USD'], default: 'DOP' },
    status: { type: String, enum: ['pending', 'approved', 'rejected', 'delivered', 'cancelled'], default: 'pending', index: true },
    deliveryLocation: {
        type: new mongoose_1.Schema({
            latitude: { type: Number, required: true, min: -90, max: 90 },
            longitude: { type: Number, required: true, min: -180, max: 180 },
        }),
        required: false,
        default: null,
    },
}, { timestamps: true });
materialOrderSchema.index({ operatorId: 1, status: 1 });
exports.default = mongoose_1.default.model('MaterialOrder', materialOrderSchema);
