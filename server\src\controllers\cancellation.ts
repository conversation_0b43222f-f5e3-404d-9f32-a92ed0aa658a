import { Request, Response } from 'express';
import { z } from 'zod';
import { getAuthenticatedUser } from '../utils/auth';
import { CancellationService } from '../services/cancellationService';
import { CreditBalanceService } from '../services/creditBalanceService';
import { CancellationRecord, OperatorPenalty, Job } from '../database/schemas';
import { sendCustomNotification, getUserNotificationToken } from './notifications';
import { logger } from '../utils/logger';
import {
  CancellationError,
  InvalidCancellationError,
  CancellationNotAllowedError,
  UnauthorizedCancellationError
} from '../errors/cancellationErrors';

// Validation schemas
const cancellationRequestSchema = z.object({
  reason: z.string().min(5).max(500),
  explanation: z.string().max(2000).optional(),
  evidenceUrls: z.array(z.string().url()).optional(),
  cancellationType: z.enum([
    'operator_before_start',
    'client_less_than_24h',
    'client_after_start',
    'operator_after_arrival'
  ])
});

const withdrawalRequestSchema = z.object({
  amount: z.number().min(100), // Minimum 100 centavos (1 DOP)
  withdrawalMethod: z.enum(['credit_card', 'bank_transfer']),
  originalPaymentMethodId: z.string().optional(),
  currency: z.enum(['DOP', 'USD']).optional()
});

const penaltyConfigSchema = z.object({
  clientCancellationWorkerCompensation: z.number().min(0).max(100).optional(),
  clientCancellationPlatformFee: z.number().min(0).max(100).optional(),
  operatorCancellationFinancialPenalty: z.number().min(0).optional(),
  operatorCancellationRatingPenalty: z.number().min(0).max(5).optional(),
  operatorAfterArrivalFinancialPenalty: z.number().min(0).optional(),
  operatorAfterArrivalRatingPenalty: z.number().min(0).max(5).optional(),
  ratingPenaltyConfigurable: z.boolean().optional(),
  enableAutomaticPenalties: z.boolean().optional()
});

/**
 * Process job cancellation
 */
export const cancelJob = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (!user) {
      return res.status(401).json({ success: false, error: 'Authentication required' });
    }

    const { jobId } = req.params;
    const parse = cancellationRequestSchema.safeParse(req.body);
    
    if (!parse.success) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid request data', 
        details: parse.error.errors 
      });
    }

    const { reason, explanation, evidenceUrls, cancellationType } = parse.data;
    
    const cancellationService = CancellationService.getInstance();
    
    const request = {
      jobId,
      initiatedBy: String(user._id),
      reason,
      explanation,
      evidenceUrls
    };

    let result;
    
    switch (cancellationType) {
      case 'operator_before_start':
        result = await cancellationService.processOperatorCancellationBeforeStart(request);
        break;
      case 'client_less_than_24h':
        result = await cancellationService.processClientCancellationLessThan24h(request);
        break;
      case 'client_after_start':
        result = await cancellationService.processClientCancellationAfterStart(request);
        break;
      case 'operator_after_arrival':
        result = await cancellationService.processOperatorCancellationAfterArrival(request);
        break;
      default:
        return res.status(400).json({ 
          success: false, 
          error: 'Invalid cancellation type' 
        });
    }

    // Send notifications to relevant parties
    await sendCancellationNotifications(jobId, cancellationType, String(user._id));

    logger.info(`Job ${jobId} cancelled by user ${user._id} with type ${cancellationType}`);

    return res.json({
      success: true,
      data: result
    });

  } catch (error) {
    logger.error('Error processing job cancellation:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });

    if (error instanceof CancellationError) {
      return res.status(error.statusCode).json({
        success: false,
        error: error.message,
        code: error.code
      });
    }

    const message = error instanceof Error ? error.message : 'Failed to process cancellation';
    return res.status(500).json({ success: false, error: message });
  }
};

/**
 * Get cancellation history for a user
 */
export const getCancellationHistory = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (!user) {
      return res.status(401).json({ success: false, error: 'Authentication required' });
    }

    const { limit = 20, offset = 0, status, type } = req.query;
    
    const query: any = { initiatedBy: String(user._id) };
    if (status) query.status = status;
    if (type) query.type = type;

    const cancellations = await CancellationRecord.find(query)
      .sort({ createdAt: -1 })
      .limit(Number(limit))
      .skip(Number(offset))
      .populate('jobId', 'description category price date hour')
      .lean();

    const total = await CancellationRecord.countDocuments(query);

    return res.json({
      success: true,
      data: {
        cancellations,
        pagination: {
          total,
          limit: Number(limit),
          offset: Number(offset),
          hasMore: total > Number(offset) + Number(limit)
        }
      }
    });

  } catch (error) {
    logger.error('Error fetching cancellation history:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    const message = error instanceof Error ? error.message : 'Failed to fetch cancellation history';
    return res.status(500).json({ success: false, error: message });
  }
};

/**
 * Get credit balance information
 */
export const getCreditBalance = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (!user) {
      return res.status(401).json({ success: false, error: 'Authentication required' });
    }

    const creditBalanceService = CreditBalanceService.getInstance();
    const balanceInfo = await creditBalanceService.getCreditBalanceInfo(String(user._id));

    if (!balanceInfo) {
      return res.json({
        success: true,
        data: {
          balance: 0,
          currency: 'DOP',
          totalCredits: 0,
          totalWithdrawals: 0,
          pendingWithdrawals: 0,
          availableForWithdrawal: 0,
          lastTransactionAt: null
        }
      });
    }

    return res.json({
      success: true,
      data: balanceInfo
    });

  } catch (error) {
    logger.error('Error fetching credit balance:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    const message = error instanceof Error ? error.message : 'Failed to fetch credit balance';
    return res.status(500).json({ success: false, error: message });
  }
};

/**
 * Get credit transaction history
 */
export const getCreditTransactionHistory = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (!user) {
      return res.status(401).json({ success: false, error: 'Authentication required' });
    }

    const { limit = 50, offset = 0 } = req.query;
    
    const creditBalanceService = CreditBalanceService.getInstance();
    const transactions = await creditBalanceService.getTransactionHistory(
      String(user._id),
      Number(limit),
      Number(offset)
    );

    return res.json({
      success: true,
      data: {
        transactions,
        pagination: {
          limit: Number(limit),
          offset: Number(offset),
          hasMore: transactions.length === Number(limit)
        }
      }
    });

  } catch (error) {
    logger.error('Error fetching credit transaction history:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    const message = error instanceof Error ? error.message : 'Failed to fetch transaction history';
    return res.status(500).json({ success: false, error: message });
  }
};

/**
 * Process credit balance withdrawal
 */
export const withdrawCreditBalance = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (!user) {
      return res.status(401).json({ success: false, error: 'Authentication required' });
    }

    const parse = withdrawalRequestSchema.safeParse(req.body);
    
    if (!parse.success) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid withdrawal request', 
        details: parse.error.errors 
      });
    }

    const { amount, withdrawalMethod, originalPaymentMethodId, currency } = parse.data;
    
    const creditBalanceService = CreditBalanceService.getInstance();
    
    const withdrawal = await creditBalanceService.processWithdrawal({
      userId: String(user._id),
      amount,
      withdrawalMethod,
      originalPaymentMethodId,
      currency
    });

    logger.info(`Withdrawal processed for user ${user._id}: ${amount} ${currency || 'DOP'}`);

    return res.json({
      success: true,
      data: {
        withdrawalId: String(withdrawal._id),
        amount,
        currency: currency || 'DOP',
        method: withdrawalMethod,
        status: withdrawal.status,
        message: 'Withdrawal request processed successfully'
      }
    });

  } catch (error) {
    logger.error('Error processing withdrawal:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });

    if (error instanceof CancellationError) {
      return res.status(error.statusCode).json({
        success: false,
        error: error.message,
        code: error.code
      });
    }

    const message = error instanceof Error ? error.message : 'Failed to process withdrawal';
    return res.status(500).json({ success: false, error: message });
  }
};

/**
 * Send cancellation notifications to relevant parties
 */
async function sendCancellationNotifications(jobId: string, cancellationType: string, initiatedBy: string) {
  try {
    const job = await Job.findById(jobId).populate('ownerId assignedOperatorId');
    if (!job) return;

    const clientId = String(job.ownerId);
    const operatorId = job.assignedOperatorId ? String(job.assignedOperatorId) : null;

    // Determine notification recipients based on who initiated
    const notifyClient = initiatedBy !== clientId;
    const notifyOperator = operatorId && initiatedBy !== operatorId;

    // Send client notification
    if (notifyClient) {
      const clientToken = await getUserNotificationToken(clientId);
      if (clientToken.success && clientToken.notificationToken) {
        await sendCustomNotification(clientToken.notificationToken, {
          title: 'Job Cancelled',
          message: getClientNotificationMessage(cancellationType),
          data: { type: 'job_cancelled', jobId, cancellationType }
        });
      }
    }

    // Send operator notification
    if (notifyOperator) {
      const operatorToken = await getUserNotificationToken(operatorId!);
      if (operatorToken.success && operatorToken.notificationToken) {
        await sendCustomNotification(operatorToken.notificationToken, {
          title: 'Job Cancelled',
          message: getOperatorNotificationMessage(cancellationType),
          data: { type: 'job_cancelled', jobId, cancellationType }
        });
      }
    }

  } catch (error) {
    logger.error('Error sending cancellation notifications:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    // Don't throw - notifications are not critical for cancellation processing
  }
}

function getClientNotificationMessage(cancellationType: string): string {
  switch (cancellationType) {
    case 'operator_before_start':
      return 'The operator has cancelled your job. You have been fully refunded and can repost or reschedule.';
    case 'operator_after_arrival':
      return 'The operator has cancelled after arriving. You have been fully refunded. A penalty has been applied to the operator.';
    default:
      return 'Your job has been cancelled.';
  }
}

function getOperatorNotificationMessage(cancellationType: string): string {
  switch (cancellationType) {
    case 'client_less_than_24h':
      return 'The client has cancelled the job with less than 24h notice. You will receive compensation.';
    case 'client_after_start':
      return 'The client has cancelled the job after it started. You will receive compensation for work done.';
    default:
      return 'The job has been cancelled.';
  }
}
