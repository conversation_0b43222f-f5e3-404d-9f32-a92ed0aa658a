import { getAzulDeletionRetryService } from '../services/azulDeletionRetryService';

/**
 * Initialize and start the Azul deletion retry processor
 * Call this during application startup
 */
export function initializeAzulRetryProcessor(): void {
  try {
    const environment = process.env.NODE_ENV === 'production' ? 'production' : 'sandbox';
    const retryService = getAzulDeletionRetryService(environment as 'sandbox' | 'production');
    
    // Start the background processor
    retryService.startProcessor();
    
    console.log('Azul deletion retry processor initialized');
    
    // Handle graceful shutdown
    const gracefulShutdown = () => {
      console.log('Shutting down Azul deletion retry processor...');
      retryService.stopProcessor();
    };
    
    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);
    
  } catch (error) {
    console.error('Failed to initialize Azul deletion retry processor:', error);
  }
}

/**
 * Get retry statistics for monitoring/admin endpoints
 */
export async function getRetryProcessorStats() {
  try {
    const environment = process.env.NODE_ENV === 'production' ? 'production' : 'sandbox';
    const retryService = getAzulDeletionRetryService(environment as 'sandbox' | 'production');
    return await retryService.getRetryStats();
  } catch (error) {
    console.error('Failed to get retry processor stats:', error);
    return {
      pending: 0,
      processing: 0,
      succeeded: 0,
      failed: 0
    };
  }
}
