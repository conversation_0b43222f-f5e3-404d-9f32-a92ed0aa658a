"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const express_1 = tslib_1.__importDefault(require("express"));
const multer_1 = tslib_1.__importDefault(require("multer"));
const authentication_1 = require("../middleware/authentication");
const middleware_1 = require("../middleware");
const chat_1 = require("../controllers/chat");
const router = express_1.default.Router();
// Configure multer for file uploads
const upload = (0, multer_1.default)({
    storage: multer_1.default.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
    },
    fileFilter: (req, file, cb) => {
        // Allow only image files
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        }
        else {
            cb(new Error('Only image files are allowed'));
        }
    }
});
// Start a new chat
router.post('/start', authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(chat_1.startChat));
// Send a message in a chat
router.post('/message', authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(chat_1.sendMessage));
// The error handling middleware is now directly integrated in the upload route
// Upload an image to a chat
router.post('/upload', authentication_1.AuthenticateTokenOAuth, (req, res, next) => {
    upload.single('image')(req, res, (err) => {
        if (err) {
            if (err instanceof multer_1.default.MulterError) {
                // A multer error occurred when uploading
                console.error('Multer error:', err);
                if (err.code === 'LIMIT_FILE_SIZE') {
                    return res.status(413).json({ success: false, error: 'File too large (max 10MB)' });
                }
                if (err.code === 'LIMIT_UNEXPECTED_FILE') {
                    return res.status(400).json({
                        success: false,
                        error: `Unexpected field name. Use 'image' as the form field name`,
                        details: err.message
                    });
                }
                return res.status(400).json({ success: false, error: err.message });
            }
            else {
                // An unknown error occurred when uploading
                console.error('Upload error:', err);
                if (err.message === 'Only image files are allowed') {
                    return res.status(400).json({ success: false, error: 'Only image files are allowed' });
                }
                return res.status(500).json({ success: false, error: err.message });
            }
        }
        next();
    });
}, (0, middleware_1.tryCatch)(chat_1.uploadImage));
// Get a specific chat
router.get('/:chatId', authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(chat_1.getChat));
// Get user's chats
router.get('/', authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(chat_1.getUserChats));
exports.default = router;
