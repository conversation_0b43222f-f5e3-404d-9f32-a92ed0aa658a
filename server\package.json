{"name": "manito-be", "version": "1.0.0", "description": "", "main": "index.ts", "scripts": {"build": "tsc", "start": "node --env-file=.env ./dist/index.js", "dev": "nodemon", "nodemon": "nodemon --quiet --exec \"node --env-file=.env -r ts-node/register ./src/index.ts\""}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.857.0", "@aws-sdk/lib-storage": "^3.857.0", "@polygon.io/client-js": "^8.0.0", "@vercel/node": "^5.3.7", "api": "^6.1.3", "axios": "^1.11.0", "bcrypt": "^6.0.0", "chalk": "^5.4.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-session": "^1.18.2", "file-type": "^18.7.0", "google-auth-library": "^10.2.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "luxon": "^3.7.1", "mongoose": "^8.17.0", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "nodemailer": "^7.0.5", "openai": "^5.11.0", "passport": "^0.7.0", "passport-apple": "^2.0.2", "passport-google-oauth20": "^2.0.0", "redis": "^5.6.1", "sharp": "^0.34.3", "tslib": "^2.8.1", "uuid": "^11.1.0", "zod": "^3.25.76"}, "devDependencies": {"@types/bcrypt": "^6.0.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/luxon": "^3.7.1", "@types/mime-types": "^3.0.1", "@types/multer": "^2.0.0", "@types/node": "^24.3.0", "@types/nodemailer": "^6.4.17", "@types/passport-apple": "^2.0.3", "@types/passport-google-oauth20": "^2.0.16", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}