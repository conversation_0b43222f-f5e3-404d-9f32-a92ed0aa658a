"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const payoutSchema = new mongoose_1.default.Schema({
    jobId: { type: String, ref: 'Job', required: true, index: true },
    bidId: { type: String, ref: 'Bid', required: true, index: true },
    operatorId: { type: String, ref: 'Account', required: true, index: true },
    amount: {
        type: Number,
        required: true,
        min: 0,
        validate: {
            validator: Number.isInteger,
            message: 'amount must be integer minor units'
        }
    },
    currency: { type: String, enum: ['DOP', 'USD'], default: 'DOP', required: true },
    status: { type: String, enum: ['pending', 'processing', 'completed', 'failed'], default: 'pending', index: true },
    operatorTaxRate: { type: Number, required: true, min: 0, max: 1 },
    operatorTaxAmount: { type: Number, required: true, min: 0 },
    destinationAccount: { type: String, select: false },
    transactionRef: { type: String }
}, { timestamps: true });
payoutSchema.index({ operatorId: 1, createdAt: -1 });
// Ensure operatorTaxAmount is consistent with amount * operatorTaxRate
payoutSchema.pre('validate', function (next) {
    const doc = this;
    try {
        if (typeof doc.amount === 'number' && typeof doc.operatorTaxRate === 'number') {
            // Round to nearest minor unit
            doc.operatorTaxAmount = Math.round(doc.amount * doc.operatorTaxRate);
        }
        return next();
    }
    catch (err) {
        return next(err);
    }
});
exports.default = mongoose_1.default.model('Payout', payoutSchema);
