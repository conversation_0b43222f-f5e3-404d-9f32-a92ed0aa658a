import LottieView from 'lottie-react-native'
import React from 'react'
import { StyleSheet, View } from 'react-native'

const SplashScreenLoader = () => {

  return (
    <View style={styles.viewport}>
      <LottieView 
        source={require('@/assets/animations/manito.json')}
        style={{ height: 370, aspectRatio: 1}}
        speed={1.5}
        autoPlay
        loop={false}
        />
    </View>
  )
}

export default SplashScreenLoader


const styles = StyleSheet.create({
  viewport: {

    zIndex: 100,
    width: '100%',
    height: 300,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    },

})