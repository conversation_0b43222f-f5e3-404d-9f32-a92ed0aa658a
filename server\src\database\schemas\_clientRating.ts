import mongoose, { Document } from 'mongoose';

export interface IClientRating extends Document {
  clientId: string; // Account ID of the job owner (client)
  operatorId: string; // Account ID of the operator submitting the rating
  jobId: string; // Job ID for which this rating applies
  stars: number; // 1-5
  adjectives: {
    positive: string[];
    negative: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}

const clientRatingSchema = new mongoose.Schema<IClientRating>({
  clientId: { type: String, required: true, ref: 'Account', index: true },
  operatorId: { type: String, required: true, ref: 'Account', index: true },
  // jobId must exist but should not be unique by itself - uniqueness is enforced
  // per (jobId, operatorId) via a compound unique index below.
  jobId: { type: String, required: true, ref: 'Job' },
  stars: { type: Number, required: true, min: 1, max: 5 },
  adjectives: {
    positive: { type: [String], default: [] },
    negative: { type: [String], default: [] },
  },
}, { timestamps: true });

clientRatingSchema.index({ clientId: 1, createdAt: -1 });
// Enforce one rating per job per operator
clientRatingSchema.index({ jobId: 1, operatorId: 1 }, { unique: true });

export default mongoose.model<IClientRating>('ClientRating', clientRatingSchema);

