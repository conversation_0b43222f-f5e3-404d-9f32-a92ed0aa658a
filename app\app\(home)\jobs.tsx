import React, { useEffect, useState } from 'react'
import { Image, RefreshControl, ScrollView, StyleSheet, Text, View } from 'react-native'

import Page from '@/components/templates/Page'
import Header from '@/components/templates/Header'
import { viewJobs } from '@/services/api'
import { InterfaceJobCard } from '@/types/jobcard'
import JobCard from '@/components/organisms/job/job_card'
import NoResults from '@/components/molecules/NoResults'
import ScreenSpinnerLoader from '@/components/atoms/loaders/ScreenSpinner'

const Index = () => {
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [jobs, setJobs] = useState([])

  const handleFetchJobs = async () => {
    setIsRefreshing(true)
    const call = await viewJobs()

    if (!call.success) return
    console.log(call.jobs)
    setJobs(call.jobs)
    setIsLoading(false)
    setIsRefreshing(false)
  }
  

  useEffect(() => {
    handleFetchJobs()
  }, [])


  

  return (

    <Page noPaddingTop noBottomBar alignItems='center' justifyContent='space-between' page='fridge'>

      <Header text='Trabajos' burgerMenu />

      {
        isLoading ? 
        <ScreenSpinnerLoader noBackground/>
        :
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentInset={{top: 90}}
          refreshControl={
            <RefreshControl
              tintColor={'#000000'}
              colors={['#000000']}
              refreshing={isRefreshing}
              onRefresh={()=>{
                handleFetchJobs()
              }}
            />
          }
        >
          {
            jobs.length > 0 ?
              jobs.map((job: InterfaceJobCard) => (
                <JobCard key={job._id} job={job} />
              ))
              :
              <NoResults
                title='No tienes trabajos'
                subtitle='Habla con el agente para agendar tu primera cita'
              />
          }
        </ScrollView>
      }
    </Page>
  )
}

export default Index

const styles = StyleSheet.create({
  

})