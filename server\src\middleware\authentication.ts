import { NextFunction, Request, Response } from "express";
import { Account } from "../database/schemas";
import { AuthenticatedUser } from "../types/express";
import jwt, { JwtPayload } from 'jsonwebtoken'

// Escape regex metacharacters to avoid unintended behavior when building RegExp from user input
function escapeRegex(input: string): string {
  return input.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}


export async function AuthenticateToken(req: Request) {
  const header = req.headers.authorization ?? "";
  const [scheme, token] = header.split(" ");
  if (scheme !== "Bearer" || !token || token === "null") {
    return { success: false, account: null };
  }

  const jwtSecret = process.env.JWT_SECRET;  if (!jwtSecret) {
    throw new Error('JWT_SECRET is not defined');
  }

  try {
    const decoded = jwt.verify(token, jwtSecret) as JwtPayload & { sub: string };
    const account = await Account.findById(decoded.sub)
      .select("-password")  // include other fields that downstream expects
      .lean();

    if (!account) {      return {
        success: false,
        account: null,
      };
    }

    return {
      success: true,
      account: account,
    };
  } catch (error) {
    return {
      success: false,
      account: null,
    };
  }
}


export const AuthenticateTokenOAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  if (!req.headers.authorization) {
    res.status(401).json({ success: false, error: 'No authorization header provided' });
    return;
  }

  const authParts = req.headers.authorization.split(' ');
  if (authParts[0] !== 'Bearer') {
    res.status(401).json({ success: false, error: 'Invalid token format' });
    return;
  }

  if (authParts[1] === 'null' || !authParts[1]) {
    res.status(401).json({ success: false, error: 'Null token provided' });
    return;
  }

  const token = authParts[1];

  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    console.error('JWT_SECRET is not defined in environment variables');
    next(new Error('JWT_SECRET is not defined'));
    return;
  }

  try {
    const decoded = jwt.verify(token, jwtSecret) as JwtPayload & { sub?: string; email?: string };
    // Look up by subject (account id) from token; email used only for sanity/logging
    const sub = String(decoded.sub || '');
    let account = sub ? await Account.findById(sub).select('-password -settings -contacts').lean() : null;
    if (!account && decoded.email) {
      // Fallback: find by email if sub was missing (legacy tokens)
      const escapedEmail = escapeRegex(String(decoded.email || ''));
      const emailRegex = new RegExp(`^${escapedEmail}$`, 'i');
      account = await Account.findOne({ email: { $regex: emailRegex } })
        .select('-password -settings -contacts')
        .lean();
    }

    if (!account) {
      res.status(401).json({ success: false, error: 'Invalid credentials' });
      return;
    }

    // Convert MongoDB document to AuthenticatedUser format
    const acc: any = account;
    // Derive a single role, preferring explicit admin/operator, else fallback to user.user.type
    const derivedRole = acc.booleans?.isAdmin ? 'admin'
      : (acc.booleans?.isOperator || acc.user?.type === 'operator') ? 'operator'
      : (acc.user?.type || 'user');

    req.user = {
      _id: String(acc._id),
      email: acc.email,
      role: derivedRole,
      user: acc.user,
      contacts: acc.contacts,
      settings: acc.settings,
      finances: acc.finances,
      booleans: acc.booleans, // deprecated but kept for backward compatibility
      notifications: acc.notifications,
      createdAt: acc.createdAt,
    } as AuthenticatedUser;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    
    // Handle JWT-specific errors with 401 status
    if (error instanceof Error) {
      if (error.name === 'TokenExpiredError') {
        res.status(401).json({ success: false, error: 'Token expired' });
        return;
      }
      if (error.name === 'JsonWebTokenError') {
        res.status(401).json({ success: false, error: 'Invalid token' });
        return;
      }
      if (error.name === 'NotBeforeError') {
        res.status(401).json({ success: false, error: 'Token not active' });
        return;
      }
    }
    
    // For other errors, return 500 (server error)
    if (error instanceof Error) {
      res.status(500).json({ success: false, error: `Authentication Error: ${error.message}` });
    } else {
      res.status(500).json({ success: false, error: 'Authentication Error' });
    }
  }
};

export function signToken(email: string, accountId: string) {
  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    throw new Error('JWT_SECRET is not defined');
  }
  return jwt.sign(
    { sub: accountId, email: email.toLowerCase() },
    jwtSecret,
    { expiresIn: '7d' }
  );
}

export function signTokenOAuth(email: string, accountId: string) {
  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    throw new Error('JWT_SECRET is not defined');
  }
  return jwt.sign(
  { sub: accountId, email: String(email || '').toLowerCase() },
    jwtSecret,
    { expiresIn: '7d' }
  );
}

