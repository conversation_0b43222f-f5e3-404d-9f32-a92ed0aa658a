import { authenticateOAuth } from '@/services/api';
import { useRouter } from 'expo-router';
import { Audio } from 'expo-av';
import React, { createContext, useState } from 'react';

import { useNotifications } from './NotificationsContext';
import { LoginChime } from '@/utils/Sounds';

interface UserDataModel {
  email: string;
  password: string;
  user: {
    name: string;
    surname: string;
    username: string;
    profile_picture: string;
    birthdate: Date | string; // You can adjust the type here
    type: 'user' | 'admin' | 'operator';
  };
  contacts: {
    phone: string;
    address: string;
  };
  settings: {
    currency: string;
    preferred_language: string;
    timezone: string;
  };
  finances: {
    billing_address: string;
  };
  booleans: {
    isVerified: boolean;
    isAdmin: boolean;
  };
  tokens: {
    verificationToken?: string;
    passwordResetToken?: string;
  };
  notifications: {
    expo_push_token?: string;
  };
  createdAt?: Date
}


export const AuthContext = createContext({
  role: null as string | null,
  userData: null as UserDataModel | null,
  handleInitialAuthentication: async () => { },
  sessionAuthentication: async () => { }
});

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const router = useRouter()
  const { initializeNotifications } = useNotifications()


  const [role, setRole] = useState(null)
  const [userData, setUserData] = useState(null)

  


  const handleInitialAuthentication = async () => {
    const call = await authenticateOAuth();
   
    
    if (!call.success) {

      router.replace('/login')

    } else {
      
      //Save user data
      setUserData(call.data)
      setRole(call.data.user.type)
      


      //Initialize notifications
      const initNotifications = initializeNotifications()     //maybe await?
      

      //Go to dashboard 
      router.replace('/(home)')
      await LoginChime()
    }

  };
  const sessionAuthentication = async () => {
    const call = await authenticateOAuth();
    if (!call.success) {
      router.replace('/login')
      return
    }

    setUserData(call.data)
    setRole(call.data.user.type)

    const initNotifications = await initializeNotifications()
  };



  return (
    <AuthContext.Provider value={{ handleInitialAuthentication, sessionAuthentication, role, userData }}>
      {children}
    </AuthContext.Provider>
  );
};