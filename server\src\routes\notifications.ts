import express from "express";
const router = express.Router();

import { Request, Response } from "express";
import { tryCatch } from "../middleware";
import { Account } from "../database/schemas";
import { AuthenticateTokenOAuth } from "../middleware/authentication";
import { getUserNotificationToken, sendCustomNotification, sendDefaultNotification } from "../controllers/notifications";
import { getUserNotifications, markNotificationAsRead } from "../services/jobNotificationService";
import { getAuthenticatedUser } from "../utils/auth";

router.route("/save-token").post(AuthenticateTokenOAuth, tryCatch(async (req: Request, res: Response): Promise<any> => {
    const { token } = req.body;
    if (!token) {
        return res
            .status(400)
            .json({ success: false, message: "Token is required" });
    }

    const account = await Account.findById((req.user as any)?._id);
    if (!account) {
        return res
            .status(404)
            .json({ success: false, message: "Account not found" });
    }

    account.notifications.expo_push_token = token;
    await account.save();

    res.status(200).json({
        success: true,
        message: "Notification token retrieved",
    });
}));

// Get user notifications
router.route("/").get(AuthenticateTokenOAuth, tryCatch(async (req: Request, res: Response): Promise<any> => {
    const user = getAuthenticatedUser(req);
    const userId = user?._id;
    const { page = 1, limit = 20, unreadOnly = 'false' } = req.query;

    if (!userId) {
        return res.status(401).json({ success: false, error: 'Unauthorized' });
    }

    const result = await getUserNotifications(
        userId.toString(),
        Number(page),
        Number(limit),
        unreadOnly === 'true'
    );

    if (!result) {
        return res.status(500).json({ success: false, error: 'Failed to get notifications' });
    }

    res.json({
        success: true,
        ...result
    });
}));

// Mark notification as read
router.route("/:notificationId/read").post(AuthenticateTokenOAuth, tryCatch(async (req: Request, res: Response): Promise<any> => {
    const { notificationId } = req.params;
    
    const success = await markNotificationAsRead(notificationId);
    
    if (!success) {
        return res.status(404).json({ success: false, error: 'Notification not found' });
    }

    res.json({
        success: true,
        message: 'Notification marked as read'
    });
}));

// Get unread notification count
router.route("/unread-count").get(AuthenticateTokenOAuth, tryCatch(async (req: Request, res: Response): Promise<any> => {
    const user = getAuthenticatedUser(req);
    const userId = user?._id;

    if (!userId) {
        return res.status(401).json({ success: false, error: 'Unauthorized' });
    }

    const { NotificationLog } = await import('../database/schemas');
    const unreadCount = await NotificationLog.countDocuments({ 
        recipientId: userId.toString(), 
        isRead: false 
    });

    res.json({
        success: true,
        unreadCount
    });
}));

export default router
