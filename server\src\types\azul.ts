// Azul API Types and Interfaces

export interface AzulCredentials {
  merchantId: string | undefined;
  auth1: string | undefined;
  auth2: string | undefined;
  primaryEndpoint: string | undefined;
  secondaryEndpoint?: string | undefined;
}

export interface AzulEnvironment {
  sandbox: AzulCredentials;
  production: AzulCredentials;
}

// Base Request Interface
export interface AzulBaseRequest {
  MerchantId: string;
  MerchantName?: string;
  MerchantType?: string;
}

// Data Vault Requests
export interface ProcessDataVaultCreateRequest extends AzulBaseRequest {
  TrxType: 'CREATE';
  CardNumber: string;
  CVC?: string;
  Expiration: string; // MMYY format
  CardHolderName?: string;
}

export interface ProcessDataVaultDeleteRequest extends AzulBaseRequest {
  TrxType: 'DELETE';
  DataVaultToken: string;
}

export type ProcessDataVaultRequest = ProcessDataVaultCreateRequest | ProcessDataVaultDeleteRequest;

// Data Vault Response
export interface ProcessDataVaultResponse {
  ResponseCode: string;
  ResponseMessage: string;
  DataVaultToken?: string;
  CardNumber?: string; // Masked PAN
  HasCVV?: boolean;
  CardType?: string;
  Expiration?: string;
  ErrorDescription?: string;
}

// Payment Request
// Payment Request - use a discriminated union so exactly one payment source is provided
export interface ProcessPaymentBase extends AzulBaseRequest {
  AzulOrderId: string;
  Amount: number;
  ITBIS?: number;
  CurrencyCode: string; // 'DOP', 'USD'
  ECommerceUrl?: string;

  // Transaction type
  TrxType: 'Sale' | 'Hold';

  // 3DS Settings
  ForceNo3DS?: 0 | 1;
  ThreeDSAuth?: ThreeDSAuthRequest;

  // Additional fields
  PosInputMode?: string;
  OrderNumber?: string;
  CustomOrderId?: string;
}

// Token-based payment (Data Vault)
export interface TokenPaymentRequest extends ProcessPaymentBase {
  paymentMethod: 'token';
  DataVaultToken: string;
  SaveToDataVault?: 0 | 1;
}

// Card-based payment (raw card fields) - only allowed when server is PCI compliant
export interface CardPaymentRequest extends ProcessPaymentBase {
  paymentMethod: 'card';
  CardNumber: string;
  CVC?: string;
  Expiration: string; // MMYY
  CardHolderName?: string;
}

export type ProcessPaymentRequest = TokenPaymentRequest | CardPaymentRequest;

// 3DS Authentication Request
export interface ThreeDSAuthRequest {
  ThreeDS2: 1;
  BrowserInfo: {
    AcceptHeader: string;
    UserAgent: string;
    JavaEnabled: boolean;
    Language: string;
    ColorDepth: number;
    ScreenHeight: number;
    ScreenWidth: number;
    TimeZoneOffset: number;
    JavascriptEnabled: boolean;
  };
  CardHolderInfo: {
    CardHolderName?: string;
    Email?: string;
    BillingAddress?: {
      Line1?: string;
      Line2?: string;
      City?: string;
      State?: string;
      PostalCode?: string;
      CountryCode?: string;
    };
    ShippingAddress?: {
      Line1?: string;
      Line2?: string;
      City?: string;
      State?: string;
      PostalCode?: string;
      CountryCode?: string;
    };
    WorkPhoneNumber?: string;
    HomePhoneNumber?: string;
    MobilePhoneNumber?: string;
  };
  PurchaseInfo?: {
    PurchaseAmount: number;
    PurchaseCurrency: string;
    PurchaseExponent: number;
    PurchaseDate: string; // YYYYMMDDHHMMSS
  };
  MerchantInfo: {
    MerchantName: string;
    MerchantCategoryCode?: string;
    AcquirerBIN?: string;
    AcquirerMerchantId?: string;
  };
  RequestorInfo?: {
    RequestorId?: string;
    RequestorName?: string;
    RequestorUrl?: string;
  };
  SDKInfo?: {
    AppId?: string;
    EphemeralPublicKey?: string;
    MaxTimeout?: number;
    ReferenceNumber?: string;
    TransactionId?: string;
  };
  // URLs for 3DS flow
  TermUrl: string;
  MethodNotificationUrl: string;
}

// Payment Response
export interface ProcessPaymentResponse {
  ResponseCode: string;
  IsoCode: string;
  ResponseMessage: string;
  ErrorDescription?: string;

  // Transaction details
  AzulOrderId: string;
  AuthorizationCode?: string;
  RRN?: string;
  OriginalAmount?: number;
  LoyaltyPoints?: number;

  // 3DS Response
  ThreeDSMethod?: {
    MethodForm?: string;
    ThreeDSServerTransID?: string;
  };
  ThreeDSChallenge?: {
    ACSTransactionId?: string;
    ChallengeForm?: string;
    RedirectPostUrl?: string;
    ThreeDSServerTransID?: string;
  };

  // Data Vault
  DataVaultToken?: string;
  SaveToDataVault?: 0 | 1;
}

// 3DS Method Request
export interface ProcessThreeDSMethodRequest extends AzulBaseRequest {
  AzulOrderId: string;
  ThreeDSServerTransID: string;
  methodNotificationStatus: 'RECEIVED' | 'EXPECTED_BUT_NOT_RECEIVED' | 'NOT_EXPECTED';
}

// 3DS Method Response
export interface ProcessThreeDSMethodResponse {
  ResponseCode: string;
  IsoCode: string;
  ResponseMessage: string;
  ErrorDescription?: string;

  AzulOrderId: string;
  AuthorizationCode?: string;
  RRN?: string;
  OriginalAmount?: number;

  // If challenge is required
  ThreeDSChallenge?: {
    ACSTransactionId?: string;
    ChallengeForm?: string;
    RedirectPostUrl?: string;
    ThreeDSServerTransID?: string;
  };
}

// 3DS Challenge Request
export interface ProcessThreeDSChallengeRequest extends AzulBaseRequest {
  AzulOrderId: string;
  ThreeDSServerTransID: string;
  CRes: string; // Challenge response from ACS
}

// 3DS Challenge Response
export interface ProcessThreeDSChallengeResponse {
  ResponseCode: string;
  IsoCode: string;
  ResponseMessage: string;
  ErrorDescription?: string;

  AzulOrderId: string;
  AuthorizationCode?: string;
  RRN?: string;
  OriginalAmount?: number;
}

// Refund Request
export interface RefundRequest extends AzulBaseRequest {
  AzulOrderId: string;
  OriginalAzulOrderId: string;
  Amount: number;
  CurrencyCode: string;
}

// Refund Response
export interface RefundResponse {
  ResponseCode: string;
  IsoCode: string;
  ResponseMessage: string;
  ErrorDescription?: string;

  AzulOrderId: string;
  AuthorizationCode?: string;
  RRN?: string;
  OriginalAmount?: number;
}

// Post Processing Request (Capture/Void)
export type ProcessPostRequest =
  | (AzulBaseRequest & {
      AzulOrderId: string;
      TrxType: 'Capture';
      Amount: number; // required for captures (can be partial)
    })
  | (AzulBaseRequest & {
      AzulOrderId: string;
      TrxType: 'Void';
      Amount?: never; // explicitly forbid Amount for voids
    });

// Post Processing Response  
export interface ProcessPostResponse {
  ResponseCode: string;
  IsoCode: string;
  ResponseMessage: string;
  ErrorDescription?: string;

  AzulOrderId: string;
  AuthorizationCode?: string;
  RRN?: string;
  OriginalAmount?: number;
}

// Method Notification (from ACS)
export interface MethodNotificationData {
  threeDSServerTransID: string;
  threeDSMethodData?: string;
}

// Client-side payment method creation request
export type CreatePaymentMethodRequest =
  | {
      // Option 1: Client-provided token (preferred)
      azulToken: string;
      setAsFavorite?: boolean;
    }
  | {
      // Option 2: Card details (only if PCI compliant)
      cardNumber: string;
      cvc?: string;
      expiryMonth: number;
      expiryYear: number;
      cardHolderName?: string;
      setAsFavorite?: boolean;
    };
// Payment method response
export interface PaymentMethodResponse {
  id: string;
  last4: string;
  brand: string;
  exp_month: number;
  exp_year: number;
  is_favorite: boolean;
  has_cvv: boolean;
  createdAt: string;
}

// Charge request
export interface ChargePaymentMethodRequest {
  amount: number;
  currency: string;
  jobId?: string;
  bidId?: string;
  type: 'Sale' | 'Hold';
  idempotencyKey: string;

  // 3DS browser info (required for 3DS)
  browserInfo?: {
    acceptHeader: string;
    userAgent: string;
    javaEnabled: boolean;
    language: string;
    colorDepth: number;
    screenHeight: number;
    screenWidth: number;
    timeZoneOffset: number;
    javascriptEnabled: boolean;
  };

  // Customer info for 3DS
  customerInfo?: {
    email?: string;
    phone?: string;
    billingAddress?: {
      line1?: string;
      line2?: string;
      city?: string;
      state?: string;
      postalCode?: string;
      countryCode?: string;
    };
  };
}

// Charge response
export interface ChargePaymentMethodResponse {
  transactionId: string;
  status: 'successful' | 'failed' | 'requires_action';

  // If successful
  authCode?: string;
  rrn?: string;

  // If requires action (3DS)
  threeDSAction?: {
    type: 'method' | 'challenge';
    methodForm?: string;
    challengeUrl?: string;
    challengeForm?: string;
  };

  // Error details
  error?: {
    code: string;
    message: string;
  };
}

// Azul Error Codes
export const AZUL_ERROR_CODES = {
  // Success
  '00': 'Approved',

  // 3DS Codes
  '3D': '3D Secure Challenge Required',
  '3D2METHOD': '3D Secure 2.0 Method Required',

  // Decline Codes
  '05': 'Declined',
  '51': 'Insufficient Funds',
  '54': 'Expired Card',
  '57': 'Transaction Not Permitted',
  '61': 'Exceeds Withdrawal Amount Limit',
  '65': 'Exceeds Withdrawal Frequency Limit',

  // Error Codes
  '96': 'System Error',
  '30': 'Format Error',
  '12': 'Invalid Transaction',
  '13': 'Invalid Amount',
  '14': 'Invalid Card Number',
  '15': 'Invalid Issuer',
} as const;

export type AzulErrorCode = keyof typeof AZUL_ERROR_CODES;
