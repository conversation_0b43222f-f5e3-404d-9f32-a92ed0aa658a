"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const express_1 = tslib_1.__importDefault(require("express"));
const multer_1 = tslib_1.__importDefault(require("multer"));
const middleware_1 = require("../middleware");
const authentication_1 = require("../middleware/authentication");
const roleAuth_1 = require("../middleware/roleAuth");
const operatorApplication_1 = require("../controllers/operatorApplication");
const router = express_1.default.Router();
const upload = (0, multer_1.default)({ storage: multer_1.default.memoryStorage(), limits: { fileSize: 10 * 1024 * 1024, files: 12 } });
// User submits application
router.post('/apply', authentication_1.AuthenticateTokenOAuth, 
// accept multiple files under field name 'documents' and optional body 'documentsMeta'
upload.array('documents', 12), (0, middleware_1.tryCatch)(operatorApplication_1.submitOperatorApplication));
// Public: return required document categories for a set of categories
router.get('/document-requirements', (0, middleware_1.tryCatch)(operatorApplication_1.getApplicationDocumentRequirements));
// Admin lists applications
router.get('/applications', authentication_1.AuthenticateTokenOAuth, roleAuth_1.requireAdmin, (0, middleware_1.tryCatch)(operatorApplication_1.listOperatorApplications));
// Admin reviews
router.post('/applications/:applicationId/approve', authentication_1.AuthenticateTokenOAuth, roleAuth_1.requireAdmin, (0, middleware_1.tryCatch)(operatorApplication_1.approveOperatorApplication));
router.post('/applications/:applicationId/reject', authentication_1.AuthenticateTokenOAuth, roleAuth_1.requireAdmin, (0, middleware_1.tryCatch)(operatorApplication_1.rejectOperatorApplication));
exports.default = router;
