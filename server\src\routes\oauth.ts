import { Request, Response } from "express";
import { Account } from "../database/schemas";
import { AuthenticateTokenOAuth, signTokenOAuth, tryCatch } from "../middleware";
import express from "express";
import { verifyAppleIdentityToken } from "../controllers/oauth";
import { generateRandomNumberString, hashPassword } from "../utils";
const router = express.Router();


router.post("/login/apple", async (req: Request, res: Response): Promise<any> => {
  try {
    const { identityToken } = req.body;

    if (!identityToken) {
      return res.status(400).json({ success: false, error: "Missing identityToken" });
    }

    const payload: any = await verifyAppleIdentityToken(identityToken);
    const appleId = payload.sub;
    const emailApple = payload.email;

    let account = await Account.findOne({ email: emailApple, password: hashPassword(appleId) });

    if (!account) {
      console.log("Creating new account for Apple user:", emailApple);
      account = new Account({
        email: emailApple,
        password: hashPassword(appleId), // no password required for Apple users or use appleId
        user: {
          name: "",
          surname: "",
          username: `apple_${appleId.slice(-6)}`,
          profile_picture: "",
          birthdate: "",
          type: "user",
        },
        booleans: {
          isVerified: true,
          isAdmin: false,
        },
        // Other fields will fall back to schema defaults
      });

      await account.save();
    }

  const token = signTokenOAuth(emailApple, String(account._id));

    res.status(200).json({ success: true, token, account });
  } catch (error) {
    console.error("Apple login error:", error);
    res.status(401).json({ success: false, error: "Invalid or expired identity token" });
  }
});
router.post("/login/google", async (req: Request, res: Response): Promise<any> => {

  try {
    const { id, email, name, surname, photo } = req.body;


    let account = await Account.findOne({ email: email, password: hashPassword(id) });
    if (!account) {
      console.log("Creating new account for Google user:", email);
      account = new Account({
        email: email,
        password: hashPassword(id), // no password required for Apple users or use appleId
        user: {
          name: name ?? "",
          surname: surname ?? "",
          username: `${name}-${generateRandomNumberString(4)}`,
          profile_picture: photo,
          birthdate: "",
          type: "user",
        },
        booleans: {
          isVerified: true,
          isAdmin: false,
        },
        // Other fields will fall back to schema defaults
      });

      await account.save();
    }

  const token = signTokenOAuth(email, String(account._id));

    res.status(200).json({ success: true, token, account });
  } catch (error) {
    console.error("Google login error:", error);
    res.status(401).json({ success: false, error: "Invalid or expired identity token" });
  }
});

// Development only route - authenticates first user in database
router.post("/dev/authenticate", async (req: Request, res: Response): Promise<any> => {
  try {
    // Find the first user in the database
    const firstUser = await Account.findOne();
    
    if (!firstUser) {
      return res.status(404).json({ 
        success: false, 
        error: "No users found in database" 
      });
    }

    // Generate token for the first user
  const token = signTokenOAuth(firstUser.email, String(firstUser._id));
  // Ensure this endpoint is only available in development
  if (process.env.NODE_ENV !== 'development') {
    return res.status(404).json({ 
      success: false, 
      error: "Endpoint not found" 
    });
  }

    res.status(200).json({ 
      success: true, 
      message: "Dev authentication successful", 
      token, 
      account: firstUser 
    });
  } catch (error) {
    console.error("Dev authentication error:", error);
    res.status(500).json({ 
      success: false, 
      error: "Internal server error during dev authentication" 
    });
  }
});
router.route("/authenticate").get(AuthenticateTokenOAuth, tryCatch(async (req: Request, res: Response): Promise<any> => {

  if (!req.user) {
    return res
      .status(401)
      .json({ success: false, message: "Auth user not found" });
  }

  res
    .status(200)
    .json({ success: true, message: "Authorized Access", data: req.user});
}));



router.route("/update/account").post(AuthenticateTokenOAuth, tryCatch(async (req: Request, res: Response): Promise<any> => {

  if (!req.user) {
    return res
      .status(400)
      .json({ success: false, message: "Auth user not found" });
  }

  const { name, surname, username } = req.body;
  const userId = req.user._id;

  // Basic normalization (adjust to your rules)
  let normalized = {
    name: typeof name === 'string' ? name.trim() : undefined,
    surname: typeof surname === 'string' ? surname.trim() : undefined,
    username: typeof username === 'string' ? username.trim().toLowerCase() : undefined,
  };

  // Treat empty strings as no-ops
  if (normalized.name === '') normalized.name = undefined;
  if (normalized.surname === '') normalized.surname = undefined;
  if (normalized.username === '') normalized.username = undefined;

  // Basic username format: 3-30 chars, alnum plus . _ -
  if (
    normalized.username !== undefined &&
    !/^[a-z0-9._-]{3,30}$/.test(normalized.username)
  ) {
    return res.status(400).json({ success: false, message: "Invalid username format" });
  }
  const $set: Record<string, unknown> = {};
  if (normalized.name !== undefined) $set['user.name'] = normalized.name;
  if (normalized.surname !== undefined) $set['user.surname'] = normalized.surname;
  if (normalized.username !== undefined) $set['user.username'] = normalized.username;

  const updated = await Account.findByIdAndUpdate(
    userId,
    { $set },
    { new: true, runValidators: true, select: 'user' }
  );

  if (!updated) {
    return res.status(404).json({ success: false, message: "User not found" });
  }
  res
    .status(200)
    .json({ success: true });
}));

export default router;
