import { Request, Response } from 'express';
import mongoose from 'mongoose';
import { z } from 'zod';
import { DateTime } from 'luxon';
import { getAuthenticatedUser } from '../utils/auth';
import { Account, Bid, Job, PaymentTransaction, OperatorProfile, Payout, Tax } from '../database/schemas';
import { PaymentProcessorService } from '../services/paymentProcessorService';
import { parseJobDateTime as parseJobDateTimeUtil } from '../utils/parseJobDateTime';
import { getUserNotificationToken, sendCustomNotification } from './notifications';

// Local types
interface JobPaymentFilter {
  userId: string;
  idempotencyKey: string;
  paymentMethodId?: string;
}

// Constants (amounts in minor units)
const COMMISSION_AMOUNT = 500; // ₫500 fixed
const COMMISSION_TAX_RATE = 0.18; // 18%
const OPERATOR_TAX_RATE = 0.02; // 2%
const MAX_BID_AMOUNT = 1_000_000_000; // upper bound in minor units (e.g. 1 billion)

const environment = process.env.NODE_ENV === 'production' ? 'production' : 'sandbox';
// Async lazy payment processor to avoid initializing external clients at module import time (helps tests) and to avoid race conditions
let paymentProcessorService: PaymentProcessorService | null = null;
let paymentProcessorInitializing: Promise<PaymentProcessorService> | null = null;
async function getPaymentProcessor(): Promise<PaymentProcessorService> {
  if (paymentProcessorService) return paymentProcessorService;
  if (paymentProcessorInitializing) return paymentProcessorInitializing;

  paymentProcessorInitializing = (async () => {
    try {
      const instance = new PaymentProcessorService(environment);
      paymentProcessorService = instance;
      return instance;
    } catch (err) {
      // Clear the initializing promise so retries are possible
      paymentProcessorInitializing = null;
      const msg = err instanceof Error ? err.message : String(err);
      throw new Error(`Failed to initialize PaymentProcessorService: ${msg}`);
    }
  })();

  return paymentProcessorInitializing;
}

// Zod schemas
const processEscrowSchema = z.object({
  paymentMethodId: z.string(),
  idempotencyKey: z.string().uuid(),
});

const completeJobSchema = z.object({
  transactionId: z.string().optional(),
});

const cancelJobSchema = z.object({
  reason: z.string().optional(),
});

export const processEscrow = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (!user) return res.status(401).json({ success: false, error: 'Authentication required' });

    const { jobId } = req.params;
    const parse = processEscrowSchema.safeParse(req.body);
    if (!parse.success) {
      return res.status(400).json({ success: false, error: 'Invalid request data', details: parse.error.errors });
    }
    const { paymentMethodId, idempotencyKey } = parse.data;

    if (!mongoose.Types.ObjectId.isValid(jobId)) {
      return res.status(400).json({ success: false, error: 'Invalid jobId' });
    }

    const job = await Job.findById(jobId);
    if (!job) return res.status(404).json({ success: false, error: 'Job not found' });
    if (job.ownerId !== String(user._id)) return res.status(403).json({ success: false, error: 'Access denied' });
    if (!job.acceptedBidId) return res.status(400).json({ success: false, error: 'No accepted bid for this job' });

    const bid = await Bid.findById(job.acceptedBidId);
    if (!bid) return res.status(404).json({ success: false, error: 'Accepted bid not found' });

    // Validate bid.amount: must exist and be coercible to a finite number
    if (typeof bid !== 'object' || bid === null || !Object.prototype.hasOwnProperty.call(bid, 'amount')) {
      return res.status(400).json({ success: false, error: 'Invalid bid amount' });
    }
    const rawBidValue = (bid as any).amount;
    // Accept numeric strings (trim) or numbers. Coerce safely to Number and ensure it's finite.
    const normalized = typeof rawBidValue === 'string' ? rawBidValue.trim() : rawBidValue;
    const coercedAmount = Number(normalized);
    if (!Number.isFinite(coercedAmount)) {
      return res.status(400).json({ success: false, error: 'Invalid bid amount' });
    }
    // The codebase uses amounts in minor units; assume incoming bid.amount is minor units.
    // Round to integer minor units and validate bounds
    const validatedBidAmount = Math.round(coercedAmount);
    if (!Number.isFinite(validatedBidAmount) || validatedBidAmount <= 0 || validatedBidAmount > MAX_BID_AMOUNT) {
      return res.status(400).json({ success: false, error: 'Invalid bid amount' });
    }

  // Atomic idempotency: claim or return existing transaction by idempotencyKey + userId
  const upsertFilter: JobPaymentFilter = { userId: String(user._id), idempotencyKey };
  // Optionally scope to paymentMethodId to avoid cross-method collisions
  if (paymentMethodId) upsertFilter.paymentMethodId = paymentMethodId;

    const setOnInsert: Record<string, any> = {
      jobId: String(job._id),
      bidId: String(bid._id),
      userId: String(user._id),
      paymentMethodId: paymentMethodId,
      // amount stored as total to charge (bid + commission + commission tax)
      amount: validatedBidAmount + COMMISSION_AMOUNT + Math.round(COMMISSION_AMOUNT * COMMISSION_TAX_RATE),
      currency: 'DOP',
      status: 'initiated',
      type: 'Hold',
      idempotencyKey
    };

    const upsertResult = await PaymentTransaction.findOneAndUpdate(
      upsertFilter,
      { $setOnInsert: setOnInsert },
      { new: true, upsert: true, rawResult: true }
    ).exec();

    // If an existing document was returned (not inserted now), check its status and return existing info
    const raw = upsertResult as any;
    if (raw && raw.lastErrorObject && raw.lastErrorObject.updatedExisting) {
      const existingEscrow = raw.value;
      if (existingEscrow && ['escrow', 'successful'].includes(existingEscrow.status)) {
        return res.json({ success: true, data: { transactionId: String((existingEscrow as any)._id), status: existingEscrow.status, amounts: { bid: bid.amount, commission: existingEscrow.commissionAmount || 0, commissionTax: existingEscrow.commissionTaxAmount || 0, total: existingEscrow.amount } } });
      }
      // If existing but not in escrow/successful, proceed to attempt charging but do not create duplicate tx
    }

    // Compute totals
    const commission = COMMISSION_AMOUNT;
    const commissionTax = Math.round(commission * COMMISSION_TAX_RATE);
    const totalCharge = validatedBidAmount + commission + commissionTax;

    // Charge customer with Hold (escrow)
    const _pp = await getPaymentProcessor();
    const charge = await _pp.chargePaymentMethod(String(user._id), paymentMethodId, {
      amount: totalCharge,
      currency: 'DOP',
      type: 'Hold',
      jobId: String(job._id),
      bidId: String(bid._id),
      idempotencyKey,
    });

    // Update transaction with commission breakdown and create tax record atomically
    const session = await mongoose.startSession();
    let tx: any = null;
    try {
      await session.withTransaction(async () => {
        tx = await PaymentTransaction.findOneAndUpdate(
          { _id: charge.transactionId },
          { $set: { commissionAmount: commission, commissionTaxAmount: commissionTax, status: 'escrow' } },
          { new: true, session }
        );

        if (!tx) {
          throw new Error('Transaction not found when updating commission');
        }

        // Create tax record within the same transaction
        const taxRecord = new Tax({
          jobId: String(job._id),
          bidId: String(bid._id),
          subjectType: 'commission',
          rate: COMMISSION_TAX_RATE,
          baseAmount: commission,
          taxAmount: commissionTax,
          currency: 'DOP',
          metadata: { reason: 'Commission tax on escrow' }
        });

        await taxRecord.save({ session });
      });
    } catch (err) {
      console.error('Failed to update transaction and create tax record atomically:', err);
      // Rethrow to outer catch which returns 500
      throw err;
    } finally {
      await session.endSession();
    }

    // Notification to customer
    const token = await getUserNotificationToken(job.ownerId);
    if (token.success && token.notificationToken) {
      await sendCustomNotification(token.notificationToken, {
        title: 'Payment Authorized (Escrow)',
        message: 'Your payment has been authorized and held in escrow.',
        data: { type: 'payment_escrowed', jobId: jobId, bidId: String(bid._id), transactionId: charge.transactionId },
        soundOn: true,
        badgeCount: 1,
      });
    }

    return res.json({ success: true, data: { transactionId: charge.transactionId, status: 'escrow', amounts: { bid: validatedBidAmount, commission, commissionTax, total: totalCharge } } });
  } catch (error) {
    console.error('Error processing escrow:', error);
    const message = error instanceof Error ? error.message : 'Failed to process escrow';
    return res.status(500).json({ success: false, error: message });
  }
};

export const completeJobAndPayout = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (!user) return res.status(401).json({ success: false, error: 'Authentication required' });

    const { jobId } = req.params;
    const parse = completeJobSchema.safeParse(req.body);
    if (!parse.success) return res.status(400).json({ success: false, error: 'Invalid request data', details: parse.error.errors });

    const job = await Job.findById(jobId);
    if (!job) return res.status(404).json({ success: false, error: 'Job not found' });
    if (job.ownerId !== String(user._id)) return res.status(403).json({ success: false, error: 'Access denied' });
    if (job.status !== 'completed') return res.status(400).json({ success: false, error: 'Job must be completed before payout' });
    if (!job.acceptedBidId || !job.assignedOperatorId) return res.status(400).json({ success: false, error: 'Missing accepted bid or operator' });

    const bid = await Bid.findById(job.acceptedBidId);
    if (!bid) return res.status(404).json({ success: false, error: 'Accepted bid not found' });

    // Idempotency: if a successful payout already exists for this job/bid, return it
    const existingPayout = await Payout.findOne({
      jobId: String(job._id),
      bidId: String(bid._id),
      operatorId: String(job.assignedOperatorId),
      status: 'completed'
    }).lean();
    if (existingPayout) {
      return res.json({
        success: true,
        data: {
          payoutId: String((existingPayout as any)._id),
          amount: existingPayout.amount,
          operatorTaxAmount: existingPayout.operatorTaxAmount
        }
      });
    }

    // Find escrow transaction
    const escrowTx = await PaymentTransaction.findOne({ jobId: String(job._id), bidId: String(bid._id), type: 'Hold', status: { $in: ['escrow', 'successful'] } });
    if (!escrowTx) return res.status(404).json({ success: false, error: 'Escrow transaction not found' });

    // Capture escrow if still in escrow status
    if (escrowTx.status === 'escrow') {
      const _pp = await getPaymentProcessor();
      await _pp.captureTransaction(String(user._id), String(escrowTx._id));
    }

    // Compute payout and operator tax
    const operatorTaxAmount = Math.round(bid.amount * OPERATOR_TAX_RATE);
    const payoutAmount = bid.amount - operatorTaxAmount;

    // Log operator tax
    await Tax.create({
      jobId: String(job._id),
      bidId: String(bid._id),
      subjectType: 'operator',
      subjectId: String(job.assignedOperatorId),
      rate: OPERATOR_TAX_RATE,
      baseAmount: bid.amount,
      taxAmount: operatorTaxAmount,
      currency: escrowTx.currency,
      metadata: { reason: 'Operator payout tax' }
    });

    // Get operator payout account
    const operatorProfile = await OperatorProfile.findOne({ accountId: String(job.assignedOperatorId) });
    if (!operatorProfile || !operatorProfile.payout?.accountNumber) {
      return res.status(400).json({
        success: false,
        error: 'Operator payout account not configured'
      });
    }
    const destinationAccount = operatorProfile?.payout?.accountNumber;

    // Create payout record (processing local ledger)
    const payout = await Payout.create({
      jobId: String(job._id),
      bidId: String(bid._id),
      operatorId: String(job.assignedOperatorId),
      amount: payoutAmount,
      currency: escrowTx.currency,
      status: 'completed',
      operatorTaxRate: OPERATOR_TAX_RATE,
      operatorTaxAmount,
      destinationAccount,
      transactionRef: escrowTx.providerReference,
    });

    // Notify operator
    const operatorToken = await getUserNotificationToken(String(job.assignedOperatorId));
    if (operatorToken.success && operatorToken.notificationToken) {
      await sendCustomNotification(operatorToken.notificationToken, {
        title: 'Payout Released',
        message: `Your payout of ${payoutAmount} has been released`,
        data: { type: 'payout_released', jobId: jobId, bidId: String(bid._id), payoutId: String((payout as any)._id) },
        soundOn: true,
        badgeCount: 1,
      });
    }

    return res.json({ success: true, data: { payoutId: (payout as any)._id.toString(), amount: payoutAmount, operatorTaxAmount } });
  } catch (error) {
    console.error('Error completing job and payout:', error);
    const message = error instanceof Error ? error.message : 'Failed to complete job payout';
    return res.status(500).json({ success: false, error: message });
  }
};

export const cancelJobAndRefund = async (req: Request, res: Response) => {
  try {
    const user = getAuthenticatedUser(req);
    if (!user) return res.status(401).json({ success: false, error: 'Authentication required' });

    const { jobId } = req.params;
    const parse = cancelJobSchema.safeParse(req.body);
    if (!parse.success) return res.status(400).json({ success: false, error: 'Invalid request data', details: parse.error.errors });

    const job = await Job.findById(jobId);
    if (!job) return res.status(404).json({ success: false, error: 'Job not found' });
    if (job.ownerId !== String(user._id)) return res.status(403).json({ success: false, error: 'Access denied' });

    // Idempotency: if already cancelled, return success
    if (job.status === 'cancelled') {
      return res.json({ success: true, data: { alreadyCancelled: true } });
    }

    // Find escrow transaction
    const escrowTx = await PaymentTransaction.findOne({ jobId: String(job._id), type: 'Hold' });
    if (!escrowTx) return res.status(404).json({ success: false, error: 'Escrow transaction not found' });

    // Get job owner's timezone for accurate time calculations
    const jobOwner = await Account.findById(job.ownerId);
    const jobOwnerTimezone = jobOwner?.settings?.timezone || 'Europe/Paris'; // Default fallback

    // Time-based refund logic
    let scheduledDateTime: Date;
    try {
      scheduledDateTime = parseJobDateTimeUtil(job.date, job.hour, jobOwnerTimezone);
    } catch (parseError) {
      // Log parsing error with contextual information and return a 400 so controller doesn't crash
      try {
        console.error('Failed to parse job scheduled datetime', {
          jobId: String(job._id || jobId),
          date: job.date,
          hour: job.hour,
          timezone: jobOwnerTimezone,
          error: parseError instanceof Error ? parseError.message : String(parseError)
        });
      } catch (logErr) {
        // Best-effort logging; swallow to avoid secondary errors
        console.error('Failed to log parse error for job scheduled datetime', logErr);
      }

      return res.status(400).json({ success: false, error: 'Invalid job date or hour format' });
    }

    const now = new Date();
    const hoursDiff = (scheduledDateTime.getTime() - now.getTime()) / (1000 * 60 * 60);

    let refundAmount = escrowTx.amount;
    let cancellationFee = 0;
    let adminShare = 0;
    let operatorShare = 0;
    let reservedFees = 0;

    if (hoursDiff >= 24) {
      // Full refund
      refundAmount = escrowTx.amount;
    } else {
      // Apply cancellation fee: 15% of bid amount retained by admin, 5% to operator
      const bid = await Bid.findById(job.acceptedBidId!);
      if (!bid) return res.status(404).json({ success: false, error: 'Accepted bid not found' });
      // Determine reserved fees already held in the escrow (commission and commission tax)
      // Ensure we operate in smallest currency unit (minor units) and use integer arithmetic only
      const reservedCommission = Math.round(escrowTx.commissionAmount || 0);
      const reservedCommissionTax = Math.round(escrowTx.commissionTaxAmount || 0);
      reservedFees = reservedCommission + reservedCommissionTax;

      // Convert escrow amount to integer minor units
      const escrowAmountMinor = Math.round(escrowTx.amount || 0);

      // Compute available balance in escrow for penalty distribution (minor units)
      const escrowAvailable = Math.max(0, escrowAmountMinor - reservedFees);

      // Derive shares from the escrow-available amount using integer math
      // admin gets 15%, operator gets 5% -> use Math.floor to avoid over-allocation
      adminShare = Math.floor((escrowAvailable * 15) / 100);
      operatorShare = Math.floor((escrowAvailable * 5) / 100);

      // Ensure rounding/logic does not allow total taken to exceed escrow amount
      let totalTaken = reservedFees + adminShare + operatorShare;
      if (totalTaken > escrowAmountMinor) {
        // Reduce admin first, then operator to remove any overage
        let over = totalTaken - escrowAmountMinor;
        const reduceAdmin = Math.min(over, adminShare);
        adminShare -= reduceAdmin;
        over -= reduceAdmin;
        if (over > 0) {
          const reduceOperator = Math.min(over, operatorShare);
          operatorShare -= reduceOperator;
          over -= reduceOperator;
        }
        totalTaken = reservedFees + adminShare + operatorShare;
      }

      cancellationFee = adminShare + operatorShare + reservedFees;
      refundAmount = Math.max(0, escrowAmountMinor - (adminShare + operatorShare + reservedFees));
    }

  // Transactional payment flow with step persistence and compensation
    const session = await mongoose.startSession();
    let compensationNeeded = false;
    let captureCompleted = false;

    try {
      await session.withTransaction(async () => {
        // Check if payment steps already completed (idempotency)
        const existingCancellation = await PaymentTransaction.findOne({
          jobId: String(job._id),
          type: 'Refund',
          metadata: { reason: 'Job cancellation' }
        }).session(session);

        if (existingCancellation) {
          // Already processed, skip payment operations
          return;
        }

        const _pp = await getPaymentProcessor();

        // Step 1: Handle escrow based on current status
        if (escrowTx.status === 'escrow') {
          if (cancellationFee > 0) {
            // Step 1a: Capture full amount
            try {
              await _pp.captureTransaction(String(user._id), String(escrowTx._id));
              captureCompleted = true;

              // Persist capture step
              await PaymentTransaction.findByIdAndUpdate(
                escrowTx._id,
                {
                  $set: {
                    status: 'captured',
                    capturedAt: new Date(),
                    metadata: { ...((escrowTx as any).metadata || {}), captureReason: 'Job cancellation with fee' }
                  }
                },
                { session }
              );

              // Step 1b: Issue refund if needed
              if (refundAmount > 0) {
                try {
                  await _pp.refundTransaction(String(user._id), String(escrowTx._id), refundAmount, 'Job cancellation');

                  // Persist refund transaction
                  await PaymentTransaction.create([{
                    jobId: String(job._id),
                    bidId: String(job.acceptedBidId),
                    userId: String(user._id),
                    amount: refundAmount,
                    currency: escrowTx.currency,
                    status: 'successful',
                    type: 'Refund',
                    parentTransactionId: String(escrowTx._id),
                    metadata: { reason: 'Job cancellation', adminShare, operatorShare, reservedFees }
                  }], { session });
                } catch (refundError) {
                  compensationNeeded = true;
                  throw new Error(`Refund failed after capture: ${refundError instanceof Error ? refundError.message : String(refundError)}`);
                }
              }
            } catch (captureError) {
              throw new Error(`Capture failed: ${captureError instanceof Error ? captureError.message : String(captureError)}`);
            }
          } else {
            // Step 1c: Simply void the escrow (no fee)
            try {
              await _pp.voidTransaction(String(user._id), String(escrowTx._id));

              // Persist void step
              await PaymentTransaction.findByIdAndUpdate(
                escrowTx._id,
                {
                  $set: {
                    status: 'voided',
                    voidedAt: new Date(),
                    metadata: { ...((escrowTx as any).metadata || {}), voidReason: 'Job cancellation no fee' }
                  }
                },
                { session }
              );
            } catch (voidError) {
              throw new Error(`Void failed: ${voidError instanceof Error ? voidError.message : String(voidError)}`);
            }
          }
        } else {
          // Already captured, issue refund if needed
          if (refundAmount > 0) {
            try {
              await _pp.refundTransaction(String(user._id), String(escrowTx._id), refundAmount, 'Job cancellation');

              // Persist refund transaction
              await PaymentTransaction.create([{
                jobId: String(job._id),
                bidId: String(job.acceptedBidId),
                userId: String(user._id),
                amount: refundAmount,
                currency: escrowTx.currency,
                status: 'successful',
                type: 'Refund',
                parentTransactionId: String(escrowTx._id),
                metadata: { reason: 'Job cancellation', adminShare, operatorShare, reservedFees }
              }], { session });
            } catch (refundError) {
              throw new Error(`Refund failed: ${refundError instanceof Error ? refundError.message : String(refundError)}`);
            }
          }
        }
      });
    } catch (transactionError) {
      // Compensation logic: attempt to reverse any completed payment operations
      if (compensationNeeded && captureCompleted) {
        try {
          console.warn('Attempting compensation: voiding captured transaction due to refund failure');
          const _pp = await getPaymentProcessor();
          await _pp.voidTransaction(String(user._id), String(escrowTx._id));

          // Update transaction status to reflect compensation
          await PaymentTransaction.findByIdAndUpdate(escrowTx._id, {
            $set: {
              status: 'voided',
              voidedAt: new Date(),
              metadata: {
                ...((escrowTx as any).metadata || {}),
                compensationReason: 'Voided due to refund failure',
                originalError: transactionError instanceof Error ? transactionError.message : String(transactionError)
              }
            }
          });
        } catch (compensationError) {
          console.error('Compensation failed:', compensationError);
          // Log but don't throw - original error takes precedence
        }
      }

      console.error('Payment transaction failed:', transactionError);
      throw transactionError;
    } finally {
      await session.endSession();
    }

  // Helper to format amounts for display: convert minor units to major units
  // ASSUMPTION: 100 minor units = 1 major unit (e.g., cents). Adjust if different.
  const MINOR_PER_MAJOR = 100;
  const formatMajor = (amt: number) => Number((amt / MINOR_PER_MAJOR).toFixed(2));

  // If operator gets a share on late cancellation, create payout record (idempotent check)
    if (operatorShare > 0 && job.assignedOperatorId) {
      const existingPenaltyPayout = await Payout.findOne({ jobId: String(job._id), bidId: String(job.acceptedBidId), operatorId: String(job.assignedOperatorId), amount: operatorShare }).lean();
      if (!existingPenaltyPayout) {
        // Load operator profile to validate payout settings and avoid marking funds as completed when operator has no valid payout configured
        const operatorProfile = await OperatorProfile.findOne({ accountId: String(job.assignedOperatorId) }).lean();
    // OperatorProfile.payout currently only exposes accountNumber and bankName
    const hasValidPayout = !!(operatorProfile && operatorProfile.payout && operatorProfile.payout.accountNumber);

    if (hasValidPayout) {
          // Create completed payout and include destination account when available
          await Payout.create({
            jobId: String(job._id),
            bidId: String(job.acceptedBidId),
            operatorId: String(job.assignedOperatorId),
            amount: operatorShare,
            currency: escrowTx.currency,
            status: 'completed',
            operatorTaxRate: 0,
            operatorTaxAmount: 0,
      destinationAccount: operatorProfile?.payout?.accountNumber || null,
            transactionRef: escrowTx.providerReference,
          });
        } else {
          // Operator has no valid payout configured: create a pending payout with a clear reason/note so funds are not stranded
          const note = 'Operator payout not configured or inactive; awaiting admin action';
          console.warn(`Pending penalty payout for operator ${job.assignedOperatorId} for job ${job._id}: ${note}`);

          const pendingPayout = await Payout.create({
            jobId: String(job._id),
            bidId: String(job.acceptedBidId),
            operatorId: String(job.assignedOperatorId),
            amount: operatorShare,
            currency: escrowTx.currency,
            status: 'pending',
            reason: 'no_valid_payout',
            note,
            operatorTaxRate: 0,
            operatorTaxAmount: 0,
            transactionRef: escrowTx.providerReference,
            metadata: { operatorProfilePresent: !!operatorProfile }
          });

          // Notify operator about pending payout and instruct them to configure payout settings
          try {
            const operatorToken2 = await getUserNotificationToken(String(job.assignedOperatorId));
            if (operatorToken2.success && operatorToken2.notificationToken) {
              await sendCustomNotification(operatorToken2.notificationToken, {
                title: 'Payout Pending',
                message: `A payout of ${formatMajor(operatorShare)} is pending because your payout account is not configured or inactive. Please update your payout settings.`,
                data: { type: 'payout_pending', jobId: String(job._id), payoutId: String((pendingPayout as any)._id) },
                soundOn: true,
                badgeCount: 1,
              });
            }
          } catch (notifyErr) {
            console.error('Failed to notify operator about pending payout:', notifyErr);
          }
        }
      }
    }

    // Notify customer
    const token = await getUserNotificationToken(job.ownerId);
    if (token.success && token.notificationToken) {
      await sendCustomNotification(token.notificationToken, {
        title: 'Refund Processed',
  message: `Your refund of ${formatMajor(refundAmount)} has been processed`,
        data: { type: 'refund_processed', jobId },
        soundOn: true,
        badgeCount: 1,
      });
    }

    // Notify operator about cancellation penalty if applicable
    if (operatorShare > 0 && job.assignedOperatorId) {
      const operatorToken = await getUserNotificationToken(String(job.assignedOperatorId));
      if (operatorToken.success && operatorToken.notificationToken) {
        await sendCustomNotification(operatorToken.notificationToken, {
          title: 'Late Cancellation Penalty',
      message: `A cancellation penalty has been applied. You received ${formatMajor(operatorShare)}.`,
          data: { type: 'cancellation_penalty', jobId },
          soundOn: true,
          badgeCount: 1,
        });
      }
    }

    // Update job status
    job.status = 'cancelled';
    await job.save();

    // Return amounts in major currency units (for display)
    return res.json({ success: true, data: { refundAmount: formatMajor(refundAmount), cancellationFee: formatMajor(cancellationFee), adminShare: formatMajor(adminShare), operatorShare: formatMajor(operatorShare) } });
  } catch (error) {
    console.error('Error cancelling job and refund:', error);
    const message = error instanceof Error ? error.message : 'Failed to cancel job/refund';
    return res.status(500).json({ success: false, error: message });
  }
};

export const parseJobDateTime = parseJobDateTimeUtil;

