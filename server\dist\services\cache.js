"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const node_cache_1 = tslib_1.__importDefault(require("node-cache"));
// In-memory cache with TTL
class CacheService {
    constructor() {
        // TTL of 30 minutes for chat objects
        this.cache = new node_cache_1.default({
            stdTTL: 1800, // 30 minutes
            checkperiod: 600, // Check for expired keys every 10 minutes
            useClones: false // Don't clone objects for better performance
        });
    }
    // Chat-specific cache methods
    getChatKey(chatId) {
        return `chat:${chatId}`;
    }
    getUserChatsKey(userId, page, limit) {
        return `user_chats:${userId}:${page}:${limit}`;
    }
    // Generic cache operations
    set(key, value, ttl) {
        if (ttl !== undefined) {
            return this.cache.set(key, value, ttl);
        }
        return this.cache.set(key, value);
    }
    get(key) {
        return this.cache.get(key);
    }
    del(key) {
        return this.cache.del(key);
    }
    // Cache invalidation methods
    invalidateChat(chatId) {
        this.del(this.getChatKey(chatId));
    }
    invalidateUserChats(userId) {
        // Get all keys that match the pattern and delete them
        const keys = this.cache.keys();
        const userChatKeys = keys.filter(key => key.startsWith(`user_chats:${userId}:`));
        if (userChatKeys.length > 0) {
            this.cache.del(userChatKeys);
        }
    }
    // Cache chat object
    setChat(chatId, chat) {
        return this.set(this.getChatKey(chatId), chat, 1800); // 30 minutes
    }
    getChat(chatId) {
        return this.get(this.getChatKey(chatId));
    }
    // Cache user chats list
    setUserChats(userId, page, limit, chats, ttl = 300) {
        return this.set(this.getUserChatsKey(userId, page, limit), chats, ttl); // 5 minutes for lists
    }
    getUserChats(userId, page, limit) {
        return this.get(this.getUserChatsKey(userId, page, limit));
    }
    // Clear all cache
    flushAll() {
        this.cache.flushAll();
    }
    // Get cache statistics
    getStats() {
        return this.cache.getStats();
    }
}
// Export singleton instance
exports.default = new CacheService();
