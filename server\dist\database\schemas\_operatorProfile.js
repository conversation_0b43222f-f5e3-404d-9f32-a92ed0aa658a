"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const _jobCategory_1 = tslib_1.__importDefault(require("./_jobCategory"));
const operatorProfileSchema = new mongoose_1.default.Schema({
    accountId: {
        type: String,
        required: true,
        ref: 'Account',
        unique: true // One operator profile per account
    },
    authorizedCategories: [{
            type: String,
            required: true,
            validate: {
                validator: function (val) {
                    return tslib_1.__awaiter(this, void 0, void 0, function* () {
                        const name = String(val).trim().toLowerCase();
                        if (!name)
                            return false;
                        const found = yield _jobCategory_1.default.findOne({ name, isActive: true });
                        return !!found;
                    });
                },
                message: 'Invalid authorized category. Must be an active job category.'
            }
        }],
    isAvailable: {
        type: Boolean,
        default: true
    },
    rating: {
        type: Number,
        default: 0,
        min: 0,
        max: 5
    },
    totalJobsCompleted: {
        type: Number,
        default: 0
    },
    totalBidsMade: {
        type: Number,
        default: 0
    },
    totalBidsWon: {
        type: Number,
        default: 0
    },
    description: {
        type: String,
        default: "",
        maxlength: 1000
    },
    skills: [{
            type: String
        }],
    portfolio: [{
            images: [{
                    type: String // S3 URLs
                }],
            description: {
                type: String,
                maxlength: 500
            }
        }],
    // Sensitive payout information. Do NOT select the raw account number by default.
    // Recommended approaches:
    // - Store only a PSP token (Stripe/Plaid/etc.) and keep that opaque token here instead of raw account numbers.
    // - Or enable field-level encryption / KMS-backed encryption plugin to protect this field at rest.
    // If you must keep the account number at rest, it is hidden by default (select: false) and masked in JSON output below.
    payout: {
        accountNumber: {
            type: String,
            select: false,
            validate: {
                validator: function (val) {
                    if (!val)
                        return true; // allow empty
                    // Normalize by removing spaces and dashes, then ensure it's numeric and between 8 and 20 digits.
                    const normalized = String(val).replace(/\s|-/g, '');
                    return /^\d{8,20}$/.test(normalized);
                },
                message: 'Invalid payout account number. Must be 8-20 digits (spaces/dashes allowed). Prefer storing PSP token or using field-level encryption.'
            }
        },
        bankName: { type: String }
    }
}, {
    timestamps: true,
    toJSON: {
        transform: function (doc, ret) {
            // If payout.accountNumber was selected, mask it in JSON output to avoid leaking full number
            try {
                if (ret && ret.payout && ret.payout.accountNumber) {
                    const acc = String(ret.payout.accountNumber);
                    const normalized = acc.replace(/\s|-/g, '');
                    // show only last 4 digits with masked prefix
                    const last4 = normalized.slice(-4);
                    ret.payout.accountNumber = `****${last4}`;
                }
            }
            catch (e) {
                // ignore masking errors
            }
            return ret;
        }
    }
});
// Indexes for efficient querying
operatorProfileSchema.index({ authorizedCategories: 1 });
operatorProfileSchema.index({ isAvailable: 1 });
operatorProfileSchema.index({ rating: -1 });
exports.default = mongoose_1.default.model('OperatorProfile', operatorProfileSchema);
