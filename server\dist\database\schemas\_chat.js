"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const _jobCategory_1 = tslib_1.__importDefault(require("./_jobCategory"));
const messageSchema = new mongoose_1.default.Schema({
    role: {
        type: String,
        enum: ['user', 'assistant'],
        required: true
    },
    content: {
        type: String,
        required: true
    },
    timestamp: {
        type: Date,
        default: Date.now
    },
    images: [{
            type: String // S3 URLs
        }]
});
const chatSchema = new mongoose_1.default.Schema({
    ownerId: {
        type: String,
        required: true,
        ref: 'Account'
    },
    status: {
        type: String,
        enum: ['started', 'chatting', 'done'],
        default: 'started'
    },
    category: {
        type: String,
        default: null,
        validate: {
            // Allow null (category not chosen yet). When provided, it must exist in JobCategory (active).
            validator: function (val) {
                return tslib_1.__awaiter(this, void 0, void 0, function* () {
                    if (val === null || typeof val === 'undefined')
                        return true;
                    const name = String(val).trim().toLowerCase();
                    if (!name)
                        return false;
                    const found = yield _jobCategory_1.default.findOne({ name, isActive: true });
                    return !!found;
                });
            },
            message: 'Invalid category. Must be one of the active job categories.'
        }
    },
    description: {
        type: String,
        default: null
    },
    images: [{
            type: String // S3 URLs
        }],
    price: {
        type: Number,
        default: null
    },
    date: {
        type: String,
        default: null,
        match: /^\d{2}\/\d{2}\/\d{4}$/ // DD/MM/YYYY format
    },
    hour: {
        type: String,
        default: null,
        match: /^\d{2}:\d{2}$/ // HH:MM format
    },
    messages: [messageSchema]
}, {
    timestamps: true
});
// Index for efficient querying
chatSchema.index({ ownerId: 1, createdAt: -1 });
chatSchema.index({ status: 1 });
// Ensure validators run on update operations
chatSchema.pre(['updateOne', 'updateMany', 'findOneAndUpdate'], function () {
    this.setOptions({ runValidators: true, context: 'query' });
});
exports.default = mongoose_1.default.model('Chat', chatSchema);
