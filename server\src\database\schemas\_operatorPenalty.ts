import mongoose, { Document } from 'mongoose';

export type PenaltyType = 
  | 'cancellation_before_start'   // Cancelled before job start
  | 'cancellation_after_arrival'  // Cancelled after arriving at location
  | 'no_show'                     // Failed to show up for job
  | 'poor_performance'            // Performance-related penalty
  | 'policy_violation'            // Platform policy violation
  | 'client_complaint';           // Client filed a complaint

export type PenaltyStatus = 
  | 'pending'                     // Penalty issued but not processed
  | 'active'                      // Penalty is active and affecting operator
  | 'served'                      // Penalty period completed
  | 'waived'                      // Penalty was waived by admin
  | 'disputed'                    // Penalty is under dispute
  | 'expired';                    // Penalty expired without being served

export interface IOperatorPenalty extends Document {
  id: string;
  operatorId: string; // Reference to Account (operator)
  jobId?: string; // Reference to Job (if job-related)
  bidId?: string; // Reference to Bid (if bid-related)
  cancellationId?: string; // Reference to CancellationRecord (if cancellation-related)
  
  type: PenaltyType;
  status: PenaltyStatus;
  severity: 'minor' | 'moderate' | 'major' | 'severe';
  
  // Financial penalty
  financialPenalty: {
    amount: number; // Penalty amount in minor units
    currency: string;
    deductedFrom: 'future_earnings' | 'security_deposit' | 'immediate_charge';
    deductedAt?: Date; // When the financial penalty was applied
    paymentTransactionId?: string; // Related payment transaction
  };
  
  // Rating impact
  ratingImpact: {
    pointsDeducted: number; // Rating points deducted (e.g., 0.1, 0.5)
    appliedAt?: Date; // When rating impact was applied
    previousRating?: number; // Operator's rating before penalty
    newRating?: number; // Operator's rating after penalty
    isConfigurable: boolean; // Whether admin can configure this impact
  };
  
  // Availability restrictions
  availabilityRestriction: {
    restrictionType: 'none' | 'temporary_suspension' | 'category_restriction' | 'permanent_ban';
    restrictedUntil?: Date; // When restriction expires
    restrictedCategories?: string[]; // Categories operator is restricted from
    appliedAt?: Date; // When restriction was applied
    liftedAt?: Date; // When restriction was lifted
  };
  
  // Penalty details
  reason: string; // Reason for the penalty
  description: string; // Detailed description
  evidenceUrls?: string[]; // Supporting evidence
  
  // Administrative details
  issuedBy: string; // Account ID of who issued the penalty (admin or system)
  issuedByRole: 'admin' | 'system';
  reviewedBy?: string; // Admin who reviewed the penalty
  reviewedAt?: Date; // When penalty was reviewed
  reviewNotes?: string; // Admin review notes
  
  // Appeal/Dispute information
  appealReason?: string; // Operator's appeal reason
  appealedAt?: Date; // When appeal was submitted
  appealStatus?: 'none' | 'pending' | 'approved' | 'rejected';
  appealResolvedAt?: Date; // When appeal was resolved
  appealResolution?: string; // Appeal resolution details
  
  // Recurrence tracking
  isRecurring: boolean; // Whether this is a repeat offense
  previousPenaltyIds: string[]; // IDs of previous related penalties
  recurrenceMultiplier: number; // Multiplier for repeat offenses (1.0 = no multiplier)
  
  // Expiration and cleanup
  expiresAt?: Date; // When penalty record expires (for cleanup)
  archivedAt?: Date; // When penalty was archived
  
  // Metadata
  metadata: {
    automaticallyIssued: boolean; // Whether penalty was issued automatically
    clientReported: boolean; // Whether client reported the issue
    systemDetected: boolean; // Whether system detected the violation
    warningIssued: boolean; // Whether a warning was issued first
    escalationLevel: number; // Escalation level (1 = first offense, 2+ = repeat)
    relatedTicketId?: string; // Support ticket ID if applicable
    internalNotes?: string; // Internal admin notes
  };
  
  createdAt: Date;
  updatedAt: Date;
}

const operatorPenaltySchema = new mongoose.Schema<IOperatorPenalty>({
  operatorId: {
    type: String,
    required: true,
    ref: 'Account',
    index: true
  },
  jobId: {
    type: String,
    ref: 'Job',
    index: true
  },
  bidId: {
    type: String,
    ref: 'Bid'
  },
  cancellationId: {
    type: String,
    ref: 'CancellationRecord'
  },
  type: {
    type: String,
    required: true,
    enum: [
      'cancellation_before_start',
      'cancellation_after_arrival',
      'no_show',
      'poor_performance',
      'policy_violation',
      'client_complaint'
    ],
    index: true
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'active', 'served', 'waived', 'disputed', 'expired'],
    default: 'pending',
    index: true
  },
  severity: {
    type: String,
    required: true,
    enum: ['minor', 'moderate', 'major', 'severe'],
    index: true
  },
  financialPenalty: {
    amount: { type: Number, required: true, min: 0 },
    currency: { type: String, required: true, enum: ['DOP', 'USD'], default: 'DOP' },
    deductedFrom: { 
      type: String, 
      required: true,
      enum: ['future_earnings', 'security_deposit', 'immediate_charge'] 
    },
    deductedAt: { type: Date },
    paymentTransactionId: { type: String, ref: 'PaymentTransaction' }
  },
  ratingImpact: {
    pointsDeducted: { type: Number, required: true, min: 0, max: 5 },
    appliedAt: { type: Date },
    previousRating: { type: Number, min: 0, max: 5 },
    newRating: { type: Number, min: 0, max: 5 },
    isConfigurable: { type: Boolean, default: true }
  },
  availabilityRestriction: {
    restrictionType: { 
      type: String, 
      required: true,
      enum: ['none', 'temporary_suspension', 'category_restriction', 'permanent_ban'],
      default: 'none'
    },
    restrictedUntil: { type: Date },
    restrictedCategories: [{ type: String }],
    appliedAt: { type: Date },
    liftedAt: { type: Date }
  },
  reason: {
    type: String,
    required: true,
    maxlength: 500
  },
  description: {
    type: String,
    required: true,
    maxlength: 2000
  },
  evidenceUrls: [{
    type: String // S3 URLs
  }],
  issuedBy: {
    type: String,
    required: true,
    ref: 'Account'
  },
  issuedByRole: {
    type: String,
    required: true,
    enum: ['admin', 'system']
  },
  reviewedBy: {
    type: String,
    ref: 'Account'
  },
  reviewedAt: {
    type: Date
  },
  reviewNotes: {
    type: String,
    maxlength: 1000
  },
  appealReason: {
    type: String,
    maxlength: 1000
  },
  appealedAt: {
    type: Date
  },
  appealStatus: {
    type: String,
    enum: ['none', 'pending', 'approved', 'rejected'],
    default: 'none'
  },
  appealResolvedAt: {
    type: Date
  },
  appealResolution: {
    type: String,
    maxlength: 1000
  },
  isRecurring: {
    type: Boolean,
    default: false
  },
  previousPenaltyIds: [{
    type: String,
    ref: 'OperatorPenalty'
  }],
  recurrenceMultiplier: {
    type: Number,
    default: 1.0,
    min: 1.0,
    max: 5.0
  },
  expiresAt: {
    type: Date,
    index: true
  },
  archivedAt: {
    type: Date
  },
  metadata: {
    automaticallyIssued: { type: Boolean, default: false },
    clientReported: { type: Boolean, default: false },
    systemDetected: { type: Boolean, default: false },
    warningIssued: { type: Boolean, default: false },
    escalationLevel: { type: Number, default: 1, min: 1 },
    relatedTicketId: { type: String },
    internalNotes: { type: String, maxlength: 2000 }
  }
}, {
  timestamps: true
});

// Indexes for efficient querying
operatorPenaltySchema.index({ operatorId: 1, createdAt: -1 });
operatorPenaltySchema.index({ type: 1, status: 1 });
operatorPenaltySchema.index({ severity: 1, status: 1 });
operatorPenaltySchema.index({ expiresAt: 1 });
operatorPenaltySchema.index({ jobId: 1 });

export default mongoose.model<IOperatorPenalty>('OperatorPenalty', operatorPenaltySchema);
