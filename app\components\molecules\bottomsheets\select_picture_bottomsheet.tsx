import React, { useEffect, useState } from 'react';
import {
  View,
  FlatList,
  Image,
  Dimensions,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Text,
  Button,
} from 'react-native';
import * as MediaLibrary from 'expo-media-library';

interface RenderBottomSheetProps {
  onConfirm: (selected: string[]) => void;
}

const MAX_SELECTION = 3;

const RenderBottomSheet: React.FC<RenderBottomSheetProps> = ({ onConfirm }) => {
  const [photos, setPhotos] = useState<MediaLibrary.Asset[]>([]);
  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([]);
  const [pageInfo, setPageInfo] = useState<MediaLibrary.PagedInfo<MediaLibrary.Asset> | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchPhotos = async (after?: string) => {
    if (loading) return;
    setLoading(true);
    const { status } = await MediaLibrary.requestPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission required', 'Please allow access to media library.');
      setLoading(false);
      return;
    }

    const media = await MediaLibrary.getAssetsAsync({
      mediaType: 'photo',
      first: 50,
      after,
      sortBy: [['creationTime', false]],
    });

    

    setPhotos((prev) => (after ? [...prev, ...media.assets] : media.assets));
    setPageInfo(media);
    setLoading(false);
  };

  useEffect(() => {
    fetchPhotos();
  }, []);

  const loadMore = () => {
    if (pageInfo?.hasNextPage) {
      fetchPhotos(pageInfo.endCursor);
    }
  };

  const toggleSelectPhoto = (id: string) => {
    setSelectedPhotos((prev) => {
      const alreadySelected = prev.includes(id);
      if (alreadySelected) {
        return prev.filter((i) => i !== id);
      } else if (prev.length < MAX_SELECTION) {
        return [...prev, id];
      } else {
        Alert.alert(`You can only select up to ${MAX_SELECTION} photos.`);
        return prev;
      }
    });
  };

  return (
    <View style={{ flex: 1, paddingHorizontal: 20 }}>
      {loading && photos.length === 0 ? (
        <ActivityIndicator size="large" />
      ) : (
        <>
          <FlatList
            data={photos}
            numColumns={4}
            keyExtractor={(item) => item.id}
            onEndReached={loadMore}
            onEndReachedThreshold={0.5}
            renderItem={({ item }) => {
              const selected = selectedPhotos.includes(item.id);
              return (
                <TouchableOpacity
                  onPress={() => toggleSelectPhoto(item.id)}
                  style={{
                    margin: 1,
                    borderWidth: selected ? 2 : 2,
                    borderColor: selected ? '#3374ddff' : '#ccc',
                    borderRadius: 8,
                    position: 'relative',
                  }}
                >
                  <Image
                    source={{ uri: item.uri }}
                    style={{
                      width: (Dimensions.get('window').width - 65) / 4,
                      height: 110,
                      borderRadius: 5,
                    }}
                  />
                  {selected && (
                    <View
                      style={{
                        position: 'absolute',
                        top: 5,
                        right: 5,
                        backgroundColor: 'white',
                        borderRadius: 10,
                        width: 20,
                        height: 20,
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                    >
                      <Text style={{ fontSize: 12 }}>✓</Text>
                    </View>
                  )}
                </TouchableOpacity>
              );
            }}
          />

          <View
            style={{
              padding: 16,
              borderTopWidth: 1,
              borderColor: '#ddd',
              backgroundColor: '#fff',
            }}
          >
            <Button
              title={`Seleccionar (${selectedPhotos.length})`}
              disabled={selectedPhotos.length === 0}
              onPress={() => onConfirm(selectedPhotos)}
              color="#0984e3"
            />
          </View>
        </>
      )}
    </View>
  );
};

export default RenderBottomSheet;
