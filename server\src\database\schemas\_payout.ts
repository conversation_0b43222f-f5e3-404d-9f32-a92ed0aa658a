import mongoose, { Document, Schema } from 'mongoose';

export interface IPayout extends Document {
  id: string;
  jobId: string;
  bidId: string;
  operatorId: string; // Account id
  amount: number; // payout amount in minor units
  currency: 'DOP' | 'USD';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  operatorTaxRate: number; // e.g., 0.02
  operatorTaxAmount: number; // computed
  destinationAccount?: string; // operator account number
  transactionRef?: string; // external transfer reference
  createdAt: Date;
  updatedAt: Date;
}

const payoutSchema = new mongoose.Schema<IPayout>({
  jobId: { type: String, ref: 'Job', required: true, index: true },
  bidId: { type: String, ref: 'Bid', required: true, index: true },
  operatorId: { type: String, ref: 'Account', required: true, index: true },
  amount: {
    type: Number,
    required: true,
    min: 0,
    validate: {
      validator: Number.isInteger,
      message: 'amount must be integer minor units'
    }
  },
  currency: { type: String, enum: ['DOP', 'USD'], default: 'DOP', required: true },
  status: { type: String, enum: ['pending', 'processing', 'completed', 'failed'], default: 'pending', index: true },
  operatorTaxRate: { type: Number, required: true, min: 0, max: 1 },
  operatorTaxAmount: { type: Number, required: true, min: 0 },
  destinationAccount: { type: String, select: false },
  transactionRef: { type: String }
}, { timestamps: true });

payoutSchema.index({ operatorId: 1, createdAt: -1 });

// Ensure operatorTaxAmount is consistent with amount * operatorTaxRate
payoutSchema.pre('validate', function (next) {
  const doc: any = this;
  try {
    if (typeof doc.amount === 'number' && typeof doc.operatorTaxRate === 'number') {
      // Round to nearest minor unit
      doc.operatorTaxAmount = Math.round(doc.amount * doc.operatorTaxRate);
    }
    return next();
  } catch (err) {
    return next(err as any);
  }
});

export default mongoose.model<IPayout>('Payout', payoutSchema);

