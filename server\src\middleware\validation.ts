import { Request, Response, NextFunction } from 'express';
import mongoose from 'mongoose';

// Factory to create ObjectId validators for different route parameter names
function makeIdValidator(paramName: 'userId' | 'operatorId' | 'jobId' | 'categoryId', errorMessage: string) {
  return (req: Request, res: Response, next: NextFunction) => {
    const value = String((req.params as any)[paramName] ?? '').trim();
    (req.params as any)[paramName] = value; // persist sanitized value for downstream use

    if (!mongoose.Types.ObjectId.isValid(value)) {
      return res.status(400).json({ success: false, error: errorMessage });
    }
    return next();
  };
}

// Middleware to validate MongoDB ObjectId parameters
export const validateUserId = makeIdValidator('userId', 'Invalid user ID format');
export const validateOperatorId = makeIdValidator('operatorId', 'Invalid operator ID format');
export const validateJobId = makeIdValidator('jobId', 'Invalid job ID format');
export const validateCategoryId = makeIdValidator('categoryId', 'Invalid category ID format');
