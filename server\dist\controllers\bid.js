"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.withdrawBid = exports.getOperatorBids = exports.acceptBid = exports.getJobBids = exports.createBid = void 0;
const tslib_1 = require("tslib");
const schemas_1 = require("../database/schemas");
const auth_1 = require("../utils/auth");
const notifications_1 = require("./notifications");
// Helper function to safely compare IDs
function isSameId(id1, id2) {
    if (!id1 || !id2)
        return false;
    return String(id1).trim() === String(id2).trim();
}
// Create a bid for a job
const createBid = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { jobId } = req.params;
        const { amount, message, estimatedDuration, proposedStartDate, proposedStartTime } = req.body;
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        // Validate required fields
        if (!amount || !message || !estimatedDuration || !proposedStartDate || !proposedStartTime) {
            return res.status(400).json({
                success: false,
                error: 'Missing required fields: amount, message, estimatedDuration, proposedStartDate, proposedStartTime'
            });
        }
        // Check if user is an operator
        const account = yield schemas_1.Account.findById(userId);
        if (!account || !account.booleans.isOperator) {
            return res.status(403).json({ success: false, error: 'Only operators can place bids' });
        }
        // Get job and validate
        const job = yield schemas_1.Job.findById(jobId);
        if (!job) {
            return res.status(404).json({ success: false, error: 'Job not found' });
        }
        if (job.status !== 'pending') {
            return res.status(400).json({ success: false, error: 'Job is no longer accepting bids' });
        }
        // Check if operator owns this job (can't bid on own job)
        if (isSameId(job.ownerId, userId)) {
            return res.status(400).json({ success: false, error: 'Cannot bid on your own job' });
        }
        // Check if operator is authorized for this category
        const operatorProfile = yield schemas_1.OperatorProfile.findOne({ accountId: userId });
        if (!operatorProfile) {
            return res.status(400).json({ success: false, error: 'Operator profile not found' });
        }
        if (!operatorProfile.authorizedCategories.includes(job.category)) {
            return res.status(403).json({
                success: false,
                error: 'Not authorized to bid on jobs in this category'
            });
        }
        if (!operatorProfile.isAvailable) {
            return res.status(400).json({ success: false, error: 'Operator is currently unavailable' });
        }
        // Check if operator already has a bid for this job
        const existingBid = yield schemas_1.Bid.findOne({ jobId, operatorId: userId });
        if (existingBid) {
            return res.status(400).json({ success: false, error: 'You have already placed a bid for this job' });
        }
        // Create the bid
        const bid = new schemas_1.Bid({
            jobId,
            operatorId: userId,
            amount: Number(amount),
            message,
            estimatedDuration,
            proposedStartDate,
            proposedStartTime
        });
        yield bid.save();
        // Update job's total bids count
        job.totalBids += 1;
        yield job.save();
        // Update operator's bid statistics
        operatorProfile.totalBidsMade += 1;
        yield operatorProfile.save();
        // Notify job owner about new bid
        const jobOwnerNotificationToken = yield (0, notifications_1.getUserNotificationToken)(job.ownerId);
        if (jobOwnerNotificationToken.success && jobOwnerNotificationToken.notificationToken) {
            yield (0, notifications_1.sendCustomNotification)(jobOwnerNotificationToken.notificationToken, {
                title: 'New Bid Received',
                message: `You received a new bid of $${amount} for your ${job.category} job`,
                data: { jobId, bidId: bid._id, type: 'bid_placed' },
                soundOn: true
            });
        }
        // Log the notification
        yield schemas_1.NotificationLog.create({
            recipientId: job.ownerId,
            type: 'bid_placed',
            title: 'New Bid Received',
            message: `You received a new bid of $${amount} for your ${job.category} job`,
            data: { jobId, bidId: bid._id, operatorId: userId },
            jobId,
            bidId: bid._id
        });
        res.status(201).json({
            success: true,
            message: 'Bid placed successfully',
            bid
        });
    }
    catch (error) {
        console.error('Error creating bid:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.createBid = createBid;
// Get all bids for a job (for job owners)
const getJobBids = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { jobId } = req.params;
        const { page = 1, limit = 10, status } = req.query;
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        // Get job and verify ownership
        const job = yield schemas_1.Job.findById(jobId);
        if (!job) {
            return res.status(404).json({ success: false, error: 'Job not found' });
        }
        if (!isSameId(job.ownerId, userId)) {
            return res.status(403).json({ success: false, error: 'Access denied' });
        }
        // Build query
        const query = { jobId };
        if (status && ['pending', 'accepted', 'rejected', 'withdrawn'].includes(status)) {
            query.status = status;
        }
        // Get bids with operator details
        const bids = yield schemas_1.Bid.find(query)
            .populate('operatorId', 'user.name user.surname user.username user.profile_picture')
            .sort({ createdAt: -1 })
            .limit(Number(limit))
            .skip((Number(page) - 1) * Number(limit));
        // Get operator profiles for additional info
        const bidsWithProfiles = yield Promise.all(bids.map((bid) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
            const operatorProfile = yield schemas_1.OperatorProfile.findOne({ accountId: bid.operatorId });
            return Object.assign(Object.assign({}, bid.toObject()), { operatorProfile: operatorProfile ? {
                    rating: operatorProfile.rating,
                    totalJobsCompleted: operatorProfile.totalJobsCompleted,
                    skills: operatorProfile.skills,
                    description: operatorProfile.description
                } : null });
        })));
        const total = yield schemas_1.Bid.countDocuments(query);
        res.json({
            success: true,
            bids: bidsWithProfiles,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total,
                pages: Math.ceil(total / Number(limit))
            }
        });
    }
    catch (error) {
        console.error('Error getting job bids:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.getJobBids = getJobBids;
// Accept a bid (for job owners)
const acceptBid = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { bidId } = req.params;
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        // Get bid and populate job
        const bid = yield schemas_1.Bid.findById(bidId);
        if (!bid) {
            return res.status(404).json({ success: false, error: 'Bid not found' });
        }
        const job = yield schemas_1.Job.findById(bid.jobId);
        if (!job) {
            return res.status(404).json({ success: false, error: 'Job not found' });
        }
        // Verify job ownership
        if (!isSameId(job.ownerId, userId)) {
            return res.status(403).json({ success: false, error: 'Access denied' });
        }
        // Check if job is still pending
        if (job.status !== 'pending') {
            return res.status(400).json({ success: false, error: 'Job is no longer accepting bids' });
        }
        // Check if bid is still pending
        if (bid.status !== 'pending') {
            return res.status(400).json({ success: false, error: 'Bid is no longer available' });
        }
        // Update bid status to accepted
        bid.status = 'accepted';
        yield bid.save();
        // Update job with accepted bid details
        job.status = 'accepted';
        job.acceptedBidId = bid._id.toString();
        job.assignedOperatorId = bid.operatorId;
        job.finalPrice = bid.amount;
        yield job.save();
        // Reject all other pending bids for this job
        yield schemas_1.Bid.updateMany({ jobId: bid.jobId, _id: { $ne: bid._id }, status: 'pending' }, { status: 'rejected' });
        // Update operator statistics
        const operatorProfile = yield schemas_1.OperatorProfile.findOne({ accountId: bid.operatorId });
        if (operatorProfile) {
            operatorProfile.totalBidsWon += 1;
            yield operatorProfile.save();
        }
        // Notify the winning operator
        const operatorNotificationToken = yield (0, notifications_1.getUserNotificationToken)(bid.operatorId);
        if (operatorNotificationToken.success && operatorNotificationToken.notificationToken) {
            yield (0, notifications_1.sendCustomNotification)(operatorNotificationToken.notificationToken, {
                title: 'Bid Accepted!',
                message: `Congratulations! Your bid of $${bid.amount} has been accepted`,
                data: { jobId: job._id, bidId: bid._id, type: 'bid_accepted' },
                soundOn: true
            });
        }
        // Log notification for winning operator
        yield schemas_1.NotificationLog.create({
            recipientId: bid.operatorId,
            type: 'bid_accepted',
            title: 'Bid Accepted!',
            message: `Congratulations! Your bid of $${bid.amount} has been accepted`,
            data: { jobId: job._id, bidId: bid._id },
            jobId: job._id.toString(),
            bidId: bid._id.toString()
        });
        // Notify rejected operators
        const rejectedBids = yield schemas_1.Bid.find({
            jobId: bid.jobId,
            _id: { $ne: bid._id },
            status: 'rejected'
        });
        for (const rejectedBid of rejectedBids) {
            const rejectedOperatorToken = yield (0, notifications_1.getUserNotificationToken)(rejectedBid.operatorId);
            if (rejectedOperatorToken.success && rejectedOperatorToken.notificationToken) {
                yield (0, notifications_1.sendCustomNotification)(rejectedOperatorToken.notificationToken, {
                    title: 'Bid Not Selected',
                    message: `Your bid for the ${job.category} job was not selected`,
                    data: { jobId: job._id, bidId: rejectedBid._id, type: 'bid_rejected' },
                    soundOn: false
                });
            }
            // Log notification for rejected operators
            yield schemas_1.NotificationLog.create({
                recipientId: rejectedBid.operatorId,
                type: 'bid_rejected',
                title: 'Bid Not Selected',
                message: `Your bid for the ${job.category} job was not selected`,
                data: { jobId: job._id, bidId: rejectedBid._id },
                jobId: job._id.toString(),
                bidId: rejectedBid._id.toString()
            });
        }
        res.json({
            success: true,
            message: 'Bid accepted successfully',
            job,
            acceptedBid: bid
        });
    }
    catch (error) {
        console.error('Error accepting bid:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.acceptBid = acceptBid;
// Get operator's bids
const getOperatorBids = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { page = 1, limit = 10, status } = req.query;
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        // Check if user is an operator
        const account = yield schemas_1.Account.findById(userId);
        if (!account || !account.booleans.isOperator) {
            return res.status(403).json({ success: false, error: 'Access denied' });
        }
        // Build query
        const query = { operatorId: userId };
        if (status && ['pending', 'accepted', 'rejected', 'withdrawn'].includes(status)) {
            query.status = status;
        }
        // Get bids with job details
        const bids = yield schemas_1.Bid.find(query)
            .populate('jobId', 'category description price date hour status ownerId')
            .sort({ createdAt: -1 })
            .limit(Number(limit))
            .skip((Number(page) - 1) * Number(limit));
        const total = yield schemas_1.Bid.countDocuments(query);
        res.json({
            success: true,
            bids,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total,
                pages: Math.ceil(total / Number(limit))
            }
        });
    }
    catch (error) {
        console.error('Error getting operator bids:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.getOperatorBids = getOperatorBids;
// Withdraw a bid (for operators)
const withdrawBid = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { bidId } = req.params;
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        // Get bid and verify ownership
        const bid = yield schemas_1.Bid.findById(bidId);
        if (!bid) {
            return res.status(404).json({ success: false, error: 'Bid not found' });
        }
        if (!isSameId(bid.operatorId, userId)) {
            return res.status(403).json({ success: false, error: 'Access denied' });
        }
        // Check if bid can be withdrawn
        if (bid.status !== 'pending') {
            return res.status(400).json({
                success: false,
                error: 'Cannot withdraw bid that is not pending'
            });
        }
        // Update bid status
        bid.status = 'withdrawn';
        yield bid.save();
        // Update job's total bids count
        const job = yield schemas_1.Job.findById(bid.jobId);
        if (job) {
            job.totalBids = Math.max(0, job.totalBids - 1);
            yield job.save();
        }
        res.json({
            success: true,
            message: 'Bid withdrawn successfully',
            bid
        });
    }
    catch (error) {
        console.error('Error withdrawing bid:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.withdrawBid = withdrawBid;
