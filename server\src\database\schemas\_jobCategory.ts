import mongoose  from 'mongoose';
import { DOCUMENT_REQUIREMENT_CATEGORIES, DocumentRequirementCategory } from '../../types/documentRequirements';

export interface IJobCategory {
  name: string;
  displayName: string;
  description?: string;
  isActive: boolean;
  order: number; // For sorting categories in UI
  documents_required?: DocumentRequirementCategory[]; // Required doc categories for operators applying to this job category
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

const jobCategorySchema = new mongoose.Schema<IJobCategory>({
  name: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    match: /^[a-z_]+$/ // Only lowercase letters and underscores
  },
  displayName: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  order: {
    type: Number,
    default: 0
  },
  documents_required: {
    type: [String],
    default: ['none'],
    validate: {
      validator: function (arr: string[]) {
        if (!Array.isArray(arr)) return false;
        return arr.every((v) => (DOCUMENT_REQUIREMENT_CATEGORIES as readonly string[]).includes(String(v)));
      },
      message: 'Invalid document requirement category provided in job category',
    },
  }
}, {
  timestamps: true
});

// Index for efficient querying 
jobCategorySchema.index({ isActive: 1, order: 1 });

const JobCategory =
   (mongoose.models.JobCategory as mongoose.Model<IJobCategory>) ||
   mongoose.model<IJobCategory>('JobCategory', jobCategorySchema);
export default JobCategory;