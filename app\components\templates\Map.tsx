import React, { forwardRef, useEffect, useRef, useState } from 'react';
import {
  Alert,
  StyleSheet,
  View,
  Platform,
  TouchableOpacity,
  Text,
} from 'react-native';
import MapView, {
  <PERSON><PERSON>,
  <PERSON>yl<PERSON>,
  PROVIDER_GOOGLE,
} from 'react-native-maps';
import * as Location from 'expo-location';
import { Ionicons } from '@expo/vector-icons';

interface MapProps {
  noCenterButton?: boolean;
  polylines?: {
    latitude: number;
    longitude: number;
  }[];
}

const Map = forwardRef<MapView, MapProps>(({ polylines, noCenterButton }, ref) => {
  const mapRef = useRef<MapView | null>(null);
  const locationSubscription = useRef<Location.LocationSubscription | null>(null);

  const [location, setLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [hasCentered, setHasCentered] = useState(false);
  const [isTracking, setIsTracking] = useState(true);
  const [droppedPin, setDroppedPin] = useState<{ latitude: number; longitude: number } | null>(null);


  const initLocation = async () => {
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Denied', 'Location access is required to show your position.');
      return;
    }

    locationSubscription.current = await Location.watchPositionAsync(
      {
        accuracy: Location.Accuracy.BestForNavigation,
        //distanceInterval: 10,
        timeInterval: 1000
      },
      async (newLocation) => {
        const { latitude, longitude } = newLocation.coords;
        const coords = { latitude, longitude };
        setLocation(coords);

        if (isTrackingRef.current && mapRef.current) {
          try {
            const camera = await mapRef.current.getCamera();
            mapRef.current.animateCamera({
              center: coords,
              pitch: camera.pitch,
              heading: camera.heading,
              altitude: camera.altitude,
              zoom: camera.zoom,
            }, { duration: 1000 });
          } catch (e) {
            // Map might not be ready, ignore error
          }
        }

        if (!hasCentered) {
          setHasCentered(true);
        }
      }
    );
  };
  const toggleTracking = async () => {
    if (!isTracking) {
      if (location && mapRef.current) {
        const camera = await mapRef.current.getCamera();
        mapRef.current.animateCamera({
          center: location,
          pitch: camera.pitch,
          heading: camera.heading,
          altitude: camera.altitude,
          zoom: camera.zoom,
        });
      }
    }
    setIsTracking((prev) => !prev);
  };

  const isTrackingRef = useRef(isTracking);

  useEffect(() => {
    initLocation();
    return () => {
      locationSubscription.current?.remove();
    };
  }, []);
  useEffect(() => {
    isTrackingRef.current = isTracking;
  }, [isTracking]);
  useEffect(() => {
    if (!polylines || polylines.length === 0) return;

    const start = {
      latitude: polylines[0].latitude,
      longitude: polylines[0].longitude,
    };

    setLocation(start);

    setTimeout(() => {
      if (mapRef.current) {
        mapRef.current.animateCamera({
          center: start,
          zoom: 17,
          pitch: 60,
        });
      }
    }, 300);
  }, [polylines]);



  return (
    <View style={styles.container}>
      {
        isTracking &&
        <TouchableOpacity
          activeOpacity={1}
          onPressIn={() => {
            if (isTrackingRef.current) {
              setIsTracking(false);
              isTrackingRef.current = false; // <-- immediate sync!
            }
          }}
          style={{
            position: 'absolute',
            height: '100%',
            width: '100%',
            top: 0,
            backgroundColor: 'transparent',
            zIndex: 100
          }}
        />
      }


      <MapView
        ref={(node) => {
          mapRef.current = node;
          if (typeof ref === 'function') ref(node);
          else if (ref) ref.current = node;
        }}
        provider={Platform.OS === 'android' ? PROVIDER_GOOGLE : undefined}
        style={styles.map}
        showsUserLocation={true}
        showsMyLocationButton={false}
        initialRegion={{
          latitude: polylines?.[0]?.latitude ?? location?.latitude ?? 0,
          longitude: polylines?.[0]?.longitude ?? location?.longitude ?? -69.89,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }}
        onPress={(event) => {
          const coords = event.nativeEvent.coordinate;
          setDroppedPin(coords);
          // Optionally disable tracking when user drops a pin
          if (isTrackingRef.current) {
            setIsTracking(false);
            isTrackingRef.current = false;
          }
        }}
      >
        {/* Path */}
        {polylines && polylines.length > 0 && (
          <>
            <Marker coordinate={polylines[0]}>
              <View style={styles.pickupMarker}>
                <Ionicons name="location" size={16} color="#10b981" />
              </View>
            </Marker>

            <Marker coordinate={polylines[polylines.length - 1]}>
              <View style={styles.dropoffMarker}>
                <Ionicons name="flag" size={16} color="#ef4444" />
              </View>
            </Marker>

            <Polyline
              coordinates={polylines}
              strokeWidth={4}
              strokeColor="#3b82f6"
              lineDashPattern={[1, 0]}
            />
          </>
        )}

        {droppedPin && (
          <Marker coordinate={droppedPin}>
            <View style={{
              width: 30,
              height: 30,
              borderRadius: 15,
              borderBottomRightRadius: 0,
              backgroundColor: '#337836',
              transform: [{rotateZ: '45deg'}, {translateY: -15}, {translateX: -15}],
              
              
              justifyContent: 'center',
              alignItems: 'center',
            }}>
              <Ionicons 
                name="pin" 
                size={20} 
                color="white"
                style={{

                  transform: [{rotateZ: '-45deg'}],
                }}
              />
            </View>
          </Marker>
        )}
      </MapView>

      {!noCenterButton && (
        <View style={styles.recenterButton}>
          <TouchableOpacity onPress={toggleTracking} style={styles.button}>
            <Ionicons
              name={isTracking ? 'locate' : 'locate-outline'}
              size={24}
              color={isTracking ? '#10b981' : '#888'}
            />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
});

export default Map;

const styles = StyleSheet.create({
  container: {
    height: '100%',
    width: '100%',
    borderRadius: 20
  },
  map: {
    width: '100%',
    height: '100%',
    borderRadius: 20
  },
  pickupMarker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#10b981',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  dropoffMarker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#ef4444',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  recenterButton: {
    position: 'absolute',
    bottom: 35,
    right: 20,
    borderRadius: 20,
    overflow: 'hidden',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    elevation: 3,
  },
  buttonText: {
    fontWeight: '600',
    marginLeft: 6,
  },
});
