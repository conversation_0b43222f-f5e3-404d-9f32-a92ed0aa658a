import React from 'react'
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'

import { useRouter } from 'expo-router'
import Chevron from '../atoms/icons/Chevron'
import { LinearGradient } from 'expo-linear-gradient'
import { Ionicons } from '@expo/vector-icons'
import { useNavigation } from '@react-navigation/native';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import { useChat } from '@/context/ChatContext'
import { triggerImpactMediumHaptic } from '@/utils/Haptics'


interface HeaderProps {
    burgerMenu?: boolean
    buttonBell?: boolean
    buttonLocation?: boolean
    text?: string
}


const Header = ({ buttonBell, buttonLocation, text, burgerMenu }: HeaderProps) => {
    const router = useRouter()
    const { handleNewChat, messages } = useChat();
    const navigation = useNavigation<DrawerNavigationProp<any>>();


    return (
        
            <View style={styles.container}>


                {
                    burgerMenu &&
                    <TouchableOpacity style={[styles.button, { transform: [{ translateY: 19 }, { translateX: 10 }] }]} onPress={() => {
                        if (burgerMenu) {
                            navigation.openDrawer()
                        }
                    }}>
                        <Ionicons name="menu" color={'white'} size={25} />
                    </TouchableOpacity>
                }
                {
                    text
                        ?
                        <Text style={styles.title}>{text}</Text>
                        :
                        <Image
                            source={require('@/assets/pictures/logo.jpeg')}
                            style={styles.image}
                        />
                }
                {
                    buttonLocation &&
                    <TouchableOpacity
                        style={[styles.button2, { transform: [{ translateY: 19 }, { translateX: -50 }]}]} onPress={() => {
                            if (buttonBell) {
                                triggerImpactMediumHaptic()
                                router.push('/dashboard/map')
                            }
                        }}
                    >
                        <Ionicons name="navigate-outline" color={'white'} size={25} />
                    </TouchableOpacity>
                }
                {
                    buttonBell &&
                    <TouchableOpacity
                        style={[styles.button2, { transform: [{ translateY: 19 }, { translateX: -10 }]}]} onPress={() => {
                            if (buttonBell) {
                                triggerImpactMediumHaptic()
                                router.push('/dashboard/notifications')
                            }
                        }}
                    >
                        <Ionicons name="notifications-outline" color={'white'} size={25} />
                    </TouchableOpacity>
                }

            </View>
        
    )
}

export default Header

const styles = StyleSheet.create({

    container: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        zIndex: 10,
        height: 100,
        backgroundColor: "#337836",
        borderBottomRightRadius: 20,
        borderBottomLeftRadius: 20,
        position: 'absolute',
        top: 0
    },
    image: {
        height: 26,
        objectFit: 'contain',
        marginBottom: 10
    },
    button: {
        position: 'absolute',
        width: 50,
        paddingVertical: 10,
        left: 10,
        transform: [{ rotate: '180deg' }, { translateY: -20 }], // <-- FIXED HERE
        zIndex: 10000,
        display: 'flex',
        alignItems: 'center',
        borderRadius: 10,
        justifyContent: 'center',
    },
    button2: {
        position: 'absolute',
        width: 50,
        paddingVertical: 10,
        right: 10,
        transform: [{ rotate: '180deg' }, { translateY: -20 }], // <-- FIXED HERE
        zIndex: 10000,
        display: 'flex',
        alignItems: 'center',
        borderRadius: 10,
        justifyContent: 'center',
    },
    title: {
        fontSize: 18,
        color: 'black',
        fontWeight: '600',
        transform: [{ translateY: 35 }], // <-- FIXED HERE
    },



})