"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const paymentTransactionSchema = new mongoose_1.default.Schema({
    jobId: {
        type: String,
        ref: 'Job',
        index: true
    },
    bidId: {
        type: String,
        ref: 'Bid',
        index: true
    },
    userId: {
        type: String,
        required: true,
        ref: 'Account'
    },
    paymentMethodId: {
        type: String,
        required: true,
        ref: 'PaymentMethod',
        index: true
    },
    amount: {
        type: Number,
        required: true,
        min: 1 // Minimum 1 cent/centavo
    },
    currency: {
        type: String,
        required: true,
        enum: ['DOP', 'USD'], // Dominican Peso and US Dollar
        default: 'DOP'
    },
    status: {
        type: String,
        enum: ['initiated', 'escrow', 'successful', 'failed', 'refunded', 'voided', 'requires_action', 'challenge_required'],
        default: 'initiated',
        required: true,
        set: (v) => {
            if (v === null || typeof v === 'undefined')
                return v;
            const s = String(v).toLowerCase();
            if (s === 'requiresaction' || s === 'requires_action' || s === 'requires-action')
                return 'requires_action';
            if (s === 'challengerequired' || s === 'challenge_required' || s === 'challenge-required')
                return 'challenge_required';
            if (['initiated', 'escrow', 'successful', 'failed', 'refunded', 'voided'].includes(s))
                return s;
            // Return the original value to trigger enum validation error if invalid
            return v;
        }
    },
    type: {
        type: String,
        enum: ['Sale', 'Hold'],
        required: true,
        set: (v) => {
            if (v === null || typeof v === 'undefined')
                return v;
            const s = String(v).toLowerCase();
            if (s === 'sale')
                return 'Sale';
            if (s === 'hold')
                return 'Hold';
            // Return the original value to trigger enum validation if invalid
            return v;
        }
    },
    providerReference: {
        type: String,
        required: true
    },
    threeDSServerTransID: {
        type: String,
        index: true
    },
    // Track total refunded amount to allow atomic refund guards
    refundedAmount: {
        type: Number,
        default: 0
    },
    // Persist raw ThreeDS method/challenge payloads as needed (do not store sensitive data like PAN)
    // …other fields…
    threeDSMethod: {
        threeDSMethodURL: String,
        threeDSServerTransID: String
    },
    threeDS: {
        acsURL: String,
        acsTransID: String,
        challengeURL: String
    },
    // Raw 3DS challenge data from Azul
    threeDSChallenge: {
        type: mongoose_1.default.Schema.Types.Mixed
    },
    // Structured challenge data for client handling
    challengeData: {
        challengeForm: String,
        challengeUrl: String,
        challengeWindowSize: String,
        threeDSServerTransID: String,
        createdAt: Date
    },
    // …following fields…  },
    idempotencyKey: {
        type: String,
        required: true,
        // uniqueness enforced by compound index with userId below
    },
    // Commission & taxes breakdown
    commissionAmount: {
        type: Number,
        default: 0,
        min: 0
    },
    commissionTaxAmount: {
        type: Number,
        default: 0,
        min: 0
    },
    azulResponse: {
        isoCode: String,
        responseMessage: String,
        authCode: String,
        rrn: String,
        originalAmount: Number
    },
    refunds: [{
            amount: {
                type: Number,
                required: true,
                min: 1
            },
            reason: {
                type: String,
                required: true,
                maxlength: 500
            },
            refundId: {
                type: String,
                required: true
            },
            processedAt: {
                type: Date,
                default: Date.now
            }
        }]
}, {
    timestamps: true
});
// Ensure total refunds do not exceed the original transaction amount
paymentTransactionSchema.pre('save', function (next) {
    // `this` is the document being saved
    const doc = this;
    try {
        const refunds = Array.isArray(doc.refunds) ? doc.refunds : [];
        const totalRefunded = refunds.reduce((sum, r) => sum + (Number(r === null || r === void 0 ? void 0 : r.amount) || 0), 0);
        if (typeof doc.amount === 'number' && totalRefunded > doc.amount) {
            return next(new Error('Total refunded amount cannot exceed original transaction amount'));
        }
        return next();
    }
    catch (err) {
        return next(err);
    }
});
// Indexes for efficient querying
paymentTransactionSchema.index({ userId: 1, createdAt: -1 });
paymentTransactionSchema.index({ status: 1, createdAt: -1 });
// Enforce uniqueness of idempotency keys per user
paymentTransactionSchema.index({ userId: 1, idempotencyKey: 1 }, { unique: true });
// providerReference should be unique to avoid duplicate external order ids
paymentTransactionSchema.index({ providerReference: 1 }, { unique: true });
exports.default = mongoose_1.default.model('PaymentTransaction', paymentTransactionSchema);
