import cors from 'cors';
import chalk from 'chalk';
import express from 'express';
import { errorHandler } from './middleware';

import { notifications, oauth, chat, job, jobCategory, bid, operatorProfile, operatorApplication, admin, payments, clients, cancellation } from './routes';
import connectDatabase from './database/connection';


// Create express app
const app = express();

// Middlewares
app.use(cors());
app.use(express.json());
app.use(errorHandler);

// Routes
app.use('/notifications', notifications);
app.use('/oauth', oauth);
app.use('/chat', chat);
app.use('/jobs', job);
app.use('/job-categories', jobCategory);
app.use('/bids', bid);
app.use('/operators', operatorProfile);
app.use('/operator-app', operatorApplication);
app.use('/admin', admin);
app.use('/payments', payments);
app.use('/clients', clients);
app.use('/cancellation', cancellation);


const startServer = async () => {
  if (process.env.NODE_ENV === 'development') {
    console.log(chalk.green(`[SERVER] Starting`));
  }
  const PORT = process.env.PORT || 4000;

  try {
    // Await database connection
    await connectDatabase();

    if (process.env.NODE_ENV === 'development') {
      console.log(chalk.green(`[SERVER] Database Connected`));
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(chalk.green(`[SERVER] Default data initialized`));
    }


    // Start the server after successful database connection
    const server = app.listen(PORT, () => {
      if (process.env.NODE_ENV === 'development') {
        console.log(chalk.green(`[SERVER] Running on`), chalk.yellow.underline(`http://localhost:${PORT}`));
      }
    });

    // Optionally schedule nightly no-show processing. Disabled by default to avoid
    // duplicate side-effectful runs in multi-replica deployments. To enable set
    // the environment variable ENABLE_NO_SHOW_CRON='true' in production.
    //
    // Note: a better long-term approach is to run this job from an external
    // scheduler (Cloud Scheduler, cron job, Kubernetes CronJob) or protect the
    // in-process task with a distributed lock (Redis, etcd) so only one replica
    // executes the job at a time.
    const enableNoShowCron = process.env.ENABLE_NO_SHOW_CRON === 'true';
    const isProd = process.env.NODE_ENV === 'production';

    if (enableNoShowCron && isProd) {
      try {
        const { processOperatorNoShows } = await import('./services/noShowCron');

        // Schedule a daily job at local 03:00 using a recalculated setTimeout chain.
        // This avoids overlapping runs, DST drift, and keeps timers from keeping Node alive (.unref()).
        const scheduleNextRun = () => {
          const now = new Date();
          const next = new Date(now);
          next.setHours(3, 0, 0, 0);
          if (next.getTime() <= now.getTime()) {
            // already past today's 03:00, schedule for tomorrow
            next.setDate(next.getDate() + 1);
          }

          const delay = next.getTime() - now.getTime();
          const timer = setTimeout(async () => {
            try {
              await processOperatorNoShows();
            } catch (err) {
              console.error('No-show cron failed:', err);
            } finally {
              // After the run completes (success or failure), schedule the next run.
              try {
                scheduleNextRun();
              } catch (schedErr) {
                console.error('Failed to schedule next no-show run:', schedErr);
              }
            }
          }, delay);

          // Do not keep the process alive solely because of this timer
          if (typeof (timer as any).unref === 'function') (timer as any).unref();
        };

        // Kick off the scheduling chain
        scheduleNextRun();
        console.log('No-show cron enabled and scheduled');
      } catch (e) {
        console.warn('No-show cron failed to initialize:', e);
      }
    } else {
      console.log('No-show cron disabled. To enable set ENABLE_NO_SHOW_CRON=true in production.');
    }


    /* Handle unhandled promise rejections */
    process.on('unhandledRejection', (err) => {
      if (err instanceof Error) {
        if (process.env.NODE_ENV === 'development') {
          console.log(chalk.red(`[ERROR] Unhandled Rejection: ${err.message}`));
        }
      }
      // Close server & exit process
      server.close(() => process.exit(1));
    });
  } catch (error: any) {
    if (process.env.NODE_ENV === 'development') {
      console.log(chalk.red(`[DATABASE] Connection failed: ${error.message}`));
    }
    process.exit(1);
  }
};


startServer();