"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreditTransaction = exports.CreditBalance = void 0;
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const creditBalanceSchema = new mongoose_1.default.Schema({
    userId: {
        type: String,
        required: true,
        ref: 'Account',
        unique: true, // One credit balance per user
        index: true
    },
    balance: {
        type: Number,
        required: true,
        default: 0,
        min: 0 // Balance cannot be negative
    },
    currency: {
        type: String,
        required: true,
        enum: ['DOP', 'USD'],
        default: 'DOP'
    },
    totalCredits: {
        type: Number,
        required: true,
        default: 0,
        min: 0
    },
    totalWithdrawals: {
        type: Number,
        required: true,
        default: 0,
        min: 0
    },
    pendingWithdrawals: {
        type: Number,
        required: true,
        default: 0,
        min: 0
    },
    lastTransactionAt: {
        type: Date,
        default: null,
        index: true
    }
}, {
    timestamps: true
});
const creditTransactionSchema = new mongoose_1.default.Schema({
    userId: {
        type: String,
        required: true,
        ref: 'Account',
        index: true
    },
    creditBalanceId: {
        type: String,
        required: true,
        ref: 'CreditBalance',
        index: true
    },
    type: {
        type: String,
        required: true,
        enum: ['credit', 'debit', 'withdrawal', 'refund', 'penalty_compensation'],
        index: true
    },
    amount: {
        type: Number,
        required: true
    },
    currency: {
        type: String,
        required: true,
        enum: ['DOP', 'USD'],
        default: 'DOP'
    },
    status: {
        type: String,
        required: true,
        enum: ['pending', 'completed', 'failed', 'cancelled'],
        default: 'pending',
        index: true
    },
    description: {
        type: String,
        required: true,
        maxlength: 500
    },
    metadata: {
        jobId: { type: String, ref: 'Job' },
        bidId: { type: String, ref: 'Bid' },
        cancellationId: { type: String, ref: 'CancellationRecord' },
        paymentTransactionId: { type: String, ref: 'PaymentTransaction' },
        withdrawalMethod: {
            type: String,
            enum: ['credit_card', 'bank_transfer']
        },
        originalPaymentMethodId: { type: String, ref: 'PaymentMethod' },
        processingFee: { type: Number, min: 0 },
        reason: { type: String, maxlength: 1000 }
    },
    processedAt: {
        type: Date,
        default: null,
        index: true
    }
}, {
    timestamps: true
});
// Indexes for efficient querying
creditBalanceSchema.index({ userId: 1 });
creditTransactionSchema.index({ userId: 1, createdAt: -1 });
creditTransactionSchema.index({ type: 1, status: 1 });
creditTransactionSchema.index({ 'metadata.jobId': 1 });
exports.CreditBalance = mongoose_1.default.model('CreditBalance', creditBalanceSchema);
exports.CreditTransaction = mongoose_1.default.model('CreditTransaction', creditTransactionSchema);
exports.default = exports.CreditBalance;
