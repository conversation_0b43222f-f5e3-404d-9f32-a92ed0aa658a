"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_1 = require("../middleware/authentication");
const payments_1 = require("../controllers/payments");
const router = (0, express_1.Router)();
// Payment Methods Management
router.post('/methods', authentication_1.AuthenticateTokenOAuth, payments_1.createPaymentMethod);
router.get('/methods', authentication_1.AuthenticateTokenOAuth, payments_1.getPaymentMethods);
router.patch('/methods/:id/favorite', authentication_1.AuthenticateTokenOAuth, payments_1.setFavoritePaymentMethod);
router.delete('/methods/:id', authentication_1.AuthenticateTokenOAuth, payments_1.deletePaymentMethod);
// Payment Processing
router.post('/methods/:id/charge', authentication_1.AuthenticateTokenOAuth, payments_1.chargePaymentMethod);
// 3DS Flow Endpoints
router.post('/3ds/method-notification/:orderId', payments_1.handle3DSMethodNotification); // No auth - called by ACS
router.post('/3ds/challenge/:transactionId', authentication_1.AuthenticateTokenOAuth, payments_1.process3DSChallenge);
// Transaction Management
router.post('/payments/:transactionId/refund', authentication_1.AuthenticateTokenOAuth, payments_1.refundTransaction);
router.post('/payments/:transactionId/capture', authentication_1.AuthenticateTokenOAuth, payments_1.captureTransaction);
// Job/Bid payments flow
const jobPayments_1 = require("../controllers/jobPayments");
router.post('/jobs/:jobId/process-escrow', authentication_1.AuthenticateTokenOAuth, jobPayments_1.processEscrow);
router.post('/jobs/:jobId/complete', authentication_1.AuthenticateTokenOAuth, jobPayments_1.completeJobAndPayout);
router.post('/jobs/:jobId/cancel', authentication_1.AuthenticateTokenOAuth, jobPayments_1.cancelJobAndRefund);
// Payouts & taxes
const taxPayout_1 = require("../controllers/taxPayout");
router.get('/payouts', authentication_1.AuthenticateTokenOAuth, taxPayout_1.listPayouts);
router.get('/taxes', authentication_1.AuthenticateTokenOAuth, taxPayout_1.listTaxes);
exports.default = router;
