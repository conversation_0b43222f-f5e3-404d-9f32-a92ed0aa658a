import React, { useContext, useEffect, useRef, useState } from 'react'
import { Animated, Image, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native'
import { Ionicons } from "@expo/vector-icons"

import Page from '@/components/templates/Page'
import { ScrollView } from 'react-native-gesture-handler'
import Header from '@/components/templates/Header'
import ButtonGlobal from '@/components/atoms/buttons/ButtonGlobal'
import { AuthContext } from '@/context/AuthContext'
import { updateAccount } from '@/services/api'
import Toast from 'react-native-toast-message'

interface UserProfile {
  name: string
  surname: string
  username: string
  timezone: string
  email: string
  joinDate: string
  profileImage?: string
}

const timezones = [
  { label: "Pacific Time (PT)", value: "America/Los_Angeles" },
  { label: "Mountain Time (MT)", value: "America/Denver" },
  { label: "Central Time (CT)", value: "America/Chicago" },
  { label: "Eastern Time (ET)", value: "America/New_York" },
  { label: "Greenwich Mean Time (GMT)", value: "Europe/London" },
  { label: "Central European Time (CET)", value: "Europe/Paris" },
  { label: "Japan Standard Time (JST)", value: "Asia/Tokyo" },
  { label: "Australian Eastern Time (AET)", value: "Australia/Sydney" },
]




const Index = () => {
  const { userData, sessionAuthentication } = useContext(AuthContext)
  const [profile, setProfile] = useState<UserProfile>({
    name: userData?.user.name ?? '',
    surname: userData?.user.surname ?? '',
    username: userData?.user.username ?? '',
    timezone: 'Europe/Paris',
    email: userData?.email ?? '',
    joinDate: "January 2025",
  })

  const [isEditing, setIsEditing] = useState(true)
  const [showTimezoneSelector, setShowTimezoneSelector] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  const pulseAnim = useRef(new Animated.Value(1)).current


  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    ).start()
  }
  const getTimezoneLabel = (value: string) => {
    return timezones.find((tz) => tz.value === value)?.label || value
  }

  const updateProfile = (field: keyof UserProfile, value: string) => {
    setProfile((prev) => ({ ...prev, [field]: value }))
    setHasChanges(true)
  }
  const handleSave = async () => {
    const call = await updateAccount(
      profile.name,
      profile.surname,
      profile.username
    )

  

    setHasChanges(false)

    if (!call.sucess) {
      Toast.show({
        type: 'error',
        text1: 'Errore',
        text2: call.message
      })
    }

    sessionAuthentication()
    Toast.show({
      type: 'success',
      text1: 'Succeso',
      text2: 'Informaciones perfil actualizadas'
    })




  }




  useEffect(() => {
    startPulseAnimation()
  }, [])



  return (
    <Page noPaddingTop alignItems='center' justifyContent='space-between' >
      <Header buttonBack text=' ' />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>

        {/* Profile Fields */}
        <View style={styles.fieldsContainer}>
          {/* Name */}
          <View style={styles.fieldCard}>
            <View style={styles.fieldHeader}>

              <Text style={styles.fieldLabel}>First Name</Text>
            </View>
            {isEditing ? (
              <TextInput
                style={styles.fieldInput}
                value={profile.name}
                onChangeText={(text) => updateProfile("name", text)}
                placeholder="Enter your first name"
                placeholderTextColor="#999"
              />
            ) : (
              <Text style={styles.fieldValue}>{profile.name}</Text>
            )}
          </View>

          {/* Surname */}
          <View style={styles.fieldCard}>
            <View style={styles.fieldHeader}>

              <Text style={styles.fieldLabel}>Last Name</Text>
            </View>
            {isEditing ? (
              <TextInput
                style={styles.fieldInput}
                value={profile.surname}
                onChangeText={(text) => updateProfile("surname", text)}
                placeholder="Enter your last name"
                placeholderTextColor="#999"
              />
            ) : (
              <Text style={styles.fieldValue}>{profile.surname}</Text>
            )}
          </View>

          {/* Username */}
          <View style={styles.fieldCard}>
            <View style={styles.fieldHeader}>
              <Text style={styles.fieldLabel}>Username</Text>
            </View>
            {isEditing ? (
              <TextInput
                style={styles.fieldInput}
                value={profile.username}
                onChangeText={(text) => updateProfile("username", text.toLowerCase().replace(/[^a-z0-9_]/g, ""))}
                placeholder="Enter your username"
                placeholderTextColor="#999"
                autoCapitalize="none"
              />
            ) : (
              <Text style={styles.fieldValue}>@{profile.username}</Text>
            )}
          </View>

          {/* Email (Read-only) */}
          <View style={styles.fieldCard}>
            <View style={styles.fieldHeader}>
              <Text style={styles.fieldLabel}>Email</Text>
            </View>
            <Text style={styles.fieldValue}>{profile.email}</Text>
            <Text style={styles.fieldHint}>Email cannot be changed</Text>
          </View>

          {/* Timezone */}
          <TouchableOpacity
            style={styles.fieldCard}
            onPress={() => isEditing && setShowTimezoneSelector(!showTimezoneSelector)}
            disabled={!isEditing}
          >
            <View style={styles.fieldHeader}>
              <Text style={styles.fieldLabel}>Timezone</Text>
              {isEditing && (
                <Ionicons name={showTimezoneSelector ? "chevron-up" : "chevron-down"} size={20} color="#666" />
              )}
            </View>
            <Text style={styles.fieldValue}>{getTimezoneLabel(profile.timezone)}</Text>

            {showTimezoneSelector && isEditing && (
              <View style={styles.timezoneSelector}>
                {timezones.map((timezone) => (
                  <TouchableOpacity
                    key={timezone.value}
                    style={[styles.timezoneOption, profile.timezone === timezone.value && styles.selectedTimezone]}
                    onPress={() => {
                      updateProfile("timezone", timezone.value)
                      setShowTimezoneSelector(false)
                    }}
                  >
                    <Text
                      style={[styles.timezoneText, profile.timezone === timezone.value && styles.selectedTimezoneText]}
                    >
                      {timezone.label}
                    </Text>
                    {profile.timezone === timezone.value && <Ionicons name="checkmark" size={20} color="rgba(34,197,94,0.7)" />}
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </TouchableOpacity>
        </View>

        {/* Save Button */}
        {isEditing && hasChanges && (
          <View style={{ paddingHorizontal: 15, marginBottom: 30, marginTop: 20 }}>
            <ButtonGlobal
              text='Save Changes'
              onPress={handleSave}
              style={{
                backgroundColor: "rgba(34,197,94,0.7)"
              }}
              icon={<Ionicons name="checkmark-circle" size={20} color="#fff" />}
            />
          </View>
        )}


      </ScrollView>
    </Page>
  )
}


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",

    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e9ecef",
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
    color: "#333",
  },
  editButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  editButtonText: {
    fontSize: 16,
    color: "rgba(34,197,94,0.7)",
    fontWeight: "500",
  },
  scrollView: {
    flex: 1,
    width: '100%'
  },
  photoSection: {
    backgroundColor: "#fff",
    paddingVertical: 40,
    paddingHorizontal: 24,

    alignItems: "center",
    marginBottom: 0,
  },
  photoContainer: {
    marginBottom: 16,
  },
  photoWrapper: {
    position: "relative",
  },
  photoPlaceholder: {
    width: 190,
    height: 190,
    borderRadius: 60,
    backgroundColor: "#f8f9fa",
    borderWidth: 4,
    borderColor: "rgba(34,197,94,0.7)",
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "rgba(34,197,94,0.7)",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  photoOverlay: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "rgba(34,197,94,0.7)",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 3,
    borderColor: "#fff",
  },
  photoHint: {
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
  },
  memberSince: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  memberSinceText: {
    fontSize: 12,
    color: "#666",
  },
  fieldsContainer: {
    marginTop: 120,
    paddingHorizontal: 0,
    gap: 16,
  },
  fieldCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    paddingHorizontal: 20,
    marginBottom: 10
  },
  fieldHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
    gap: 12,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
    flex: 1,
  },
  fieldValue: {
    fontSize: 16,
    color: "#333",
    fontWeight: "500",
  },
  fieldInput: {
    fontSize: 16,
    color: "#333",
    fontWeight: "500",
    borderBottomWidth: 1,
    borderBottomColor: "#e9ecef",
    paddingBottom: 8,
  },
  fieldHint: {
    fontSize: 12,
    color: "#999",
    marginTop: 8,
  },
  timezoneSelector: {
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: "#e9ecef",
    paddingTop: 16,
  },
  timezoneOption: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
  },
  selectedTimezone: {
    backgroundColor: "#f0f9f0",
  },
  timezoneText: {
    fontSize: 14,
    color: "#333",
    flex: 1,
  },
  selectedTimezoneText: {
    color: "rgba(34,197,94,0.7)",
    fontWeight: "500",
  },
  saveSection: {
    padding: 20,
  },
  saveButton: {
    backgroundColor: "rgba(34,197,94,0.7)",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  actionsSection: {
    marginTop: 20,
    paddingBottom: 32,
    gap: 12,
  },
  actionButton: {
    backgroundColor: "#fff",
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 12,
  },
  actionButtonText: {
    fontSize: 16,
    color: "#333",
    flex: 1,
  },
  dangerButton: {
    marginTop: 8,
  },
  dangerText: {
    color: "#F44336",
  },
})

export default Index

