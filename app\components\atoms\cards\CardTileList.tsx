import { StyleSheet, Text, View, Dimensions, TouchableOpacity } from 'react-native'
import React from 'react'

const { width } = Dimensions.get('window');
const TILE_SIZE = (width - 50) * 0.5;


interface CardTileProps {
  onPress?: () => void
  children: React.ReactNode
}

const CardTileList = ({ onPress, children }: CardTileProps) => {
  return (
    <TouchableOpacity
      style={styles.component}
      activeOpacity={0.7}
      onPress={() => {
        onPress && onPress()
      }}
    >
      {children}
    </TouchableOpacity>
  )

}

export default CardTileList

const styles = StyleSheet.create({
  component: {
    width: TILE_SIZE,
    height: TILE_SIZE,

    backgroundColor: '#e8e8e8ff',
    borderRadius: 20,
    zIndex:10,

    flexDirection: 'column',
    justifyContent: 'flex-end',

    
  }
})