import express from 'express';
import { AuthenticateTokenOAuth, requireAdmin, validateUserId, validateOperatorId } from '../middleware';
import { tryCatch } from '../middleware';
import {
  getAllUsers,
  createUser,
  updateUser,
  deleteUser,
  getAllOperators,
  createOperator,
  updateOperator,
  deleteOperator,
  getSystemStats
} from '../controllers/admin';

const router = express.Router();

// Apply authentication and admin requirement to all routes
router.use(AuthenticateTokenOAuth);
router.use(requireAdmin);

// System stats
router.get('/stats', tryCatch(getSystemStats));

// User management
router.get('/users', tryCatch(getAllUsers));
router.post('/users', tryCatch(createUser));
router.put('/users/:userId', validateUserId, tryCatch(updateUser));
router.delete('/users/:userId', validateUserId, tryCatch(deleteUser));

// Operator management
router.get('/operators', tryCatch(getAllOperators));
router.post('/operators', tryCatch(createOperator));
router.put('/operators/:operatorId', validateOperatorId, tryCatch(updateOperator));
router.delete('/operators/:operatorId', validateOperatorId, tryCatch(deleteOperator));

export default router;
