"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const schemas_1 = require("../database/schemas");
const middleware_1 = require("../middleware");
const express_1 = tslib_1.__importDefault(require("express"));
const oauth_1 = require("../controllers/oauth");
const utils_1 = require("../utils");
const router = express_1.default.Router();
router.post("/login/apple", (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const { identityToken } = req.body;
        if (!identityToken) {
            return res.status(400).json({ success: false, error: "Missing identityToken" });
        }
        const payload = yield (0, oauth_1.verifyAppleIdentityToken)(identityToken);
        const appleId = payload.sub;
        const emailApple = payload.email;
        let account = yield schemas_1.Account.findOne({ email: emailApple, password: (0, utils_1.hashPassword)(appleId) });
        if (!account) {
            console.log("Creating new account for Apple user:", emailApple);
            account = new schemas_1.Account({
                email: emailApple,
                password: (0, utils_1.hashPassword)(appleId), // no password required for Apple users or use appleId
                user: {
                    name: "",
                    surname: "",
                    username: `apple_${appleId.slice(-6)}`,
                    profile_picture: "",
                    birthdate: "",
                    type: "user",
                },
                booleans: {
                    isVerified: true,
                    isAdmin: false,
                },
                // Other fields will fall back to schema defaults
            });
            yield account.save();
        }
        const token = (0, middleware_1.signTokenOAuth)(emailApple, String(account._id));
        res.status(200).json({ success: true, token, account });
    }
    catch (error) {
        console.error("Apple login error:", error);
        res.status(401).json({ success: false, error: "Invalid or expired identity token" });
    }
}));
router.post("/login/google", (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id, email, name, surname, photo } = req.body;
        let account = yield schemas_1.Account.findOne({ email: email, password: (0, utils_1.hashPassword)(id) });
        if (!account) {
            console.log("Creating new account for Google user:", email);
            account = new schemas_1.Account({
                email: email,
                password: (0, utils_1.hashPassword)(id), // no password required for Apple users or use appleId
                user: {
                    name: name !== null && name !== void 0 ? name : "",
                    surname: surname !== null && surname !== void 0 ? surname : "",
                    username: `${name}-${(0, utils_1.generateRandomNumberString)(4)}`,
                    profile_picture: photo,
                    birthdate: "",
                    type: "user",
                },
                booleans: {
                    isVerified: true,
                    isAdmin: false,
                },
                // Other fields will fall back to schema defaults
            });
            yield account.save();
        }
        const token = (0, middleware_1.signTokenOAuth)(email, String(account._id));
        res.status(200).json({ success: true, token, account });
    }
    catch (error) {
        console.error("Google login error:", error);
        res.status(401).json({ success: false, error: "Invalid or expired identity token" });
    }
}));
// Development only route - authenticates first user in database
router.post("/dev/authenticate", (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        // Find the first user in the database
        const firstUser = yield schemas_1.Account.findOne();
        if (!firstUser) {
            return res.status(404).json({
                success: false,
                error: "No users found in database"
            });
        }
        // Generate token for the first user
        const token = (0, middleware_1.signTokenOAuth)(firstUser.email, String(firstUser._id));
        // Ensure this endpoint is only available in development
        if (process.env.NODE_ENV !== 'development') {
            return res.status(404).json({
                success: false,
                error: "Endpoint not found"
            });
        }
        res.status(200).json({
            success: true,
            message: "Dev authentication successful",
            token,
            account: firstUser
        });
    }
    catch (error) {
        console.error("Dev authentication error:", error);
        res.status(500).json({
            success: false,
            error: "Internal server error during dev authentication"
        });
    }
}));
router.route("/authenticate").get(middleware_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)((req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    if (!req.user) {
        return res
            .status(401)
            .json({ success: false, message: "Auth user not found" });
    }
    res
        .status(200)
        .json({ success: true, message: "Authorized Access", data: req.user });
})));
router.route("/update/account").post(middleware_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)((req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    if (!req.user) {
        return res
            .status(400)
            .json({ success: false, message: "Auth user not found" });
    }
    const { name, surname, username } = req.body;
    const userId = req.user._id;
    // Basic normalization (adjust to your rules)
    let normalized = {
        name: typeof name === 'string' ? name.trim() : undefined,
        surname: typeof surname === 'string' ? surname.trim() : undefined,
        username: typeof username === 'string' ? username.trim().toLowerCase() : undefined,
    };
    // Treat empty strings as no-ops
    if (normalized.name === '')
        normalized.name = undefined;
    if (normalized.surname === '')
        normalized.surname = undefined;
    if (normalized.username === '')
        normalized.username = undefined;
    // Basic username format: 3-30 chars, alnum plus . _ -
    if (normalized.username !== undefined &&
        !/^[a-z0-9._-]{3,30}$/.test(normalized.username)) {
        return res.status(400).json({ success: false, message: "Invalid username format" });
    }
    const $set = {};
    if (normalized.name !== undefined)
        $set['user.name'] = normalized.name;
    if (normalized.surname !== undefined)
        $set['user.surname'] = normalized.surname;
    if (normalized.username !== undefined)
        $set['user.username'] = normalized.username;
    const updated = yield schemas_1.Account.findByIdAndUpdate(userId, { $set }, { new: true, runValidators: true, select: 'user' });
    if (!updated) {
        return res.status(404).json({ success: false, message: "User not found" });
    }
    res
        .status(200)
        .json({ success: true });
})));
exports.default = router;
