# Azul Deletion Retry System

This system handles failed Azul Data Vault token deletions by implementing a robust retry queue with exponential backoff.

## Overview

When a payment method is deleted locally but the corresponding Azul Data Vault token deletion fails, the system automatically enqueues the deletion for retry instead of just logging the failure.

## Components

### 1. Database Schema (`_azulDeletionRetryJob.ts`)
- Stores retry job information with encrypted token data
- Tracks attempt count, error history, and retry scheduling
- Auto-cleanup of completed jobs after 30 days

### 2. Retry Service (`azulDeletionRetryService.ts`)
- Manages retry job lifecycle
- Handles encryption/decryption of sensitive token data
- Implements exponential backoff strategy
- Processes retry jobs on configurable intervals

### 3. Payment Method Service Integration
- Automatically enqueues failed deletions
- Continues with local deletion even if A<PERSON><PERSON> fails
- Logs comprehensive error information

## Configuration

### Environment Variables
```bash
# Required: Encryption key for token storage
AZUL_TOKEN_ENCRYPTION_KEY=your-secure-encryption-key-here

# Optional: Node environment (affects retry service instance)
NODE_ENV=production
```

### Retry Settings (configurable in service)
- **Default Max Attempts**: 5
- **Base Delay**: 5 seconds
- **Max Delay**: 5 minutes
- **Processing Interval**: 30 seconds
- **Exponential Backoff**: `delay = baseDelay * 2^(attemptCount-1)`

## Usage

### Initialization
Add to your application startup (e.g., `src/index.ts`):

```typescript
import { initializeAzulRetryProcessor } from './utils/azulRetryInit';

// Start the retry processor
initializeAzulRetryProcessor();
```

### Monitoring
Get retry statistics:

```typescript
import { getRetryProcessorStats } from './utils/azulRetryInit';

const stats = await getRetryProcessorStats();
console.log(stats); // { pending: 2, processing: 0, succeeded: 15, failed: 1 }
```

### Manual Operations
```typescript
import { getAzulDeletionRetryService } from './services/azulDeletionRetryService';

const retryService = getAzulDeletionRetryService('production');

// Manually enqueue a retry
await retryService.enqueueRetry({
  paymentMethodId: 'pm_123',
  dataVaultToken: 'token_abc',
  userId: 'user_456',
  maxAttempts: 3,
  baseDelayMs: 10000
});

// Process jobs immediately
await retryService.processRetryJobs();

// Get detailed statistics
const stats = await retryService.getRetryStats();
```

## Security Considerations

### Token Encryption
- Sensitive Azul tokens are encrypted using AES-256-GCM before storage
- Encryption key must be securely managed and rotated regularly
- Fallback to base64 encoding if encryption fails (not recommended for production)

### Token Rotation
- Failed deletion jobs should be monitored for permanently failed tokens
- Consider implementing token lifecycle management
- Alert operations team when max attempts are reached

## Monitoring & Alerting

### Key Metrics to Monitor
- Number of pending retry jobs
- Number of permanently failed jobs
- Average retry success rate
- Time to successful deletion

### Alerts
- Permanently failed deletions (requires manual intervention)
- High number of pending retries (potential Azul service issues)
- Retry service processing failures

## Error Handling

### Automatic Handling
- **Transient failures**: Automatically retried with exponential backoff
- **Permanent failures**: Marked as failed after max attempts
- **Service failures**: Logged and processing continues

### Manual Intervention Required
- Jobs marked as permanently failed need manual review
- Check Azul service status for systematic failures
- Consider token cleanup for orphaned entries

## Database Indexes

The system creates several indexes for optimal performance:
- Status + nextRetryAt (for job processing)
- PaymentMethodId (for preventing duplicates)
- UserId (for user-specific queries)

## Cleanup

Completed jobs are automatically removed after 30 days via MongoDB TTL indexes. Failed jobs are retained for audit purposes until manual cleanup.
