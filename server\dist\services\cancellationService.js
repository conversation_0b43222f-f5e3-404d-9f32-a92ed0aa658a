"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CancellationService = void 0;
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const schemas_1 = require("../database/schemas");
const creditBalanceService_1 = require("./creditBalanceService");
const paymentProcessorService_1 = require("./paymentProcessorService");
const parseJobDateTime_1 = require("../utils/parseJobDateTime");
const logger_1 = require("../utils/logger");
const cancellationErrors_1 = require("../errors/cancellationErrors");
class CancellationService {
    constructor() {
        // Default penalty configuration
        this.penaltyConfig = {
            clientCancellationWorkerCompensation: 40, // 40%
            clientCancellationPlatformFee: 12, // 12%
            operatorCancellationFinancialPenalty: 500, // 500 DOP fixed
            operatorCancellationRatingPenalty: 0.2, // 0.2 points
            operatorAfterArrivalFinancialPenalty: 1000, // 1000 DOP fixed
            operatorAfterArrivalRatingPenalty: 0.5, // 0.5 points
            ratingPenaltyConfigurable: true,
            enableAutomaticPenalties: true
        };
        this.creditBalanceService = creditBalanceService_1.CreditBalanceService.getInstance();
        this.paymentProcessor = paymentProcessorService_1.PaymentProcessorService.getInstance();
    }
    static getInstance() {
        if (!CancellationService.instance) {
            CancellationService.instance = new CancellationService();
        }
        return CancellationService.instance;
    }
    /**
     * Process operator cancellation before job start
     */
    processOperatorCancellationBeforeStart(request) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const session = yield mongoose_1.default.startSession();
            try {
                return yield session.withTransaction(() => tslib_1.__awaiter(this, void 0, void 0, function* () {
                    // Validate request and get job details
                    const { job, bid, escrowTx } = yield this.validateCancellationRequest(request, 'operator');
                    // Check timing - must be before job start
                    const scheduledDateTime = (0, parseJobDateTime_1.parseJobDateTime)(job.date, job.hour, 'America/Santo_Domingo');
                    const now = new Date();
                    if (now >= scheduledDateTime) {
                        throw new cancellationErrors_1.CancellationNotAllowedError('Cannot cancel after job start time');
                    }
                    // Calculate penalty amounts
                    const penaltyAmount = this.penaltyConfig.operatorCancellationFinancialPenalty;
                    const refundAmount = escrowTx.amount; // Full refund to client
                    // Create cancellation record
                    const cancellationRecord = yield schemas_1.CancellationRecord.create([{
                            jobId: request.jobId,
                            bidId: bid ? String(bid._id) : undefined,
                            initiatedBy: request.initiatedBy,
                            initiatorRole: 'operator',
                            type: 'operator_before_start',
                            status: 'processing',
                            scheduledStartTime: scheduledDateTime,
                            cancellationTime: now,
                            hoursBeforeStart: (scheduledDateTime.getTime() - now.getTime()) / (1000 * 60 * 60),
                            originalAmount: escrowTx.amount,
                            penaltyAmount,
                            refundAmount,
                            adminShare: 0,
                            operatorShare: 0,
                            currency: escrowTx.currency,
                            reason: request.reason,
                            explanation: request.explanation,
                            evidenceUrls: request.evidenceUrls || [],
                            paymentTransactionIds: [String(escrowTx._id)],
                            creditTransactionIds: [],
                            metadata: {
                                clientNotified: false,
                                operatorNotified: false,
                                ratingImpactApplied: false,
                                jobReposted: false,
                                rescheduleOffered: true
                            }
                        }], { session });
                    // Process financial penalty for operator
                    if (penaltyAmount > 0) {
                        const operatorPenalty = yield this.createOperatorPenalty({
                            operatorId: request.initiatedBy,
                            jobId: request.jobId,
                            bidId: bid ? String(bid._id) : undefined,
                            cancellationId: String(cancellationRecord[0]._id),
                            type: 'cancellation_before_start',
                            severity: 'moderate',
                            financialPenalty: penaltyAmount,
                            ratingPenalty: this.penaltyConfig.operatorCancellationRatingPenalty,
                            reason: request.reason,
                            description: `Operator cancelled job before start time. Penalty: ${penaltyAmount} ${escrowTx.currency}`,
                            session
                        });
                        yield schemas_1.CancellationRecord.findByIdAndUpdate(cancellationRecord[0]._id, { operatorPenaltyId: String(operatorPenalty._id) }, { session });
                    }
                    // Process refund to client's credit balance
                    const creditTransaction = yield this.creditBalanceService.addCredit({
                        userId: String(job.ownerId),
                        type: 'refund',
                        amount: refundAmount,
                        currency: escrowTx.currency,
                        description: `Refund for job cancellation by operator`,
                        metadata: {
                            jobId: request.jobId,
                            bidId: bid ? String(bid._id) : undefined,
                            cancellationId: String(cancellationRecord[0]._id),
                            paymentTransactionId: String(escrowTx._id)
                        }
                    });
                    // Update job status
                    yield schemas_1.Job.findByIdAndUpdate(request.jobId, {
                        status: 'cancelled',
                        cancelledAt: now,
                        cancelReason: request.reason
                    }, { session });
                    // Update cancellation record
                    yield schemas_1.CancellationRecord.findByIdAndUpdate(cancellationRecord[0]._id, {
                        status: 'completed',
                        processedAt: now,
                        creditTransactionIds: [String(creditTransaction._id)],
                        'metadata.clientNotified': true,
                        'metadata.operatorNotified': true,
                        'metadata.rescheduleOffered': true
                    }, { session });
                    logger_1.logger.info(`Processed operator cancellation before start for job ${request.jobId}`);
                    return {
                        cancellationId: String(cancellationRecord[0]._id),
                        refundAmount,
                        penaltyAmount,
                        creditAmount: refundAmount,
                        status: 'completed',
                        message: 'Cancellation processed successfully. Client has been refunded and can repost or reschedule the job.'
                    };
                }));
            }
            finally {
                yield session.endSession();
            }
        });
    }
    /**
     * Process client cancellation less than 24 hours before start
     */
    processClientCancellationLessThan24h(request) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const session = yield mongoose_1.default.startSession();
            try {
                return yield session.withTransaction(() => tslib_1.__awaiter(this, void 0, void 0, function* () {
                    // Validate request and get job details
                    const { job, bid, escrowTx } = yield this.validateCancellationRequest(request, 'client');
                    // Check timing - must be less than 24h before start
                    const scheduledDateTime = (0, parseJobDateTime_1.parseJobDateTime)(job.date, job.hour, 'America/Santo_Domingo');
                    const now = new Date();
                    const hoursBeforeStart = (scheduledDateTime.getTime() - now.getTime()) / (1000 * 60 * 60);
                    if (hoursBeforeStart >= 24) {
                        throw new cancellationErrors_1.CancellationNotAllowedError('Use regular cancellation for cancellations more than 24 hours before start');
                    }
                    if (now >= scheduledDateTime) {
                        throw new cancellationErrors_1.CancellationNotAllowedError('Job has already started. Use after-start cancellation.');
                    }
                    // Calculate penalty fees
                    const workerCompensation = Math.floor((escrowTx.amount * this.penaltyConfig.clientCancellationWorkerCompensation) / 100);
                    const platformFee = Math.floor((escrowTx.amount * this.penaltyConfig.clientCancellationPlatformFee) / 100);
                    const totalPenalty = workerCompensation + platformFee;
                    const refundAmount = Math.max(0, escrowTx.amount - totalPenalty);
                    // Create cancellation record
                    const cancellationRecord = yield schemas_1.CancellationRecord.create([{
                            jobId: request.jobId,
                            bidId: bid ? String(bid._id) : undefined,
                            initiatedBy: request.initiatedBy,
                            initiatorRole: 'client',
                            type: 'client_less_than_24h',
                            status: 'processing',
                            scheduledStartTime: scheduledDateTime,
                            cancellationTime: now,
                            hoursBeforeStart,
                            originalAmount: escrowTx.amount,
                            penaltyAmount: totalPenalty,
                            refundAmount,
                            adminShare: platformFee,
                            operatorShare: workerCompensation,
                            currency: escrowTx.currency,
                            reason: request.reason,
                            explanation: request.explanation,
                            evidenceUrls: request.evidenceUrls || [],
                            paymentTransactionIds: [String(escrowTx._id)],
                            creditTransactionIds: [],
                            metadata: {
                                clientNotified: false,
                                operatorNotified: false,
                                ratingImpactApplied: false,
                                jobReposted: false,
                                rescheduleOffered: false
                            }
                        }], { session });
                    // Process refund to client's credit balance
                    let creditTransactionId = null;
                    if (refundAmount > 0) {
                        const creditTransaction = yield this.creditBalanceService.addCredit({
                            userId: String(job.ownerId),
                            type: 'refund',
                            amount: refundAmount,
                            currency: escrowTx.currency,
                            description: `Partial refund for late cancellation (${hoursBeforeStart.toFixed(1)}h before start)`,
                            metadata: {
                                jobId: request.jobId,
                                bidId: bid ? String(bid._id) : undefined,
                                cancellationId: String(cancellationRecord[0]._id),
                                paymentTransactionId: String(escrowTx._id)
                            }
                        });
                        creditTransactionId = String(creditTransaction._id);
                    }
                    // Compensate operator if job was assigned
                    if (job.assignedOperatorId && workerCompensation > 0) {
                        const operatorCreditTransaction = yield this.creditBalanceService.addCredit({
                            userId: String(job.assignedOperatorId),
                            type: 'penalty_compensation',
                            amount: workerCompensation,
                            currency: escrowTx.currency,
                            description: `Compensation for client late cancellation`,
                            metadata: {
                                jobId: request.jobId,
                                bidId: bid ? String(bid._id) : undefined,
                                cancellationId: String(cancellationRecord[0]._id),
                                paymentTransactionId: String(escrowTx._id)
                            }
                        });
                    }
                    // Update job status
                    yield schemas_1.Job.findByIdAndUpdate(request.jobId, {
                        status: 'cancelled',
                        cancelledAt: now,
                        cancelReason: request.reason
                    }, { session });
                    // Update cancellation record
                    const updateData = {
                        status: 'completed',
                        processedAt: now,
                        'metadata.clientNotified': true,
                        'metadata.operatorNotified': true
                    };
                    if (creditTransactionId) {
                        updateData.creditTransactionIds = [creditTransactionId];
                    }
                    yield schemas_1.CancellationRecord.findByIdAndUpdate(cancellationRecord[0]._id, updateData, { session });
                    logger_1.logger.info(`Processed client late cancellation for job ${request.jobId}, penalty: ${totalPenalty}`);
                    return {
                        cancellationId: String(cancellationRecord[0]._id),
                        refundAmount,
                        penaltyAmount: totalPenalty,
                        creditAmount: refundAmount,
                        status: 'completed',
                        message: `Cancellation processed. Penalty applied: ${totalPenalty} ${escrowTx.currency}. Remaining amount credited to your balance.`
                    };
                }));
            }
            finally {
                yield session.endSession();
            }
        });
    }
    /**
     * Validate cancellation request and return job details
     */
    validateCancellationRequest(request, expectedRole) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            // Get job
            const job = yield schemas_1.Job.findById(request.jobId);
            if (!job) {
                throw new cancellationErrors_1.InvalidCancellationError('Job not found');
            }
            // Check if job can be cancelled
            if (job.status === 'cancelled') {
                throw new cancellationErrors_1.JobNotCancellableError('Job is already cancelled');
            }
            if (job.status === 'completed') {
                throw new cancellationErrors_1.JobNotCancellableError('Cannot cancel completed job');
            }
            // Check authorization
            if (expectedRole === 'client' && job.ownerId !== request.initiatedBy) {
                throw new cancellationErrors_1.UnauthorizedCancellationError('Only job owner can cancel');
            }
            if (expectedRole === 'operator' && job.assignedOperatorId !== request.initiatedBy) {
                throw new cancellationErrors_1.UnauthorizedCancellationError('Only assigned operator can cancel');
            }
            // Get bid if job is accepted
            let bid = null;
            if (job.acceptedBidId) {
                bid = yield schemas_1.Bid.findById(job.acceptedBidId);
            }
            // Get escrow transaction
            const escrowTx = yield schemas_1.PaymentTransaction.findOne({
                jobId: request.jobId,
                type: 'Hold',
                status: { $in: ['escrow', 'successful'] }
            });
            if (!escrowTx) {
                throw new cancellationErrors_1.InvalidCancellationError('No escrow transaction found for this job');
            }
            return { job, bid, escrowTx };
        });
    }
    /**
     * Create operator penalty record
     */
    createOperatorPenalty(options) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            // Check for previous penalties to determine recurrence
            const previousPenalties = yield schemas_1.OperatorPenalty.find({
                operatorId: options.operatorId,
                type: options.type,
                status: { $in: ['active', 'served'] }
            }).session(options.session);
            const isRecurring = previousPenalties.length > 0;
            const recurrenceMultiplier = Math.min(1.0 + (previousPenalties.length * 0.5), 3.0);
            // Create penalty record
            const penalty = yield schemas_1.OperatorPenalty.create([{
                    operatorId: options.operatorId,
                    jobId: options.jobId,
                    bidId: options.bidId,
                    cancellationId: options.cancellationId,
                    type: options.type,
                    status: 'active',
                    severity: options.severity,
                    financialPenalty: {
                        amount: Math.floor(options.financialPenalty * recurrenceMultiplier),
                        currency: 'DOP',
                        deductedFrom: 'future_earnings'
                    },
                    ratingImpact: {
                        pointsDeducted: options.ratingPenalty * recurrenceMultiplier,
                        isConfigurable: this.penaltyConfig.ratingPenaltyConfigurable
                    },
                    availabilityRestriction: {
                        restrictionType: 'none'
                    },
                    reason: options.reason,
                    description: options.description,
                    issuedBy: 'system',
                    issuedByRole: 'system',
                    isRecurring,
                    previousPenaltyIds: previousPenalties.map(p => String(p._id)),
                    recurrenceMultiplier,
                    metadata: {
                        automaticallyIssued: true,
                        escalationLevel: previousPenalties.length + 1
                    }
                }], { session: options.session });
            return penalty[0];
        });
    }
    /**
     * Process client cancellation after job has started
     */
    processClientCancellationAfterStart(request) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const session = yield mongoose_1.default.startSession();
            try {
                return yield session.withTransaction(() => tslib_1.__awaiter(this, void 0, void 0, function* () {
                    // Validate request and get job details
                    const { job, bid, escrowTx } = yield this.validateCancellationRequest(request, 'client');
                    // Check timing - must be after job start
                    const scheduledDateTime = (0, parseJobDateTime_1.parseJobDateTime)(job.date, job.hour, 'America/Santo_Domingo');
                    const now = new Date();
                    if (now < scheduledDateTime) {
                        throw new cancellationErrors_1.CancellationNotAllowedError('Job has not started yet. Use pre-start cancellation.');
                    }
                    // Require explanation for after-start cancellations
                    if (!request.explanation || request.explanation.trim().length < 10) {
                        throw new cancellationErrors_1.InvalidCancellationError('Detailed explanation required for after-start cancellations');
                    }
                    // Calculate retention based on job progress (50-70% suggested)
                    const retentionPercentage = 60; // Default 60% retention
                    const retainedAmount = Math.floor((escrowTx.amount * retentionPercentage) / 100);
                    const refundAmount = escrowTx.amount - retainedAmount;
                    // Create cancellation record
                    const cancellationRecord = yield schemas_1.CancellationRecord.create([{
                            jobId: request.jobId,
                            bidId: bid ? String(bid._id) : undefined,
                            initiatedBy: request.initiatedBy,
                            initiatorRole: 'client',
                            type: 'client_after_start',
                            status: 'processing',
                            scheduledStartTime: scheduledDateTime,
                            cancellationTime: now,
                            hoursBeforeStart: (scheduledDateTime.getTime() - now.getTime()) / (1000 * 60 * 60), // Negative value
                            originalAmount: escrowTx.amount,
                            penaltyAmount: retainedAmount,
                            refundAmount,
                            adminShare: Math.floor(retainedAmount * 0.3), // 30% to platform
                            operatorShare: Math.floor(retainedAmount * 0.7), // 70% to operator
                            currency: escrowTx.currency,
                            reason: request.reason,
                            explanation: request.explanation,
                            evidenceUrls: request.evidenceUrls || [],
                            paymentTransactionIds: [String(escrowTx._id)],
                            creditTransactionIds: [],
                            metadata: {
                                clientNotified: false,
                                operatorNotified: false,
                                ratingImpactApplied: false,
                                jobReposted: false,
                                rescheduleOffered: false
                            }
                        }], { session });
                    // Process refund to client's credit balance
                    let creditTransactionId = null;
                    if (refundAmount > 0) {
                        const creditTransaction = yield this.creditBalanceService.addCredit({
                            userId: String(job.ownerId),
                            type: 'refund',
                            amount: refundAmount,
                            currency: escrowTx.currency,
                            description: `Partial refund for after-start cancellation`,
                            metadata: {
                                jobId: request.jobId,
                                bidId: bid ? String(bid._id) : undefined,
                                cancellationId: String(cancellationRecord[0]._id),
                                paymentTransactionId: String(escrowTx._id)
                            }
                        });
                        creditTransactionId = String(creditTransaction._id);
                    }
                    // Compensate operator for work done
                    if (job.assignedOperatorId && Math.floor(retainedAmount * 0.7) > 0) {
                        yield this.creditBalanceService.addCredit({
                            userId: String(job.assignedOperatorId),
                            type: 'penalty_compensation',
                            amount: Math.floor(retainedAmount * 0.7),
                            currency: escrowTx.currency,
                            description: `Compensation for after-start cancellation`,
                            metadata: {
                                jobId: request.jobId,
                                bidId: bid ? String(bid._id) : undefined,
                                cancellationId: String(cancellationRecord[0]._id),
                                paymentTransactionId: String(escrowTx._id)
                            }
                        });
                    }
                    // Update job status
                    yield schemas_1.Job.findByIdAndUpdate(request.jobId, {
                        status: 'cancelled',
                        cancelledAt: now,
                        cancelReason: request.reason
                    }, { session });
                    // Update cancellation record
                    const updateData = {
                        status: 'completed',
                        processedAt: now,
                        'metadata.clientNotified': true,
                        'metadata.operatorNotified': true
                    };
                    if (creditTransactionId) {
                        updateData.creditTransactionIds = [creditTransactionId];
                    }
                    yield schemas_1.CancellationRecord.findByIdAndUpdate(cancellationRecord[0]._id, updateData, { session });
                    logger_1.logger.info(`Processed client after-start cancellation for job ${request.jobId}`);
                    return {
                        cancellationId: String(cancellationRecord[0]._id),
                        refundAmount,
                        penaltyAmount: retainedAmount,
                        creditAmount: refundAmount,
                        status: 'completed',
                        message: `After-start cancellation processed. ${retentionPercentage}% retained for work done. Remaining amount credited to your balance.`
                    };
                }));
            }
            finally {
                yield session.endSession();
            }
        });
    }
    /**
     * Process operator cancellation after arriving at location
     */
    processOperatorCancellationAfterArrival(request) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const session = yield mongoose_1.default.startSession();
            try {
                return yield session.withTransaction(() => tslib_1.__awaiter(this, void 0, void 0, function* () {
                    // Validate request and get job details
                    const { job, bid, escrowTx } = yield this.validateCancellationRequest(request, 'operator');
                    // This type of cancellation can happen at any time after operator accepts
                    const scheduledDateTime = (0, parseJobDateTime_1.parseJobDateTime)(job.date, job.hour, 'America/Santo_Domingo');
                    const now = new Date();
                    // Higher penalties for after-arrival cancellation
                    const penaltyAmount = this.penaltyConfig.operatorAfterArrivalFinancialPenalty;
                    const refundAmount = escrowTx.amount; // Full refund to client
                    // Create cancellation record
                    const cancellationRecord = yield schemas_1.CancellationRecord.create([{
                            jobId: request.jobId,
                            bidId: bid ? String(bid._id) : undefined,
                            initiatedBy: request.initiatedBy,
                            initiatorRole: 'operator',
                            type: 'operator_after_arrival',
                            status: 'processing',
                            scheduledStartTime: scheduledDateTime,
                            cancellationTime: now,
                            hoursBeforeStart: (scheduledDateTime.getTime() - now.getTime()) / (1000 * 60 * 60),
                            originalAmount: escrowTx.amount,
                            penaltyAmount,
                            refundAmount,
                            adminShare: 0,
                            operatorShare: 0,
                            currency: escrowTx.currency,
                            reason: request.reason,
                            explanation: request.explanation,
                            evidenceUrls: request.evidenceUrls || [],
                            paymentTransactionIds: [String(escrowTx._id)],
                            creditTransactionIds: [],
                            metadata: {
                                clientNotified: false,
                                operatorNotified: false,
                                ratingImpactApplied: false,
                                jobReposted: false,
                                rescheduleOffered: true
                            }
                        }], { session });
                    // Process severe financial penalty for operator
                    if (penaltyAmount > 0) {
                        const operatorPenalty = yield this.createOperatorPenalty({
                            operatorId: request.initiatedBy,
                            jobId: request.jobId,
                            bidId: bid ? String(bid._id) : undefined,
                            cancellationId: String(cancellationRecord[0]._id),
                            type: 'cancellation_after_arrival',
                            severity: 'major',
                            financialPenalty: penaltyAmount,
                            ratingPenalty: this.penaltyConfig.operatorAfterArrivalRatingPenalty,
                            reason: request.reason,
                            description: `Operator cancelled after arriving at location. Severe penalty: ${penaltyAmount} ${escrowTx.currency}`,
                            session
                        });
                        yield schemas_1.CancellationRecord.findByIdAndUpdate(cancellationRecord[0]._id, { operatorPenaltyId: String(operatorPenalty._id) }, { session });
                    }
                    // Process full refund to client's credit balance
                    const creditTransaction = yield this.creditBalanceService.addCredit({
                        userId: String(job.ownerId),
                        type: 'refund',
                        amount: refundAmount,
                        currency: escrowTx.currency,
                        description: `Full refund for operator cancellation after arrival`,
                        metadata: {
                            jobId: request.jobId,
                            bidId: bid ? String(bid._id) : undefined,
                            cancellationId: String(cancellationRecord[0]._id),
                            paymentTransactionId: String(escrowTx._id)
                        }
                    });
                    // Update job status
                    yield schemas_1.Job.findByIdAndUpdate(request.jobId, {
                        status: 'cancelled',
                        cancelledAt: now,
                        cancelReason: request.reason
                    }, { session });
                    // Update cancellation record
                    yield schemas_1.CancellationRecord.findByIdAndUpdate(cancellationRecord[0]._id, {
                        status: 'completed',
                        processedAt: now,
                        creditTransactionIds: [String(creditTransaction._id)],
                        'metadata.clientNotified': true,
                        'metadata.operatorNotified': true,
                        'metadata.rescheduleOffered': true
                    }, { session });
                    logger_1.logger.info(`Processed operator after-arrival cancellation for job ${request.jobId}`);
                    return {
                        cancellationId: String(cancellationRecord[0]._id),
                        refundAmount,
                        penaltyAmount,
                        creditAmount: refundAmount,
                        status: 'completed',
                        message: 'Cancellation processed successfully. Severe penalty applied to operator. Client has been fully refunded.'
                    };
                }));
            }
            finally {
                yield session.endSession();
            }
        });
    }
    /**
     * Update penalty configuration
     */
    updatePenaltyConfiguration(config) {
        this.penaltyConfig = Object.assign(Object.assign({}, this.penaltyConfig), config);
        logger_1.logger.info('Penalty configuration updated', config);
    }
    /**
     * Get current penalty configuration
     */
    getPenaltyConfiguration() {
        return Object.assign({}, this.penaltyConfig);
    }
}
exports.CancellationService = CancellationService;
