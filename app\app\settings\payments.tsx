import React, { useContext, useEffect, useRef, useState } from 'react'
import { ScrollView, StyleSheet, View } from 'react-native'


import Page from '@/components/templates/Page'
import Header from '@/components/templates/Header'
import CreditCardPanel from '@/components/molecules/CreditCardPanel'
import ButtonIcon from '@/components/atoms/buttons/ButtonIcon'
import { AuthContext } from '@/context/AuthContext'
import { useBottomSheet } from '@/context/BottomSheetContext'
import NewCardSheet from '@/components/molecules/bottomsheets/new_credit_card'






const Index = () => {
  const { userData } = useContext(AuthContext)
  const { handleToggleBottomSheet, bottomSheetModalRef } = useBottomSheet()


  const toggleBottomSheet = () => handleToggleBottomSheet(<NewCardSheet onConfirm={()=>{bottomSheetModalRef?.current?.dismiss()}}/>, 800)


  return (
    <Page noPaddingTop alignItems='center' justifyContent='space-between' >
      <Header buttonBack text='Metodos de Pago' />

      <ScrollView contentInset={{top: 100}} style={styles.scrollView} showsVerticalScrollIndicator={false}>

        <View style={{width: '100%', paddingHorizontal: 20, marginBottom: 20}}>
          <ButtonIcon text='Añadir Nueva Tarjeta' onPress={toggleBottomSheet}/>
        </View>

        <CreditCardPanel
          name={userData?.user.name ?? ''}
          surname={userData?.user.surname ?? ''}
          last4='3722'
          exp={{
            month: 9,
            year: 2026
          }}
        />
      </ScrollView>
    </Page>
  )
}



const styles = StyleSheet.create({

  scrollView: {
    flex: 1,
    width: '100%'
  },

})

export default Index

