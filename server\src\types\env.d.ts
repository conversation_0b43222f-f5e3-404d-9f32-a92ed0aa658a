// src/env.d.ts
declare namespace NodeJS {
    interface ProcessEnv {
      JWT_SECRET: string;
      DOCKER_MONGODB_URI: string;
      MONGODB_USERNAME: string;
      MONGODB_PASSWORD: string;
      MONGODB_URI: string;
      OPENAI_API_KEY: string;
      NODE_ENV: string;
      PORT: string;
      //STRIPE_SECRET_KEY: string;
      AWS_S3_REGION: string;
      AWS_S3_ACCESS_KEY_ID: string;
      AWS_S3_SECRET_ACCESS_KEY: string;
      AWS_S3_BUCKET_NAME: string;
    }
}
 