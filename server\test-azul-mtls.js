const axios = require('axios');
const crypto = require('crypto');
const https = require('https');
const fs = require('fs');
const path = require('path');

// Test credentials from Azul documentation
const TEST_CREDENTIALS = {
  merchantId: '39038540035',
  auth1: 'splitit',
  auth2: 'splitit',
  endpoint: 'https://pruebas.azul.com.do/WebServices/JSON/Default.aspx'
};

// Test cards provided by Azul
const TEST_CARDS = {
  // Regular test cards
  mastercard: { number: '****************', exp: '1228', cvv: '732' },
  discover: { number: '****************', exp: '1228', cvv: '818' },
  visa1: { number: '****************', exp: '1228', cvv: '872' },
  visa2_limited: { number: '****************', exp: '1228', cvv: '977' }, // Limit RD$ 75
  mastercard2: { number: '****************', exp: '1228', cvv: '979' },
  visa3: { number: '****************', exp: '1228', cvv: '123' },
  
  // 3D Secure test cards
  threeds_frictionless_with_method: { number: '****************', exp: '1228', cvv: '123' },
  threeds_frictionless_no_method: { number: '****************', exp: '1228', cvv: '123' },
  threeds_challenge_with_method: { number: '****************', exp: '1228', cvv: '123' }, // Limit 50
  threeds_challenge_no_method: { number: '****************', exp: '1228', cvv: '123' }
};

// Environment configuration:
// AZUL_MTLS_REJECT_UNAUTHORIZED: controls whether Node's TLS layer rejects
// unauthorized/invalid SSL certificates for the mTLS axios agent.
// Accepted values (case-insensitive): '1','true','yes','on' => true; '0','false','no','off' => false.
// Default: true (validate certificates). For security, when NODE_ENV === 'production' the
// check is forced to true and cannot be disabled via env var.
function parseBoolEnv(name, defaultValue) {
  const raw = process.env[name];
  if (raw === undefined || raw === null) return defaultValue;
  const s = String(raw).trim().toLowerCase();
  if (['0', 'false', 'no', 'off'].includes(s)) return false;
  if (['1', 'true', 'yes', 'on'].includes(s)) return true;
  return defaultValue;
}

let rejectUnauthorized = parseBoolEnv('AZUL_MTLS_REJECT_UNAUTHORIZED', true);
if (process.env.NODE_ENV === 'production') {
  // Never allow disabling cert validation in production
  rejectUnauthorized = true;
}
if (!rejectUnauthorized) {
  console.warn('⚠️  WARNING: SSL certificate validation is disabled. This should only be used for local testing.');
}

function generateAuthHeaders(data) {
  // Sort the data alphabetically by key
  const sortedKeys = Object.keys(data).sort();
  
  // Convert to URL parameters format
  const params = [];
  sortedKeys.forEach(key => {
    const value = data[key];
    if (value !== undefined && value !== null && value !== '') {
      params.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
    }
  });
  
  const queryString = params.join('&');
  console.log('Query string for hash:', queryString);
  
  // Generate Auth1 (SHA512 hash of query string + Auth1 secret)
  const auth1Hash = crypto
    .createHash('sha512')
    .update(queryString + TEST_CREDENTIALS.auth1)
    .digest('hex')
    .toUpperCase();

  // Generate Auth2 (SHA512 hash of query string + Auth2 secret)  
  const auth2Hash = crypto
    .createHash('sha512')
    .update(queryString + TEST_CREDENTIALS.auth2)
    .digest('hex')
    .toUpperCase();

  return {
    Auth1: auth1Hash,
    Auth2: auth2Hash
  };
}

function createMTLSAgent() {
  const certsPath = path.resolve(__dirname, 'certs/azul');
  
  try {
    const clientCert = path.join(certsPath, 'local.pem');
    const privateKey = path.join(certsPath, 'private.key');
    const intermediateCert = path.join(certsPath, 'intermediate.pem');
    const rootCert = path.join(certsPath, 'root.pem');
    
    if (fs.existsSync(clientCert) && fs.existsSync(privateKey)) {
      console.log('🔐 Loading mTLS certificates for authentication');
      
      // Build CA chain (intermediate + root)
      const caChain = [];
      if (fs.existsSync(intermediateCert)) {
        caChain.push(fs.readFileSync(intermediateCert));
        console.log('✅ Loaded intermediate certificate');
      }
      if (fs.existsSync(rootCert)) {
        caChain.push(fs.readFileSync(rootCert));
        console.log('✅ Loaded root certificate');
      }
      
      const agent = new https.Agent({
        cert: fs.readFileSync(clientCert),          // Client certificate from Azul
        key: fs.readFileSync(privateKey),           // Private key from CSR
        ca: caChain,                                // Certificate authority chain
        rejectUnauthorized: rejectUnauthorized,
        keepAlive: true,                           // Performance optimization
        secureProtocol: 'TLSv1_2_method'          // TLS 1.2+ required
      });
      
      console.log('✅ mTLS agent created successfully');
      return agent;
    } else {
      console.error('❌ Certificate files not found:');
      console.error('  Client cert:', clientCert, '- exists:', fs.existsSync(clientCert));
      console.error('  Private key:', privateKey, '- exists:', fs.existsSync(privateKey));
      return null;
    }
  } catch (error) {
    console.error('❌ Error creating mTLS agent:', error.message);
    return null;
  }
}

async function testDataVault() {
  // Use one of the official test cards from Azul
  const testCard = TEST_CARDS.visa1; // ****************
  
  const requestData = {
    TrxType: 'CREATE',
    MerchantId: TEST_CREDENTIALS.merchantId,
    CardNumber: testCard.number,
    Expiration: testCard.exp,
    CVC: testCard.cvv,
    CardHolderName: 'Test User'
  };

  const authHeaders = generateAuthHeaders(requestData);
  const httpsAgent = createMTLSAgent();
  
  if (!httpsAgent) {
    console.error('❌ Cannot proceed without mTLS certificates');
    return;
  }
  
  console.log('Request data:', {
    ...requestData,
    CardNumber: '****' + requestData.CardNumber.slice(-4)
  });
  
  console.log('Auth headers:', {
    Auth1: authHeaders.Auth1.substring(0, 20) + '...',
    Auth2: authHeaders.Auth2.substring(0, 20) + '...'
  });

  try {
    console.log('🚀 Making request to Azul with mTLS...');
    const response = await axios.post(
      TEST_CREDENTIALS.endpoint,
      requestData,
      {
        httpsAgent,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          ...authHeaders
        },
        timeout: 30000
      }
    );

    console.log('🎉 SUCCESS! Response from Azul:');
    console.log(JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('❌ Request failed:');
    console.error('Message:', error.message);
    console.error('Status:', error.response?.status);
    console.error('Status Text:', error.response?.statusText);
    
    if (error.response?.data) {
      console.error('Response Data:', error.response.data);
    }
    
    // Check if this is still an Incapsula error
    if (error.response?.data && typeof error.response.data === 'string' && 
        error.response.data.includes('Incapsula')) {
      console.error('🚨 Still getting Incapsula error - mTLS may not be configured correctly');
    }
  }
}

testDataVault();
