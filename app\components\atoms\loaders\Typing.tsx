import LottieView from 'lottie-react-native'
import React from 'react'
import { StyleSheet, View } from 'react-native'

const TypingLoader = () => {

  return (
    <View style={styles.viewport}>
      <LottieView 
        source={require('@/assets/animations/typing.json')}
        style={{ height: 80, aspectRatio: 1}}
        speed={1.5}
        autoPlay
        loop
        />
    </View>
  )
}

export default TypingLoader


const styles = StyleSheet.create({
  viewport: {
    position: 'absolute',
    top: -38,
    left: 20,
    zIndex: 100,
    width: 40,
    height: 20,
    backgroundColor: "#00000010",
    borderWidth: 1.7,
    borderColor: "#00000020",
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 20,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    },

})