
// Unified role type used across the app
export type Role = 'user' | 'operator' | 'admin';

// Interface for authenticated user with required _id property
export interface AuthenticatedUser {
  _id: string;
  email: string;
  role: Role;
  user: {
    name: string;
    surname: string;
    username: string;
    profile_picture: string;
    birthdate: Date | string;
  };
  contacts: {
    phone: string;
    address: string;
  };
  settings: {
    currency: string;
    preferred_language: string;
    timezone: string;
  };
  finances: {
    billing_address: string;
  };
  booleans: {
    isVerified?: boolean;
  };
  notifications: {
    expo_push_token?: string;
  };
  createdAt: Date;
}

declare global {
  namespace Express {
    interface Request {
      user?: AuthenticatedUser;
    }
  }
}

// This ensures the file is treated as a module
export {};
