import mongoose, { Document, Schema } from 'mongoose';

export interface IPaymentTransaction extends Document {
  id: string;
  jobId?: string; // Reference to Job (nullable for non-job payments)
  bidId?: string; // Reference to Bid (nullable)
  userId: string; // Reference to Account
  paymentMethodId: string; // Reference to PaymentMethod
  amount: number; // Amount in cents/centavos
  currency: string; // 'DOP', 'USD', etc.
  status: 'initiated' | 'escrow' | 'successful' | 'failed' | 'refunded' | 'voided' | 'requires_action' | 'challenge_required' | string;
  type: 'Sale' | 'Hold' | 'sale' | 'hold'; // accept both cases in interface, normalized on set
  providerReference: string; // AzulOrderId / RRN from Azul
  threeDSServerTransID?: string; // For 3DS tracking
  refundedAmount?: number;
  threeDSMethod?: any;
  threeDS?: any;
  threeDSChallenge?: any; // Raw challenge data from Azul
  challengeData?: { // Structured challenge data for client handling
    challengeForm?: string;
    challengeUrl?: string;
    challengeWindowSize?: string;
    threeDSServerTransID?: string;
    createdAt?: Date;
  };
  idempotencyKey: string; // For preventing duplicate charges
  // New fields for commission & taxes breakdown (amounts in cents/centavos)
  commissionAmount?: number; // Fixed platform commission taken from customer
  commissionTaxAmount?: number; // Tax applied to the commission
  azulResponse?: {
    isoCode?: string;
    responseMessage?: string;
    authCode?: string;
    rrn?: string;
    originalAmount?: number;
    // Limited response data for audit purposes
  };
  refunds?: Array<{
    amount: number;
    reason: string;
    refundId: string;
    processedAt: Date;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

const paymentTransactionSchema = new mongoose.Schema<IPaymentTransaction>({
  jobId: {
    type: String,
    ref: 'Job',
    index: true
  },
  bidId: {
    type: String,
    ref: 'Bid',
    index: true
  },
  userId: {
    type: String,
    required: true,
    ref: 'Account'
  },
  paymentMethodId: {
    type: String,
    required: true,
    ref: 'PaymentMethod',
    index: true
  },
  amount: {
    type: Number,
    required: true,
    min: 1 // Minimum 1 cent/centavo
  },
  currency: {
    type: String,
    required: true,
    enum: ['DOP', 'USD'], // Dominican Peso and US Dollar
    default: 'DOP'
  },
  status: {
    type: String,
    enum: ['initiated', 'escrow', 'successful', 'failed', 'refunded', 'voided', 'requires_action', 'challenge_required'],
    default: 'initiated',
    required: true,
    set: (v: any) => {
      if (v === null || typeof v === 'undefined') return v;
      const s = String(v).toLowerCase();
      if (s === 'requiresaction' || s === 'requires_action' || s === 'requires-action') return 'requires_action';
      if (s === 'challengerequired' || s === 'challenge_required' || s === 'challenge-required') return 'challenge_required';
      if (['initiated', 'escrow', 'successful', 'failed', 'refunded', 'voided'].includes(s)) return s;
      // Return the original value to trigger enum validation error if invalid
      return v;
    }
  },
  type: {
    type: String,
    enum: ['Sale', 'Hold'],
    required: true,
    set: (v: any) => {
      if (v === null || typeof v === 'undefined') return v;
      const s = String(v).toLowerCase();
      if (s === 'sale') return 'Sale';
      if (s === 'hold') return 'Hold';
      // Return the original value to trigger enum validation if invalid
      return v;
    }
  },
  providerReference: {
    type: String,
    required: true
  },
  threeDSServerTransID: {
    type: String,
    index: true
  },
  // Track total refunded amount to allow atomic refund guards
  refundedAmount: {
    type: Number,
    default: 0
  },
  // Persist raw ThreeDS method/challenge payloads as needed (do not store sensitive data like PAN)
  // …other fields…
  threeDSMethod: {
    threeDSMethodURL: String,
    threeDSServerTransID: String
  },
  threeDS: {
    acsURL: String,
    acsTransID: String,
    challengeURL: String
  },
  // Raw 3DS challenge data from Azul
  threeDSChallenge: {
    type: mongoose.Schema.Types.Mixed
  },
  // Structured challenge data for client handling
  challengeData: {
    challengeForm: String,
    challengeUrl: String,
    challengeWindowSize: String,
    threeDSServerTransID: String,
    createdAt: Date
  },
  // …following fields…  },
  idempotencyKey: {
    type: String,
    required: true,
    // uniqueness enforced by compound index with userId below
  },
  // Commission & taxes breakdown
  commissionAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  commissionTaxAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  azulResponse: {
    isoCode: String,
    responseMessage: String,
    authCode: String,
    rrn: String,
    originalAmount: Number
  },
  refunds: [{
    amount: {
      type: Number,
      required: true,
      min: 1
    },
    reason: {
      type: String,
      required: true,
      maxlength: 500
    },
    refundId: {
      type: String,
      required: true
    },
    processedAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true
});

// Ensure total refunds do not exceed the original transaction amount
paymentTransactionSchema.pre('save', function (next) {
  // `this` is the document being saved
  const doc: any = this;

  try {
    const refunds = Array.isArray(doc.refunds) ? doc.refunds : [];
    const totalRefunded = refunds.reduce((sum: number, r: any) => sum + (Number(r?.amount) || 0), 0);

    if (typeof doc.amount === 'number' && totalRefunded > doc.amount) {
      return next(new Error('Total refunded amount cannot exceed original transaction amount'));
    }

    return next();
  } catch (err) {
    return next(err as any);
  }
});

// Indexes for efficient querying
paymentTransactionSchema.index({ userId: 1, createdAt: -1 });
paymentTransactionSchema.index({ status: 1, createdAt: -1 });
// Enforce uniqueness of idempotency keys per user
paymentTransactionSchema.index({ userId: 1, idempotencyKey: 1 }, { unique: true });
// providerReference should be unique to avoid duplicate external order ids
paymentTransactionSchema.index({ providerReference: 1 }, { unique: true });

export default mongoose.model<IPaymentTransaction>('PaymentTransaction', paymentTransactionSchema);
