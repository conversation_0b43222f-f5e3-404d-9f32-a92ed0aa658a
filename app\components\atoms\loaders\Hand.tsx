import LottieView from 'lottie-react-native'
import React from 'react'
import { StyleSheet, View } from 'react-native'

const HandAnimation = () => {

  return (
    <View style={styles.viewport}>
      <LottieView
        source={require('@/assets/animations/waving_hand.json')}
        style={{ height: 340, aspectRatio: 1 }}
        speed={1}
        autoPlay
        loop
      />
    </View>
  )
}

export default HandAnimation


const styles = StyleSheet.create({
  viewport: {
    position: 'absolute',
    bottom: 152,
    zIndex: 1,
    width: '100%',
    height: 200,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },

})