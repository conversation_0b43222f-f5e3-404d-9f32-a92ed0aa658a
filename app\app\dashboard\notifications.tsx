import React, { useRef, useEffect } from 'react';
import { Image, ScrollView, StyleSheet, Text, View } from 'react-native';

import Page from '@/components/templates/Page';
import Header from '@/components/templates/Header';
import { useLocalSearchParams } from 'expo-router';

const Index = () => {


  return (

    <Page noPaddingTop noBottomBar alignItems="center" justifyContent="space-between" page="home">
      <Header buttonBack text='Notificaciones' />
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.scrollView}
      >

        <View style={{alignItems: 'center', justifyContent: 'center', height: 650}}>
          <Image
            source={require('@/assets/fallbacks/notifications.png')}
            style={{width: 280,height: 184, resizeMode: 'contain'}}
          />
          <Text style={styles.title}>No hay notificaciones</Text>
          <Text style={styles.subtitle}>Ya viste todas las notificaciones. Cuando tengas nuevas las veràs aqui</Text>
        </View>


      </ScrollView>
    </Page>

  );
};

export default Index;

const styles = StyleSheet.create({
  scrollView: {
    paddingTop: 100
  },
  title: {
    fontFamily: 'Montserrat',
    fontSize: 18,
    fontWeight: 700,
    color: '#000'
  },
  subtitle: {
    marginTop: 8,
    fontFamily: 'Montserrat',
    fontSize: 14,
    fontWeight: 400,
    
    textAlign: 'center',
    paddingHorizontal: 40,
    lineHeight: 20,
    color: '#000000db'
  },
});