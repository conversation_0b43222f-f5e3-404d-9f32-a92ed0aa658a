# Environment Variables for Chat System

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here

# AWS S3 Configuration
AWS_S3_REGION=us-east-1
AWS_S3_ACCESS_KEY_ID=your-access-key-id
AWS_S3_SECRET_ACCESS_KEY=your-secret-access-key
AWS_S3_BUCKET_NAME=your-bucket-name

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key

# MongoDB Configuration
MONGODB_USERNAME=your-mongodb-username
MONGODB_PASSWORD=your-mongodb-password
MONGODB_URI=@cluster0.mongodb.net/manito?retryWrites=true&w=majority

# Server Configuration
NODE_ENV=development
PORT=4000

AZUL_SANDBOX_MERCHANT_ID=your-azul-sandbox-merchant-id
AZUL_SANDBOX_AUTH1=your-azul-sandbox-auth1
AZUL_SANDBOX_AUTH2=your-azul-sandbox-auth2
AZUL_SANDBOX_PRIMARY_ENDPOINT=https://pruebas.azul.com.do/WebServices/JSON/Default.aspx
AZUL_MOCK_MODE=true