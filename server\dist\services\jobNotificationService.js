"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserNotifications = exports.markNotificationAsRead = exports.getNotificationStats = exports.notifyOperatorsAboutNewJob = void 0;
const tslib_1 = require("tslib");
const schemas_1 = require("../database/schemas");
const notifications_1 = require("../controllers/notifications");
// Notify operators about new job creation
const notifyOperatorsAboutNewJob = (jobData) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log(`Starting notification process for new ${jobData.category} job: ${jobData.jobId}`);
        // Get all available operators authorized for this category
        const authorizedOperators = yield schemas_1.OperatorProfile.find({
            authorizedCategories: jobData.category,
            isAvailable: true
        }).populate('accountId', 'notifications.expo_push_token user.name');
        console.log(`Found ${authorizedOperators.length} authorized operators for category: ${jobData.category}`);
        if (authorizedOperators.length === 0) {
            console.log('No available operators found for this category');
            return;
        }
        // Prepare notification content
        const notificationTitle = 'New Job Available';
        const notificationMessage = `New ${jobData.category} job available - Budget: $${jobData.price}`;
        const notificationData = {
            jobId: jobData.jobId,
            category: jobData.category,
            type: 'job_created'
        };
        // Send notifications to all authorized operators
        const notificationPromises = authorizedOperators.map((operatorProfile) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
            try {
                const account = operatorProfile.accountId;
                const operatorId = operatorProfile.accountId.toString();
                // Skip if this is the job owner (shouldn't happen, but safety check)
                if (operatorId === jobData.ownerId) {
                    return;
                }
                // Get notification token
                const tokenResult = yield (0, notifications_1.getUserNotificationToken)(operatorId);
                if (tokenResult.success && tokenResult.notificationToken) {
                    // Send push notification
                    const notificationResult = yield (0, notifications_1.sendCustomNotification)(tokenResult.notificationToken, {
                        title: notificationTitle,
                        message: notificationMessage,
                        data: notificationData,
                        soundOn: true,
                        badgeCount: 1
                    });
                    console.log(`Notification sent to operator ${operatorId}:`, notificationResult ? 'success' : 'failed');
                }
                else {
                    console.log(`No notification token found for operator ${operatorId}`);
                }
                // Log the notification in database
                yield schemas_1.NotificationLog.create({
                    recipientId: operatorId,
                    type: 'job_created',
                    title: notificationTitle,
                    message: notificationMessage,
                    data: notificationData,
                    jobId: jobData.jobId
                });
                console.log(`Notification logged for operator ${operatorId}`);
            }
            catch (operatorError) {
                console.error(`Error notifying operator ${operatorProfile.accountId}:`, operatorError);
            }
        }));
        // Wait for all notifications to complete
        yield Promise.allSettled(notificationPromises);
        console.log(`Notification process completed for job ${jobData.jobId}`);
    }
    catch (error) {
        console.error('Error in notifyOperatorsAboutNewJob:', error);
    }
});
exports.notifyOperatorsAboutNewJob = notifyOperatorsAboutNewJob;
// Get notification statistics
const getNotificationStats = (jobId) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const stats = yield schemas_1.NotificationLog.aggregate([
            { $match: { jobId, type: 'job_created' } },
            {
                $group: {
                    _id: null,
                    totalSent: { $sum: 1 },
                    totalRead: { $sum: { $cond: ['$isRead', 1, 0] } }
                }
            }
        ]);
        return stats[0] || { totalSent: 0, totalRead: 0 };
    }
    catch (error) {
        console.error('Error getting notification stats:', error);
        return { totalSent: 0, totalRead: 0 };
    }
});
exports.getNotificationStats = getNotificationStats;
// Mark notification as read
const markNotificationAsRead = (notificationId) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = yield schemas_1.NotificationLog.findByIdAndUpdate(notificationId, {
            isRead: true,
            readAt: new Date()
        });
        return !!result;
    }
    catch (error) {
        console.error('Error marking notification as read:', error);
        return false;
    }
});
exports.markNotificationAsRead = markNotificationAsRead;
// Get user notifications
const getUserNotifications = (userId_1, ...args_1) => tslib_1.__awaiter(void 0, [userId_1, ...args_1], void 0, function* (userId, page = 1, limit = 20, unreadOnly = false) {
    try {
        const query = { recipientId: userId };
        if (unreadOnly) {
            query.isRead = false;
        }
        const notifications = yield schemas_1.NotificationLog.find(query)
            .sort({ sentAt: -1 })
            .limit(limit)
            .skip((page - 1) * limit)
            .populate('jobId', 'category description status')
            .populate('bidId', 'amount status');
        const total = yield schemas_1.NotificationLog.countDocuments(query);
        const unreadCount = yield schemas_1.NotificationLog.countDocuments({
            recipientId: userId,
            isRead: false
        });
        return {
            notifications,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            },
            unreadCount
        };
    }
    catch (error) {
        console.error('Error getting user notifications:', error);
        return null;
    }
});
exports.getUserNotifications = getUserNotifications;
