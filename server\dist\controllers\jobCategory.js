"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateCategoryDocumentRequirements = exports.getCategoryDocumentRequirements = exports.deleteCategory = exports.updateCategory = exports.createCategory = exports.getAllCategories = exports.getDocumentRequirementCategories = exports.getActiveCategories = void 0;
const tslib_1 = require("tslib");
const auth_1 = require("../utils/auth");
const jobCategoryService_1 = tslib_1.__importDefault(require("../services/jobCategoryService"));
const documentRequirements_1 = require("../types/documentRequirements");
const schemas_1 = require("../database/schemas");
// Helper function to safely compare MongoDB IDs and strings
function isSameId(id1, id2) {
    if (!id1 || !id2)
        return false;
    // Convert both to string and trim to ensure consistent comparison
    return String(id1).trim() === String(id2).trim();
}
/**
 * Get active job categories (public endpoint for users)
 */
const getActiveCategories = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const categories = yield jobCategoryService_1.default.getActiveCategories();
        res.json({
            success: true,
            categories: categories.map((cat) => ({
                name: cat.name,
                displayName: cat.displayName,
                description: cat.description,
                order: cat.order,
                documents_required: cat.documents_required || ['none'],
            })),
        });
    }
    catch (error) {
        console.error('Error getting active categories:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.getActiveCategories = getActiveCategories;
/**
 * Public: get master list of supported document requirement categories
 */
const getDocumentRequirementCategories = (_req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    res.json({
        success: true,
        categories: documentRequirements_1.DOCUMENT_REQUIREMENT_CATEGORIES,
        labels: documentRequirements_1.DOCUMENT_REQUIREMENT_LABELS,
    });
});
exports.getDocumentRequirementCategories = getDocumentRequirementCategories;
/**
 * Get all job categories (admin only)
 */
const getAllCategories = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if ((user === null || user === void 0 ? void 0 : user.role) !== 'admin') {
            return res.status(403).json({ success: false, error: 'Admin access required' });
        }
        const categories = yield jobCategoryService_1.default.getAllCategories();
        res.json({ success: true, categories });
    }
    catch (error) {
        console.error('Error getting all categories:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.getAllCategories = getAllCategories;
/**
 * Create new job category (admin only)
 */
const createCategory = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ success: false, error: 'Admin access required' });
        }
        const { name, displayName, description, order } = req.body;
        let documents_required = (_a = req.body) === null || _a === void 0 ? void 0 : _a.documents_required;
        if (!name || !displayName) {
            return res.status(400).json({ success: false, error: 'Name and displayName are required' });
        }
        // Check if category already exists
        const existingCategory = yield jobCategoryService_1.default.getCategoryByName(String(name));
        if (existingCategory) {
            return res.status(409).json({ success: false, error: 'Category with this name already exists' });
        }
        // Sanitize documents_required
        if (Array.isArray(documents_required)) {
            documents_required = documents_required
                .map((v) => (typeof v === 'string' ? v.trim().toLowerCase() : v))
                .filter(documentRequirements_1.isDocumentRequirementCategory);
        }
        else {
            documents_required = undefined;
        }
        const category = yield jobCategoryService_1.default.createCategory({
            name,
            displayName,
            description,
            order,
            documents_required: documents_required,
        });
        res.status(201).json({ success: true, category });
    }
    catch (error) {
        if ((error === null || error === void 0 ? void 0 : error.code) === 11000) {
            return res.status(409).json({ success: false, error: 'Category with this name already exists' });
        }
        console.error('Error creating category:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.createCategory = createCategory;
/**
 * Update job category (admin only)
 */
const updateCategory = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ success: false, error: 'Admin access required' });
        }
        const { categoryId } = req.params;
        const allowedFields = ['name', 'displayName', 'description', 'order', 'isActive', 'documents_required'];
        const raw = req.body;
        const updateData = Object.fromEntries(Object.entries(raw).filter(([k]) => allowedFields.includes(k)));
        if (typeof updateData.name === 'string') {
            updateData.name = updateData.name.trim().toLowerCase();
        }
        if (typeof updateData.displayName === 'string') {
            updateData.displayName = updateData.displayName.trim();
        }
        if (updateData.order != null) {
            updateData.order = Number(updateData.order);
        }
        // documents_required validation and normalization
        if (Array.isArray(raw.documents_required)) {
            const incoming = raw.documents_required;
            const sanitized = incoming
                .map((v) => (typeof v === 'string' ? v.trim().toLowerCase() : v))
                .filter(documentRequirements_1.isDocumentRequirementCategory);
            updateData.documents_required = sanitized.length ? sanitized : ['none'];
        }
        // Don't allow updating the name if it would conflict with existing category
        if (typeof updateData.name === 'string' && updateData.name) {
            const existingCategory = yield jobCategoryService_1.default.getCategoryByName(updateData.name);
            if (existingCategory && !isSameId(existingCategory._id, categoryId)) {
                return res.status(409).json({ success: false, error: 'Category with this name already exists' });
            }
        }
        const category = yield jobCategoryService_1.default.updateCategory(categoryId, updateData);
        if (!category) {
            return res.status(404).json({ success: false, error: 'Category not found' });
        }
        res.json({ success: true, category });
    }
    catch (error) {
        console.error('Error updating category:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.updateCategory = updateCategory;
/**
 * Delete job category (admin only)
 */
const deleteCategory = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ success: false, error: 'Admin access required' });
        }
        const { categoryId } = req.params;
        const success = yield jobCategoryService_1.default.deleteCategory(categoryId);
        if (!success) {
            return res.status(404).json({ success: false, error: 'Category not found' });
        }
        res.json({ success: true, message: 'Category deleted successfully' });
    }
    catch (error) {
        console.error('Error deleting category:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.deleteCategory = deleteCategory;
// Admin: get document requirements for a specific job category
const getCategoryDocumentRequirements = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ success: false, error: 'Admin access required' });
        }
        const { categoryId } = req.params;
        const category = yield schemas_1.JobCategory.findById(categoryId);
        if (!category)
            return res.status(404).json({ success: false, error: 'Category not found' });
        res.json({ success: true, name: category.name, documents_required: category.documents_required || ['none'] });
    }
    catch (error) {
        console.error('Error getting category document requirements:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.getCategoryDocumentRequirements = getCategoryDocumentRequirements;
// Admin: update document requirements for a specific job category
const updateCategoryDocumentRequirements = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ success: false, error: 'Admin access required' });
        }
        const { categoryId } = req.params;
        let { documents_required } = req.body;
        if (!Array.isArray(documents_required)) {
            return res.status(400).json({ success: false, error: 'documents_required must be an array' });
        }
        const sanitized = documents_required
            .map((v) => (typeof v === 'string' ? v.trim().toLowerCase() : v))
            .filter(documentRequirements_1.isDocumentRequirementCategory);
        const updated = yield jobCategoryService_1.default.updateCategory(categoryId, { documents_required: sanitized });
        if (!updated)
            return res.status(404).json({ success: false, error: 'Category not found' });
        res.json({ success: true, category: updated });
    }
    catch (error) {
        console.error('Error updating category document requirements:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.updateCategoryDocumentRequirements = updateCategoryDocumentRequirements;
