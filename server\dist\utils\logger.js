"use strict";
// Minimal structured logger for the project
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = void 0;
function safeSerialize(obj) {
    try {
        return JSON.parse(JSON.stringify(obj));
    }
    catch (_a) {
        return String(obj);
    }
}
;
exports.logger = {
    error: (msg, meta) => {
        const out = { level: 'error', message: msg };
        if (meta)
            out.meta = safeSerialize(meta);
        // Console used as a fallback sink; in production this should be replaced with a structured logger
        console.error(JSON.stringify(out));
    },
    warn: (msg, meta) => {
        const out = { level: 'warn', message: msg };
        if (meta)
            out.meta = safeSerialize(meta);
        console.warn(JSON.stringify(out));
    },
    info: (msg, meta) => {
        const out = { level: 'info', message: msg };
        if (meta)
            out.meta = safeSerialize(meta);
        console.info(JSON.stringify(out));
    },
    debug: (msg, meta) => {
        const out = { level: 'debug', message: msg };
        if (meta)
            out.meta = safeSerialize(meta);
        console.log(JSON.stringify(out));
    }
};
