"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.listTaxes = exports.listPayouts = void 0;
const tslib_1 = require("tslib");
const auth_1 = require("../utils/auth");
const schemas_1 = require("../database/schemas");
const listPayouts = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user)
            return res.status(401).json({ success: false, error: 'Authentication required' });
        const pageParsed = parseInt(String((_a = req.query.page) !== null && _a !== void 0 ? _a : '1'), 10);
        const limitParsed = parseInt(String((_b = req.query.limit) !== null && _b !== void 0 ? _b : '20'), 10);
        const limitClamped = Math.min(Math.max(Number.isNaN(limitParsed) ? 20 : limitParsed, 1), 100);
        const pageClamped = Math.max(Number.isNaN(pageParsed) ? 1 : pageParsed, 1);
        const query = {};
        // If operator, only their payouts; if admin role in future, can see all
        if (user.role !== 'admin') {
            query.operatorId = String(user._id);
        }
        const payouts = yield schemas_1.Payout.find(query)
            .sort({ createdAt: -1 })
            .limit(limitClamped)
            .skip((pageClamped - 1) * limitClamped)
            .lean();
        res.json({ success: true, data: payouts });
    }
    catch (error) {
        console.error('Error listing payouts:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.listPayouts = listPayouts;
const listTaxes = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user)
            return res.status(401).json({ success: false, error: 'Authentication required' });
        const pageParsed = parseInt(String((_a = req.query.page) !== null && _a !== void 0 ? _a : '1'), 10);
        const limitParsed = parseInt(String((_b = req.query.limit) !== null && _b !== void 0 ? _b : '20'), 10);
        const limitClamped = Math.min(Math.max(Number.isNaN(limitParsed) ? 20 : limitParsed, 1), 100);
        const pageClamped = Math.max(Number.isNaN(pageParsed) ? 1 : pageParsed, 1);
        const subjectType = String(req.query.subjectType || '');
        const query = {};
        if (subjectType && ['commission', 'operator'].includes(String(subjectType))) {
            query.subjectType = subjectType;
        }
        const taxes = yield schemas_1.Tax.find(query)
            .sort({ createdAt: -1 })
            .limit(limitClamped)
            .skip((pageClamped - 1) * limitClamped)
            .lean();
        res.json({ success: true, data: taxes });
    }
    catch (error) {
        console.error('Error listing taxes:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.listTaxes = listTaxes;
