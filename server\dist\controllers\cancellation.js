"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.withdrawCreditBalance = exports.getCreditTransactionHistory = exports.getCreditBalance = exports.getCancellationHistory = exports.cancelJob = void 0;
const tslib_1 = require("tslib");
const zod_1 = require("zod");
const auth_1 = require("../utils/auth");
const cancellationService_1 = require("../services/cancellationService");
const creditBalanceService_1 = require("../services/creditBalanceService");
const schemas_1 = require("../database/schemas");
const notifications_1 = require("./notifications");
const logger_1 = require("../utils/logger");
const cancellationErrors_1 = require("../errors/cancellationErrors");
// Validation schemas
const cancellationRequestSchema = zod_1.z.object({
    reason: zod_1.z.string().min(5).max(500),
    explanation: zod_1.z.string().max(2000).optional(),
    evidenceUrls: zod_1.z.array(zod_1.z.string().url()).optional(),
    cancellationType: zod_1.z.enum([
        'operator_before_start',
        'client_less_than_24h',
        'client_after_start',
        'operator_after_arrival'
    ])
});
const withdrawalRequestSchema = zod_1.z.object({
    amount: zod_1.z.number().min(100), // Minimum 100 centavos (1 DOP)
    withdrawalMethod: zod_1.z.enum(['credit_card', 'bank_transfer']),
    originalPaymentMethodId: zod_1.z.string().optional(),
    currency: zod_1.z.enum(['DOP', 'USD']).optional()
});
const penaltyConfigSchema = zod_1.z.object({
    clientCancellationWorkerCompensation: zod_1.z.number().min(0).max(100).optional(),
    clientCancellationPlatformFee: zod_1.z.number().min(0).max(100).optional(),
    operatorCancellationFinancialPenalty: zod_1.z.number().min(0).optional(),
    operatorCancellationRatingPenalty: zod_1.z.number().min(0).max(5).optional(),
    operatorAfterArrivalFinancialPenalty: zod_1.z.number().min(0).optional(),
    operatorAfterArrivalRatingPenalty: zod_1.z.number().min(0).max(5).optional(),
    ratingPenaltyConfigurable: zod_1.z.boolean().optional(),
    enableAutomaticPenalties: zod_1.z.boolean().optional()
});
/**
 * Process job cancellation
 */
const cancelJob = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user) {
            return res.status(401).json({ success: false, error: 'Authentication required' });
        }
        const { jobId } = req.params;
        const parse = cancellationRequestSchema.safeParse(req.body);
        if (!parse.success) {
            return res.status(400).json({
                success: false,
                error: 'Invalid request data',
                details: parse.error.errors
            });
        }
        const { reason, explanation, evidenceUrls, cancellationType } = parse.data;
        const cancellationService = cancellationService_1.CancellationService.getInstance();
        const request = {
            jobId,
            initiatedBy: String(user._id),
            reason,
            explanation,
            evidenceUrls
        };
        let result;
        switch (cancellationType) {
            case 'operator_before_start':
                result = yield cancellationService.processOperatorCancellationBeforeStart(request);
                break;
            case 'client_less_than_24h':
                result = yield cancellationService.processClientCancellationLessThan24h(request);
                break;
            case 'client_after_start':
                result = yield cancellationService.processClientCancellationAfterStart(request);
                break;
            case 'operator_after_arrival':
                result = yield cancellationService.processOperatorCancellationAfterArrival(request);
                break;
            default:
                return res.status(400).json({
                    success: false,
                    error: 'Invalid cancellation type'
                });
        }
        // Send notifications to relevant parties
        yield sendCancellationNotifications(jobId, cancellationType, String(user._id));
        logger_1.logger.info(`Job ${jobId} cancelled by user ${user._id} with type ${cancellationType}`);
        return res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing job cancellation:', error);
        if (error instanceof cancellationErrors_1.CancellationError) {
            return res.status(error.statusCode).json({
                success: false,
                error: error.message,
                code: error.code
            });
        }
        const message = error instanceof Error ? error.message : 'Failed to process cancellation';
        return res.status(500).json({ success: false, error: message });
    }
});
exports.cancelJob = cancelJob;
/**
 * Get cancellation history for a user
 */
const getCancellationHistory = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user) {
            return res.status(401).json({ success: false, error: 'Authentication required' });
        }
        const { limit = 20, offset = 0, status, type } = req.query;
        const query = { initiatedBy: String(user._id) };
        if (status)
            query.status = status;
        if (type)
            query.type = type;
        const cancellations = yield schemas_1.CancellationRecord.find(query)
            .sort({ createdAt: -1 })
            .limit(Number(limit))
            .skip(Number(offset))
            .populate('jobId', 'description category price date hour')
            .lean();
        const total = yield schemas_1.CancellationRecord.countDocuments(query);
        return res.json({
            success: true,
            data: {
                cancellations,
                pagination: {
                    total,
                    limit: Number(limit),
                    offset: Number(offset),
                    hasMore: total > Number(offset) + Number(limit)
                }
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching cancellation history:', error);
        const message = error instanceof Error ? error.message : 'Failed to fetch cancellation history';
        return res.status(500).json({ success: false, error: message });
    }
});
exports.getCancellationHistory = getCancellationHistory;
/**
 * Get credit balance information
 */
const getCreditBalance = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user) {
            return res.status(401).json({ success: false, error: 'Authentication required' });
        }
        const creditBalanceService = creditBalanceService_1.CreditBalanceService.getInstance();
        const balanceInfo = yield creditBalanceService.getCreditBalanceInfo(String(user._id));
        if (!balanceInfo) {
            return res.json({
                success: true,
                data: {
                    balance: 0,
                    currency: 'DOP',
                    totalCredits: 0,
                    totalWithdrawals: 0,
                    pendingWithdrawals: 0,
                    availableForWithdrawal: 0,
                    lastTransactionAt: null
                }
            });
        }
        return res.json({
            success: true,
            data: balanceInfo
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching credit balance:', error);
        const message = error instanceof Error ? error.message : 'Failed to fetch credit balance';
        return res.status(500).json({ success: false, error: message });
    }
});
exports.getCreditBalance = getCreditBalance;
/**
 * Get credit transaction history
 */
const getCreditTransactionHistory = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user) {
            return res.status(401).json({ success: false, error: 'Authentication required' });
        }
        const { limit = 50, offset = 0 } = req.query;
        const creditBalanceService = creditBalanceService_1.CreditBalanceService.getInstance();
        const transactions = yield creditBalanceService.getTransactionHistory(String(user._id), Number(limit), Number(offset));
        return res.json({
            success: true,
            data: {
                transactions,
                pagination: {
                    limit: Number(limit),
                    offset: Number(offset),
                    hasMore: transactions.length === Number(limit)
                }
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error fetching credit transaction history:', error);
        const message = error instanceof Error ? error.message : 'Failed to fetch transaction history';
        return res.status(500).json({ success: false, error: message });
    }
});
exports.getCreditTransactionHistory = getCreditTransactionHistory;
/**
 * Process credit balance withdrawal
 */
const withdrawCreditBalance = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user) {
            return res.status(401).json({ success: false, error: 'Authentication required' });
        }
        const parse = withdrawalRequestSchema.safeParse(req.body);
        if (!parse.success) {
            return res.status(400).json({
                success: false,
                error: 'Invalid withdrawal request',
                details: parse.error.errors
            });
        }
        const { amount, withdrawalMethod, originalPaymentMethodId, currency } = parse.data;
        const creditBalanceService = creditBalanceService_1.CreditBalanceService.getInstance();
        const withdrawal = yield creditBalanceService.processWithdrawal({
            userId: String(user._id),
            amount,
            withdrawalMethod,
            originalPaymentMethodId,
            currency
        });
        logger_1.logger.info(`Withdrawal processed for user ${user._id}: ${amount} ${currency || 'DOP'}`);
        return res.json({
            success: true,
            data: {
                withdrawalId: String(withdrawal._id),
                amount,
                currency: currency || 'DOP',
                method: withdrawalMethod,
                status: withdrawal.status,
                message: 'Withdrawal request processed successfully'
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing withdrawal:', error);
        if (error instanceof cancellationErrors_1.CancellationError) {
            return res.status(error.statusCode).json({
                success: false,
                error: error.message,
                code: error.code
            });
        }
        const message = error instanceof Error ? error.message : 'Failed to process withdrawal';
        return res.status(500).json({ success: false, error: message });
    }
});
exports.withdrawCreditBalance = withdrawCreditBalance;
/**
 * Send cancellation notifications to relevant parties
 */
function sendCancellationNotifications(jobId, cancellationType, initiatedBy) {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        try {
            const job = yield schemas_1.Job.findById(jobId).populate('ownerId assignedOperatorId');
            if (!job)
                return;
            const clientId = String(job.ownerId);
            const operatorId = job.assignedOperatorId ? String(job.assignedOperatorId) : null;
            // Determine notification recipients based on who initiated
            const notifyClient = initiatedBy !== clientId;
            const notifyOperator = operatorId && initiatedBy !== operatorId;
            // Send client notification
            if (notifyClient) {
                const clientToken = yield (0, notifications_1.getUserNotificationToken)(clientId);
                if (clientToken.success && clientToken.notificationToken) {
                    yield (0, notifications_1.sendCustomNotification)(clientToken.notificationToken, {
                        title: 'Job Cancelled',
                        message: getClientNotificationMessage(cancellationType),
                        data: { type: 'job_cancelled', jobId, cancellationType }
                    });
                }
            }
            // Send operator notification
            if (notifyOperator) {
                const operatorToken = yield (0, notifications_1.getUserNotificationToken)(operatorId);
                if (operatorToken.success && operatorToken.notificationToken) {
                    yield (0, notifications_1.sendCustomNotification)(operatorToken.notificationToken, {
                        title: 'Job Cancelled',
                        message: getOperatorNotificationMessage(cancellationType),
                        data: { type: 'job_cancelled', jobId, cancellationType }
                    });
                }
            }
        }
        catch (error) {
            logger_1.logger.error('Error sending cancellation notifications:', error);
            // Don't throw - notifications are not critical for cancellation processing
        }
    });
}
function getClientNotificationMessage(cancellationType) {
    switch (cancellationType) {
        case 'operator_before_start':
            return 'The operator has cancelled your job. You have been fully refunded and can repost or reschedule.';
        case 'operator_after_arrival':
            return 'The operator has cancelled after arriving. You have been fully refunded. A penalty has been applied to the operator.';
        default:
            return 'Your job has been cancelled.';
    }
}
function getOperatorNotificationMessage(cancellationType) {
    switch (cancellationType) {
        case 'client_less_than_24h':
            return 'The client has cancelled the job with less than 24h notice. You will receive compensation.';
        case 'client_after_start':
            return 'The client has cancelled the job after it started. You will receive compensation for work done.';
        default:
            return 'The job has been cancelled.';
    }
}
