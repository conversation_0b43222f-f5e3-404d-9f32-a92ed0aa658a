"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthenticateTokenOAuth = void 0;
exports.AuthenticateToken = AuthenticateToken;
exports.signToken = signToken;
exports.signTokenOAuth = signTokenOAuth;
const tslib_1 = require("tslib");
const schemas_1 = require("../database/schemas");
const jsonwebtoken_1 = tslib_1.__importDefault(require("jsonwebtoken"));
// Escape regex metacharacters to avoid unintended behavior when building RegExp from user input
function escapeRegex(input) {
    return input.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
function AuthenticateToken(req) {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        var _a;
        const header = (_a = req.headers.authorization) !== null && _a !== void 0 ? _a : "";
        const [scheme, token] = header.split(" ");
        if (scheme !== "Bearer" || !token || token === "null") {
            return { success: false, account: null };
        }
        const jwtSecret = process.env.JWT_SECRET;
        if (!jwtSecret) {
            throw new Error('JWT_SECRET is not defined');
        }
        try {
            const decoded = jsonwebtoken_1.default.verify(token, jwtSecret);
            const account = yield schemas_1.Account.findById(decoded.sub)
                .select("-password") // include other fields that downstream expects
                .lean();
            if (!account) {
                return {
                    success: false,
                    account: null,
                };
            }
            return {
                success: true,
                account: account,
            };
        }
        catch (error) {
            return {
                success: false,
                account: null,
            };
        }
    });
}
const AuthenticateTokenOAuth = (req, res, next) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d;
    if (!req.headers.authorization) {
        res.status(401).json({ success: false, error: 'No authorization header provided' });
        return;
    }
    const authParts = req.headers.authorization.split(' ');
    if (authParts[0] !== 'Bearer') {
        res.status(401).json({ success: false, error: 'Invalid token format' });
        return;
    }
    if (authParts[1] === 'null' || !authParts[1]) {
        res.status(401).json({ success: false, error: 'Null token provided' });
        return;
    }
    const token = authParts[1];
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
        console.error('JWT_SECRET is not defined in environment variables');
        next(new Error('JWT_SECRET is not defined'));
        return;
    }
    try {
        const decoded = jsonwebtoken_1.default.verify(token, jwtSecret);
        // Look up by subject (account id) from token; email used only for sanity/logging
        const sub = String(decoded.sub || '');
        let account = sub ? yield schemas_1.Account.findById(sub).select('-password -settings -contacts').lean() : null;
        if (!account && decoded.email) {
            // Fallback: find by email if sub was missing (legacy tokens)
            const escapedEmail = escapeRegex(String(decoded.email || ''));
            const emailRegex = new RegExp(`^${escapedEmail}$`, 'i');
            account = yield schemas_1.Account.findOne({ email: { $regex: emailRegex } })
                .select('-password -settings -contacts')
                .lean();
        }
        if (!account) {
            res.status(401).json({ success: false, error: 'Invalid credentials' });
            return;
        }
        // Convert MongoDB document to AuthenticatedUser format
        const acc = account;
        // Derive a single role, preferring explicit admin/operator, else fallback to user.user.type
        const derivedRole = ((_a = acc.booleans) === null || _a === void 0 ? void 0 : _a.isAdmin) ? 'admin'
            : (((_b = acc.booleans) === null || _b === void 0 ? void 0 : _b.isOperator) || ((_c = acc.user) === null || _c === void 0 ? void 0 : _c.type) === 'operator') ? 'operator'
                : (((_d = acc.user) === null || _d === void 0 ? void 0 : _d.type) || 'user');
        req.user = {
            _id: String(acc._id),
            email: acc.email,
            role: derivedRole,
            user: acc.user,
            contacts: acc.contacts,
            settings: acc.settings,
            finances: acc.finances,
            booleans: acc.booleans, // deprecated but kept for backward compatibility
            notifications: acc.notifications,
            createdAt: acc.createdAt,
        };
        next();
    }
    catch (error) {
        console.error('Authentication error:', error);
        // Handle JWT-specific errors with 401 status
        if (error instanceof Error) {
            if (error.name === 'TokenExpiredError') {
                res.status(401).json({ success: false, error: 'Token expired' });
                return;
            }
            if (error.name === 'JsonWebTokenError') {
                res.status(401).json({ success: false, error: 'Invalid token' });
                return;
            }
            if (error.name === 'NotBeforeError') {
                res.status(401).json({ success: false, error: 'Token not active' });
                return;
            }
        }
        // For other errors, return 500 (server error)
        if (error instanceof Error) {
            res.status(500).json({ success: false, error: `Authentication Error: ${error.message}` });
        }
        else {
            res.status(500).json({ success: false, error: 'Authentication Error' });
        }
    }
});
exports.AuthenticateTokenOAuth = AuthenticateTokenOAuth;
function signToken(email, accountId) {
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
        throw new Error('JWT_SECRET is not defined');
    }
    return jsonwebtoken_1.default.sign({ sub: accountId, email: email.toLowerCase() }, jwtSecret, { expiresIn: '7d' });
}
function signTokenOAuth(email, accountId) {
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
        throw new Error('JWT_SECRET is not defined');
    }
    return jsonwebtoken_1.default.sign({ sub: accountId, email: String(email || '').toLowerCase() }, jwtSecret, { expiresIn: '7d' });
}
