import { Request } from 'express';
import { AuthenticatedUser } from '../types/express';

// Type guard to check if user is authenticated
export function isAuthenticated(req: Request): req is Request & { user: AuthenticatedUser } {
  // Treat both undefined and null as unauthenticated
  return req.user != null;
}

// Type assertion utility for authenticated requests
export function getAuthenticatedUser(req: Request): AuthenticatedUser | undefined {
  return isAuthenticated(req) ? req.user : undefined;
}
