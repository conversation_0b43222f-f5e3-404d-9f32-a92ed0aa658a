import React, { useRef, useEffect, useState } from 'react';
import { StyleSheet, Animated } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';

import { useChat } from '@/context/ChatContext';
import { ScreenWrapper } from '@/components/atoms/blur/BlurDrawer';

import Page from '@/components/templates/Page';
import Header from '@/components/templates/Header';
import ChatStarter from '@/components/organisms/chat/ChatStarter';
import ChatInputArea from '@/components/organisms/chat/ChatInputArea';
import { ChatAgentBubbles } from '@/components/organisms/chat/ChatAgentBubbles';
import HandAnimation from '@/components/atoms/loaders/Hand';

const Index = () => {
  const scrollViewRef = useRef<ScrollView>(null);
  const { messages, handleNewChat, chatId } = useChat();
  const [isLoading, setIsLoading] = useState(true)

  // Animation values
  const chatStarterOpacity = useRef(new Animated.Value(1)).current; // Start with ChatStarter visible
  const chatBubblesOpacity = useRef(new Animated.Value(0)).current; // Start with ChatAgentBubbles hidden


  const scrollToBottom = () => {
    scrollViewRef.current?.scrollToEnd({ animated: true });
  };

  const handleStartChat = async () => {
    const call = await handleNewChat()
    setIsLoading(false)
  }

  useEffect(() => {
    handleStartChat()
  }, [])



  useEffect(() => {
    // Animate based on messages length
    if (messages.length > 0) {
      // Fade out ChatStarter, fade in ChatAgentBubbles
      Animated.parallel([
        Animated.timing(chatStarterOpacity, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(chatBubblesOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Fade in ChatStarter, fade out ChatAgentBubbles
      Animated.parallel([
        Animated.timing(chatStarterOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(chatBubblesOpacity, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start();
    }

    // Scroll to bottom when messages change
    setTimeout(() => scrollToBottom(), 100);
  }, [messages]);

  return (

    <Page noPaddingTop noBottomBar alignItems="center" justifyContent="space-between" page="agent">
      <Header buttonBack buttonWrite text=' ' />

      {
        !isLoading &&
        <>
          <ScrollView
            ref={scrollViewRef}
            style={styles.chatContainer}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.chatContent}
          >

            {/* New Chat Fallback (Start Screen) */}
            <Animated.View style={{ opacity: chatStarterOpacity }}>
              {
                messages.length === 0 &&
                <ChatStarter />
              }
            </Animated.View>

            {/* Message Feed */}
            <Animated.View style={{ opacity: chatBubblesOpacity }}>
              {
                messages.length > 0 &&
                messages.map((m, i) => (
                  <ChatAgentBubbles
                    key={i}
                    isLastMessage={i == messages.length - 1}
                    message={m}
                    userIngredients={[]}
                  />
                ))}
            </Animated.View>



          </ScrollView>
          {
            messages.length === 0 &&
            <HandAnimation />
          }
          {
            !messages.find(m => m.action == 'done') &&
            <ChatInputArea />
          }
        </>
      }

    </Page>

  );
};

export default Index;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  profileButton: {
    padding: 4,
  },
  profileAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  chatContainer: {

    flex: 1,
    width: '100%',
    paddingHorizontal: 15,
    paddingBottom: 30,
  },
  chatContent: {
    width: '100%',
    paddingBottom: 20,
    paddingTop: 100,
  },
});