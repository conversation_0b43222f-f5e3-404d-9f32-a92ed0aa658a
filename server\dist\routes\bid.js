"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const express_1 = tslib_1.__importDefault(require("express"));
const router = express_1.default.Router();
const middleware_1 = require("../middleware");
const authentication_1 = require("../middleware/authentication");
const bid_1 = require("../controllers/bid");
// Create a bid for a job
router.route("/jobs/:jobId/bids").post(authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(bid_1.createBid));
// Get all bids for a specific job (for job owners)
router.route("/jobs/:jobId/bids").get(authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(bid_1.getJobBids));
// Accept a specific bid (for job owners)
router.route("/bids/:bidId/accept").post(authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(bid_1.acceptBid));
// Get all bids made by the current operator
router.route("/my-bids").get(authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(bid_1.getOperatorBids));
// Withdraw a bid (for operators)
router.route("/bids/:bidId/withdraw").post(authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(bid_1.withdrawBid));
exports.default = router;
