"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_1 = require("../middleware/authentication");
const cancellation_1 = require("../controllers/cancellation");
const router = (0, express_1.Router)();
// Job Cancellation Routes
router.post('/jobs/:jobId/cancel', authentication_1.AuthenticateTokenOAuth, cancellation_1.cancelJob);
router.get('/history', authentication_1.AuthenticateTokenOAuth, cancellation_1.getCancellationHistory);
// Credit Balance Routes
router.get('/credit-balance', authentication_1.AuthenticateTokenOAuth, cancellation_1.getCreditBalance);
router.get('/credit-transactions', authentication_1.AuthenticateTokenOAuth, cancellation_1.getCreditTransactionHistory);
router.post('/credit-balance/withdraw', authentication_1.AuthenticateTokenOAuth, cancellation_1.withdrawCreditBalance);
exports.default = router;
