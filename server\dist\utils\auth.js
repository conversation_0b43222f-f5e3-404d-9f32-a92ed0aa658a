"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isAuthenticated = isAuthenticated;
exports.getAuthenticatedUser = getAuthenticatedUser;
// Type guard to check if user is authenticated
function isAuthenticated(req) {
    // Treat both undefined and null as unauthenticated
    return req.user != null;
}
// Type assertion utility for authenticated requests
function getAuthenticatedUser(req) {
    return isAuthenticated(req) ? req.user : undefined;
}
