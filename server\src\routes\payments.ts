import { Router } from 'express';
import { AuthenticateTokenOAuth } from '../middleware/authentication';
import {
  createPaymentMethod,
  getPaymentMethods,
  setFavoritePaymentMethod,
  deletePaymentMethod,
  chargePaymentMethod,
  handle3DSMethodNotification,
  process3DSChallenge,
  refundTransaction,
  captureTransaction
} from '../controllers/payments';

const router = Router();

// Payment Methods Management
router.post('/methods', AuthenticateTokenOAuth, createPaymentMethod);
router.get('/methods', AuthenticateTokenOAuth, getPaymentMethods);
router.patch('/methods/:id/favorite', AuthenticateTokenOAuth, setFavoritePaymentMethod);
router.delete('/methods/:id', AuthenticateTokenOAuth, deletePaymentMethod);

// Payment Processing
router.post('/methods/:id/charge', AuthenticateTokenOAuth, chargePaymentMethod);

// 3DS Flow Endpoints
router.post('/3ds/method-notification/:orderId', handle3DSMethodNotification); // No auth - called by ACS
router.post('/3ds/challenge/:transactionId', AuthenticateTokenOAuth, process3DSChallenge);

// Transaction Management
router.post('/payments/:transactionId/refund', AuthenticateTokenOAuth, refundTransaction);
router.post('/payments/:transactionId/capture', AuthenticateTokenOAuth, captureTransaction);

// Job/Bid payments flow
import { processEscrow, completeJobAndPayout, cancelJobAndRefund } from '../controllers/jobPayments';
router.post('/jobs/:jobId/process-escrow', AuthenticateTokenOAuth, processEscrow);
router.post('/jobs/:jobId/complete', AuthenticateTokenOAuth, completeJobAndPayout);
router.post('/jobs/:jobId/cancel', AuthenticateTokenOAuth, cancelJobAndRefund);

// Payouts & taxes
import { listPayouts, listTaxes } from '../controllers/taxPayout';
router.get('/payouts', AuthenticateTokenOAuth, listPayouts);
router.get('/taxes', AuthenticateTokenOAuth, listTaxes);

export default router;
