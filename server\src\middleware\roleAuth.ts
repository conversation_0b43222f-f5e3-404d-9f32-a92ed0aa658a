import { Request, Response, NextFunction } from 'express';
import { getAuthenticatedUser } from '../utils/auth';

/**
 * Generic middleware factory to require specific roles
 */
export const requireRoles = (allowedRoles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const user = getAuthenticatedUser(req);

      if (!user) {
        return res.status(401).json({ success: false, error: 'Authentication required' });
      }

      if (!allowedRoles.includes(user.role)) {
        const msg = `${allowedRoles.join(' or ')} access required`;
        return res.status(403).json({ success: false, error: msg });
      }

      next();
    } catch (error) {
      return next(error);
    }
  };
};

/**
 * Middleware to require admin access
 */
export const requireAdmin = requireRoles(['admin']);

/**
 * Middleware to require operator access
 */
export const requireOperator = requireRoles(['operator']);

/**
 * Middleware to require admin or operator access
 */
export const requireAdminOrOperator = requireRoles(['admin', 'operator']);
