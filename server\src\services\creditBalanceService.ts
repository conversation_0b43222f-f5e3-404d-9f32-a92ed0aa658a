import mongoose from 'mongoose';
import { CreditBalance, CreditTransaction } from '../database/schemas';
import { PaymentProcessorService } from './paymentProcessorService';
import { logger } from '../utils/logger';

export interface CreditBalanceCreateOptions {
  userId: string;
  currency?: string;
}

export interface CreditTransactionOptions {
  userId: string;
  type: 'credit' | 'debit' | 'withdrawal' | 'refund' | 'penalty_compensation';
  amount: number;
  currency?: string;
  description: string;
  metadata?: {
    jobId?: string;
    bidId?: string;
    cancellationId?: string;
    paymentTransactionId?: string;
    withdrawalMethod?: 'credit_card' | 'bank_transfer';
    originalPaymentMethodId?: string;
    processingFee?: number;
    reason?: string;
  };
}

export interface WithdrawalOptions {
  userId: string;
  amount: number;
  withdrawalMethod: 'credit_card' | 'bank_transfer';
  originalPaymentMethodId?: string;
  currency?: string;
}

export interface CreditBalanceInfo {
  balance: number;
  currency: string;
  totalCredits: number;
  totalWithdrawals: number;
  pendingWithdrawals: number;
  availableForWithdrawal: number;
  lastTransactionAt: Date | null;
}

export class CreditBalanceService {
  private static instance: CreditBalanceService;
  private paymentProcessor: PaymentProcessorService;

  private constructor() {
    this.paymentProcessor = PaymentProcessorService.getInstance();
  }

  public static getInstance(): CreditBalanceService {
    if (!CreditBalanceService.instance) {
      CreditBalanceService.instance = new CreditBalanceService();
    }
    return CreditBalanceService.instance;
  }

  /**
   * Get or create credit balance for a user
   */
  async getOrCreateCreditBalance(options: CreditBalanceCreateOptions): Promise<any> {
    const { userId, currency = 'DOP' } = options;

    let creditBalance = await CreditBalance.findOne({ userId });
    
    if (!creditBalance) {
      creditBalance = await CreditBalance.create({
        userId,
        currency,
        balance: 0,
        totalCredits: 0,
        totalWithdrawals: 0,
        pendingWithdrawals: 0,
        lastTransactionAt: null
      });
      
      logger.info(`Created new credit balance for user ${userId}`);
    }

    return creditBalance;
  }

  /**
   * Get credit balance information for a user
   */
  async getCreditBalanceInfo(userId: string): Promise<CreditBalanceInfo | null> {
    const creditBalance = await CreditBalance.findOne({ userId });
    
    if (!creditBalance) {
      return null;
    }

    const availableForWithdrawal = Math.max(0, creditBalance.balance - creditBalance.pendingWithdrawals);

    return {
      balance: creditBalance.balance,
      currency: creditBalance.currency,
      totalCredits: creditBalance.totalCredits,
      totalWithdrawals: creditBalance.totalWithdrawals,
      pendingWithdrawals: creditBalance.pendingWithdrawals,
      availableForWithdrawal,
      lastTransactionAt: creditBalance.lastTransactionAt
    };
  }

  /**
   * Add credit to user's balance
   */
  async addCredit(options: CreditTransactionOptions): Promise<any> {
    if (options.type !== 'credit' && options.type !== 'refund' && options.type !== 'penalty_compensation') {
      throw new Error('Invalid transaction type for adding credit');
    }

    if (options.amount <= 0) {
      throw new Error('Credit amount must be positive');
    }

    const session = await mongoose.startSession();
    
    try {
      return await session.withTransaction(async () => {
        // Get or create credit balance
        const creditBalance = await this.getOrCreateCreditBalance({
          userId: options.userId,
          currency: options.currency
        });

        // Create credit transaction
        const transaction = await CreditTransaction.create([{
          userId: options.userId,
          creditBalanceId: String(creditBalance._id),
          type: options.type,
          amount: options.amount,
          currency: options.currency || 'DOP',
          status: 'completed',
          description: options.description,
          metadata: options.metadata || {},
          processedAt: new Date()
        }], { session });

        // Update credit balance
        await CreditBalance.findByIdAndUpdate(
          creditBalance._id,
          {
            $inc: {
              balance: options.amount,
              totalCredits: options.amount
            },
            $set: {
              lastTransactionAt: new Date()
            }
          },
          { session }
        );

        logger.info(`Added ${options.amount} credit to user ${options.userId}`);
        return transaction[0];
      });
    } finally {
      await session.endSession();
    }
  }

  /**
   * Deduct from user's balance (for internal use)
   */
  async deductBalance(options: CreditTransactionOptions): Promise<any> {
    if (options.type !== 'debit' && options.type !== 'withdrawal') {
      throw new Error('Invalid transaction type for deducting balance');
    }

    if (options.amount <= 0) {
      throw new Error('Deduction amount must be positive');
    }

    const session = await mongoose.startSession();
    
    try {
      return await session.withTransaction(async () => {
        const creditBalance = await CreditBalance.findOne({ userId: options.userId }).session(session);
        
        if (!creditBalance) {
          throw new Error('Credit balance not found');
        }

        // Check if sufficient balance
        const availableBalance = creditBalance.balance - creditBalance.pendingWithdrawals;
        if (availableBalance < options.amount) {
          throw new Error('Insufficient credit balance');
        }

        // Create debit transaction
        const transaction = await CreditTransaction.create([{
          userId: options.userId,
          creditBalanceId: String(creditBalance._id),
          type: options.type,
          amount: -options.amount, // Negative for debit
          currency: options.currency || creditBalance.currency,
          status: 'completed',
          description: options.description,
          metadata: options.metadata || {},
          processedAt: new Date()
        }], { session });

        // Update credit balance
        const updateFields: any = {
          $inc: {
            balance: -options.amount
          },
          $set: {
            lastTransactionAt: new Date()
          }
        };

        if (options.type === 'withdrawal') {
          updateFields.$inc.totalWithdrawals = options.amount;
        }

        await CreditBalance.findByIdAndUpdate(
          creditBalance._id,
          updateFields,
          { session }
        );

        logger.info(`Deducted ${options.amount} from user ${options.userId} balance`);
        return transaction[0];
      });
    } finally {
      await session.endSession();
    }
  }

  /**
   * Process withdrawal to original payment method
   */
  async processWithdrawal(options: WithdrawalOptions): Promise<any> {
    if (options.amount <= 0) {
      throw new Error('Withdrawal amount must be positive');
    }

    const session = await mongoose.startSession();
    
    try {
      return await session.withTransaction(async () => {
        const creditBalance = await CreditBalance.findOne({ userId: options.userId }).session(session);
        
        if (!creditBalance) {
          throw new Error('Credit balance not found');
        }

        // Check available balance
        const availableBalance = creditBalance.balance - creditBalance.pendingWithdrawals;
        if (availableBalance < options.amount) {
          throw new Error('Insufficient available balance for withdrawal');
        }

        // Create pending withdrawal transaction
        const transaction = await CreditTransaction.create([{
          userId: options.userId,
          creditBalanceId: String(creditBalance._id),
          type: 'withdrawal',
          amount: -options.amount,
          currency: options.currency || creditBalance.currency,
          status: 'pending',
          description: `Withdrawal to ${options.withdrawalMethod}`,
          metadata: {
            withdrawalMethod: options.withdrawalMethod,
            originalPaymentMethodId: options.originalPaymentMethodId,
            processingFee: 0 // TODO: Calculate processing fee based on method
          }
        }], { session });

        // Update pending withdrawals
        await CreditBalance.findByIdAndUpdate(
          creditBalance._id,
          {
            $inc: {
              pendingWithdrawals: options.amount
            },
            $set: {
              lastTransactionAt: new Date()
            }
          },
          { session }
        );

        // Process withdrawal through payment processor
        try {
          // TODO: Implement actual withdrawal processing based on method
          if (options.withdrawalMethod === 'credit_card' && options.originalPaymentMethodId) {
            // Process refund to original payment method
            // This would integrate with the existing payment processor
            logger.info(`Processing withdrawal to credit card for user ${options.userId}`);
          } else if (options.withdrawalMethod === 'bank_transfer') {
            // Process bank transfer
            logger.info(`Processing bank transfer withdrawal for user ${options.userId}`);
          }

          // For now, mark as completed (in real implementation, this would be async)
          await CreditTransaction.findByIdAndUpdate(
            transaction[0]._id,
            {
              status: 'completed',
              processedAt: new Date()
            },
            { session }
          );

          // Update balances
          await CreditBalance.findByIdAndUpdate(
            creditBalance._id,
            {
              $inc: {
                balance: -options.amount,
                totalWithdrawals: options.amount,
                pendingWithdrawals: -options.amount
              }
            },
            { session }
          );

          logger.info(`Completed withdrawal of ${options.amount} for user ${options.userId}`);
          return transaction[0];

        } catch (processingError) {
          // Mark transaction as failed and restore pending balance
          await CreditTransaction.findByIdAndUpdate(
            transaction[0]._id,
            {
              status: 'failed',
              processedAt: new Date()
            },
            { session }
          );

          await CreditBalance.findByIdAndUpdate(
            creditBalance._id,
            {
              $inc: {
                pendingWithdrawals: -options.amount
              }
            },
            { session }
          );

          throw new Error(`Withdrawal processing failed: ${processingError instanceof Error ? processingError.message : String(processingError)}`);
        }
      });
    } finally {
      await session.endSession();
    }
  }

  /**
   * Get transaction history for a user
   */
  async getTransactionHistory(userId: string, limit: number = 50, offset: number = 0): Promise<any[]> {
    return await CreditTransaction.find({ userId })
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(offset)
      .lean();
  }

  /**
   * Get pending withdrawals for a user
   */
  async getPendingWithdrawals(userId: string): Promise<any[]> {
    return await CreditTransaction.find({
      userId,
      type: 'withdrawal',
      status: 'pending'
    })
    .sort({ createdAt: -1 })
    .lean();
  }
}
