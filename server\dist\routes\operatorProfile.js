"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const express_1 = tslib_1.__importDefault(require("express"));
const router = express_1.default.Router();
const middleware_1 = require("../middleware");
const authentication_1 = require("../middleware/authentication");
const operatorProfile_1 = require("../controllers/operatorProfile");
// Create or update operator profile
router.route("/profile").post(authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(operatorProfile_1.createOrUpdateOperatorProfile));
// Get operator profile (own or by ID)
router.route("/profile").get(authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(operatorProfile_1.getOperatorProfile));
router.route("/profile/:operatorId").get(authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(operatorProfile_1.getOperatorProfile));
// Update operator availability
router.route("/availability").patch(authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(operatorProfile_1.updateOperatorAvailability));
// Switch user role between 'user' and 'operator'
router.route("/switch-role").post(authentication_1.AuthenticateTokenOAuth, (0, middleware_1.tryCatch)(operatorProfile_1.switchUserRole));
// Get operators by category (for browsing)
router.route("/category/:category").get((0, middleware_1.tryCatch)(operatorProfile_1.getOperatorsByCategory));
exports.default = router;
