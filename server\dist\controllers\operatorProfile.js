"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateOperatorRating = exports.getOperatorsByCategory = exports.switchUserRole = exports.updateOperatorAvailability = exports.getOperatorProfile = exports.createOrUpdateOperatorProfile = void 0;
const tslib_1 = require("tslib");
const schemas_1 = require("../database/schemas");
const jobCategoryService_1 = tslib_1.__importDefault(require("../services/jobCategoryService"));
const auth_1 = require("../utils/auth");
// Helper function to safely compare IDs
function isSameId(id1, id2) {
    if (!id1 || !id2)
        return false;
    return String(id1).trim() === String(id2).trim();
}
// Create or update operator profile
const createOrUpdateOperatorProfile = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        const { authorizedCategories, isAvailable, description, skills, portfolio } = req.body;
        // Validate required fields
        if (!authorizedCategories || !Array.isArray(authorizedCategories) || authorizedCategories.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'At least one authorized category is required'
            });
        }
        // Validate categories using active job categories
        const activeCategories = yield jobCategoryService_1.default.getActiveCategories();
        const validCategoryNames = new Set(activeCategories.map(c => c.name.toLowerCase()));
        const invalidCategories = authorizedCategories.filter((cat) => !validCategoryNames.has(String(cat).trim().toLowerCase()));
        if (invalidCategories.length > 0) {
            return res.status(400).json({
                success: false,
                error: `Invalid categories: ${invalidCategories.join(', ')}`
            });
        }
        // Ensure user is already an approved operator
        const account = yield schemas_1.Account.findById(userId);
        if (!account) {
            return res.status(404).json({ success: false, error: 'Account not found' });
        }
        const isOperator = ((_a = account.booleans) === null || _a === void 0 ? void 0 : _a.isOperator) === true || (user === null || user === void 0 ? void 0 : user.role) === 'operator' || (user === null || user === void 0 ? void 0 : user.role) === 'admin';
        if (!isOperator) {
            return res.status(403).json({
                success: false,
                error: 'Only operators or admins can create/update an operator profile. Others must submit an operator application.'
            });
        }
        // Create or update operator profile
        const profileData = {
            accountId: userId,
            authorizedCategories,
            isAvailable: isAvailable !== undefined ? isAvailable : true,
            description: description !== undefined ? description : null,
            skills: skills !== undefined ? skills : [],
            portfolio: portfolio !== undefined ? portfolio : []
        };
        let isNewProfile = false;
        const existingProfile = yield schemas_1.OperatorProfile.findOne({ accountId: userId });
        if (!existingProfile) {
            isNewProfile = true;
        }
        const operatorProfile = yield schemas_1.OperatorProfile.findOneAndUpdate({ accountId: userId }, profileData, { new: true, upsert: true });
        res.json({
            success: true,
            message: isNewProfile
                ? 'Operator profile created successfully'
                : 'Operator profile updated successfully',
            profile: operatorProfile
        });
    }
    catch (error) {
        console.error('Error creating/updating operator profile:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.createOrUpdateOperatorProfile = createOrUpdateOperatorProfile;
// Get operator profile
const getOperatorProfile = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { operatorId } = req.params;
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        // Use provided operatorId or current user's ID
        const targetId = operatorId || userId;
        const operatorProfile = yield schemas_1.OperatorProfile.findOne({ accountId: targetId })
            .populate('accountId', 'user.name user.surname user.username user.profile_picture');
        if (!operatorProfile) {
            return res.status(404).json({ success: false, error: 'Operator profile not found' });
        }
        res.json({
            success: true,
            profile: operatorProfile
        });
    }
    catch (error) {
        console.error('Error getting operator profile:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.getOperatorProfile = getOperatorProfile;
// Update operator availability
const updateOperatorAvailability = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { isAvailable } = req.body;
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        if (typeof isAvailable !== 'boolean') {
            return res.status(400).json({
                success: false,
                error: 'isAvailable must be a boolean value'
            });
        }
        const operatorProfile = yield schemas_1.OperatorProfile.findOne({ accountId: userId });
        if (!operatorProfile) {
            return res.status(404).json({ success: false, error: 'Operator profile not found' });
        }
        operatorProfile.isAvailable = isAvailable;
        yield operatorProfile.save();
        res.json({
            success: true,
            message: `Availability updated to ${isAvailable ? 'available' : 'unavailable'}`,
            profile: operatorProfile
        });
    }
    catch (error) {
        console.error('Error updating operator availability:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.updateOperatorAvailability = updateOperatorAvailability;
// Switch user role (between user and operator)
const switchUserRole = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = user === null || user === void 0 ? void 0 : user._id;
        const { role } = req.body;
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        if (!['user', 'operator'].includes(role)) {
            return res.status(400).json({
                success: false,
                error: 'Role must be either "user" or "operator"'
            });
        }
        const account = yield schemas_1.Account.findById(userId);
        if (!account) {
            return res.status(404).json({ success: false, error: 'Account not found' });
        }
        // If switching to operator role, require admin approval AND an existing operator profile
        if (role === 'operator') {
            if (!((_a = account.booleans) === null || _a === void 0 ? void 0 : _a.isOperator)) {
                return res.status(403).json({
                    success: false,
                    error: 'Operator access must be approved by an administrator'
                });
            }
            const operatorProfile = yield schemas_1.OperatorProfile.findOne({ accountId: userId });
            if (!operatorProfile || !Array.isArray(operatorProfile.authorizedCategories) || operatorProfile.authorizedCategories.length === 0) {
                return res.status(400).json({
                    success: false,
                    error: 'User must have an approved operator profile with at least one authorized category'
                });
            }
        }
        account.currentRole = role;
        yield account.save();
        res.json({
            success: true,
            message: `Role switched to ${role}`,
            currentRole: role
        });
    }
    catch (error) {
        console.error('Error switching user role:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.switchUserRole = switchUserRole;
// Get operators by category (for job matching)
const getOperatorsByCategory = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const { category } = req.params;
        const { page = 1, limit = 10, available = 'true' } = req.query;
        // Validate category against active job categories
        const isValid = yield jobCategoryService_1.default.isCategoryValid(String(category));
        if (!isValid) {
            return res.status(400).json({
                success: false,
                error: 'Invalid category'
            });
        }
        // Build query
        const query = {
            authorizedCategories: category
        };
        if (available === 'true') {
            query.isAvailable = true;
        }
        const operators = yield schemas_1.OperatorProfile.find(query)
            .populate('accountId', 'user.name user.surname user.username user.profile_picture')
            .sort({ rating: -1, totalJobsCompleted: -1 })
            .limit(Number(limit))
            .skip((Number(page) - 1) * Number(limit));
        const total = yield schemas_1.OperatorProfile.countDocuments(query);
        res.json({
            success: true,
            operators,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total,
                pages: Math.ceil(total / Number(limit))
            }
        });
    }
    catch (error) {
        console.error('Error getting operators by category:', error);
        res.status(500).json({ success: false, error: 'Internal server error' });
    }
});
exports.getOperatorsByCategory = getOperatorsByCategory;
// Update operator rating (after job completion)
const updateOperatorRating = (operatorId, newRating) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        // Validate rating range (assuming 1-5 scale)
        if (newRating < 1 || newRating > 5) {
            throw new Error(`Invalid rating: ${newRating}. Rating must be between 1 and 5.`);
        }
        const operatorProfile = yield schemas_1.OperatorProfile.findOne({ accountId: operatorId });
        if (!operatorProfile) {
            throw new Error(`Operator profile not found for ID: ${operatorId}`);
        }
        // Calculate new average rating
        const totalRatings = operatorProfile.totalJobsCompleted;
        const currentRating = operatorProfile.rating;
        const newAverageRating = ((currentRating * totalRatings) + newRating) / (totalRatings + 1);
        operatorProfile.rating = Math.round(newAverageRating * 100) / 100; // Round to 2 decimal places
        operatorProfile.totalJobsCompleted += 1;
        yield operatorProfile.save();
    }
    catch (error) {
        console.error('Error updating operator rating:', error);
        throw error; // Propagate error to caller for proper handling
    }
});
exports.updateOperatorRating = updateOperatorRating;
