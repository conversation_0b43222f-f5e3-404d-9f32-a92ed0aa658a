"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importStar(require("mongoose"));
const documentRequirements_1 = require("../../types/documentRequirements");
const operatorDocumentSchema = new mongoose_1.Schema({
    name: { type: String, required: true, trim: true, minlength: 1, maxlength: 256 },
    url: {
        type: String,
        required: true,
        trim: true,
        match: [/^https?:\/\/\S+$/i, 'Invalid URL format'],
    },
    type: {
        type: String,
        required: true,
        trim: true,
        enum: [
            'application/pdf',
            'image/jpeg',
            'image/png',
            'image/gif',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ],
        message: 'Document type not allowed'
    },
    category: {
        type: String,
        enum: documentRequirements_1.DOCUMENT_REQUIREMENT_CATEGORIES,
        required: false,
    },
}, { _id: false });
const operatorApplicationSchema = new mongoose_1.Schema({
    applicantId: {
        type: mongoose_1.Schema.Types.ObjectId,
        required: true,
        ref: 'Account',
        index: true,
    },
    authorizedCategories: [
        {
            type: String,
            trim: true,
            lowercase: true,
        }
    ],
    description: { type: String, maxlength: 2000 }, // optional
    skills: [{ type: String, maxlength: 100 }], // optional
    documents: { type: [operatorDocumentSchema], default: [] },
    documents_required: {
        type: [String],
        default: ['none'],
        validate: {
            validator: function (arr) {
                if (!Array.isArray(arr))
                    return false;
                // All values must be in allowed set
                return arr.every((v) => documentRequirements_1.DOCUMENT_REQUIREMENT_CATEGORIES.includes(String(v)));
            },
            message: 'Invalid document requirement category provided',
        },
    },
    status: {
        type: String,
        enum: ['pending', 'approved', 'rejected'],
        default: 'pending',
        index: true,
    },
    reviewedBy: { type: mongoose_1.Schema.Types.ObjectId, ref: 'Account' },
    reviewedAt: { type: Date },
    reviewNote: { type: String },
}, { timestamps: true });
// Ensure at least one authorized category is provided
operatorApplicationSchema.path('authorizedCategories').validate(function (value) {
    return Array.isArray(value) && value.length > 0;
}, 'At least one authorized category is required');
// Validate that, if documents are required (non-'none'), each required category has at least one document in that category
operatorApplicationSchema.pre('validate', function (next) {
    const app = this;
    const required = (app.documents_required || []).filter((c) => c !== 'none');
    if (required.length === 0)
        return next();
    const docs = Array.isArray(app.documents) ? app.documents : [];
    const categoriesWithDocs = new Set(docs
        .map((d) => d === null || d === void 0 ? void 0 : d.category)
        .filter((c) => typeof c === 'string'));
    const missing = required.filter((c) => !categoriesWithDocs.has(c));
    if (missing.length > 0) {
        return next(new Error(`Missing required documents for categories: ${missing.join(', ')}`));
    }
    return next();
});
operatorApplicationSchema.index({ applicantId: 1, status: 1, createdAt: -1 });
exports.default = mongoose_1.default.model('OperatorApplication', operatorApplicationSchema);
