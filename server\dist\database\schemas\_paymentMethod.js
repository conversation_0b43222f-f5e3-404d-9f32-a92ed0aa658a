"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importStar(require("mongoose"));
const paymentMethodSchema = new mongoose_1.default.Schema({
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        required: true,
        ref: 'Account'
    },
    provider: {
        type: String,
        enum: ['azul'],
        default: 'azul',
        required: true
    },
    dataVaultToken: {
        type: String,
        required: true,
        unique: true, // Azul tokens should be unique
        select: false,
        immutable: true
    },
    last4: {
        type: String,
        required: true,
        match: /^\d{4}$/ // exactly 4 digits
    },
    brand: {
        type: String,
        required: true,
        enum: ['visa', 'mastercard', 'amex', 'discover', 'diners', 'jcb', 'other']
    },
    exp_month: {
        type: Number,
        required: true,
        min: 1,
        max: 12
    },
    exp_year: {
        type: Number,
        required: true,
        validate: {
            validator: (value) => {
                // Validate against the current year at validation time so the schema
                // doesn't freeze the minimum year at server start.
                return typeof value === 'number' && value >= new Date().getFullYear();
            },
            message: (props) => `Expiration year ${props.value} cannot be in the past`
        }
    },
    has_cvv: {
        type: Boolean,
        default: false
    },
    is_favorite: {
        type: Boolean,
        default: false
    },
    metadata: {
        azulOrderId: {
            type: String
        },
        notes: {
            type: String,
            maxlength: 500
        }
    }
}, {
    timestamps: true
});
// Indexes for efficient querying
paymentMethodSchema.index({ userId: 1, is_favorite: 1 }, { unique: true, partialFilterExpression: { is_favorite: true } });
paymentMethodSchema.index({ userId: 1, createdAt: -1 });
// Ensure only one favorite payment method per user
paymentMethodSchema.pre('save', function (next) {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        if (this.is_favorite && this.isModified('is_favorite')) {
            // If this payment method is being set as favorite, unset all others for this user
            const session = yield mongoose_1.default.startSession();
            try {
                yield session.withTransaction(() => tslib_1.__awaiter(this, void 0, void 0, function* () {
                    yield mongoose_1.default.model('PaymentMethod').updateMany({ userId: this.userId, _id: { $ne: this._id } }, { $set: { is_favorite: false } }, { session });
                }));
            }
            catch (err) {
                return next(err);
            }
            finally {
                yield session.endSession();
            }
        }
        next();
    });
});
exports.default = mongoose_1.default.model('PaymentMethod', paymentMethodSchema);
