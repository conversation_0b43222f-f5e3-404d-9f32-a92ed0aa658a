"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateCategoryId = exports.validateJobId = exports.validateOperatorId = exports.validateUserId = void 0;
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
// Factory to create ObjectId validators for different route parameter names
function makeIdValidator(paramName, errorMessage) {
    return (req, res, next) => {
        var _a;
        const value = String((_a = req.params[paramName]) !== null && _a !== void 0 ? _a : '').trim();
        req.params[paramName] = value; // persist sanitized value for downstream use
        if (!mongoose_1.default.Types.ObjectId.isValid(value)) {
            return res.status(400).json({ success: false, error: errorMessage });
        }
        return next();
    };
}
// Middleware to validate MongoDB ObjectId parameters
exports.validateUserId = makeIdValidator('userId', 'Invalid user ID format');
exports.validateOperatorId = makeIdValidator('operatorId', 'Invalid operator ID format');
exports.validateJobId = makeIdValidator('jobId', 'Invalid job ID format');
exports.validateCategoryId = makeIdValidator('categoryId', 'Invalid category ID format');
