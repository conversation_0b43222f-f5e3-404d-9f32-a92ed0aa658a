"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdempotencyConflictError = exports.NotFoundPaymentError = exports.PaymentProcessingError = exports.PaymentMethodExpiredError = exports.InvalidPaymentDataError = exports.PaymentError = void 0;
class PaymentError extends Error {
    /**
     * @param message Error message
     * @param statusCode HTTP status code (defaults to 500 for server errors)
     * @param code Optional machine-readable error code
     * @param cause Optional underlying error (will be assigned to `error.cause` for debugging)
     */
    constructor(message, statusCode = 500, code, cause) {
        // Call base Error ctor with message. We cannot reliably pass ErrorOptions across
        // different TS/lib targets, so we assign `cause` afterwards for compatibility.
        super(message);
        this.name = 'PaymentError';
        // Ensure instanceof works across transpilation boundaries by setting the
        // prototype using the actual constructor so subclasses retain their prototype.
        const ctor = (new.target || PaymentError);
        Object.setPrototypeOf(this, ctor.prototype);
        // Preserve stack trace if available, using the same constructor reference
        // so the stack frames point to the appropriate subclass when applicable.
        if (typeof Error.captureStackTrace === 'function') {
            Error.captureStackTrace(this, ctor);
        }
        this.statusCode = statusCode;
        this.code = code;
        if (cause !== undefined) {
            try {
                // Prefer platform `cause` if supported; otherwise attach manually for debugging
                this.cause = cause;
            }
            catch (_a) {
                // ignore
            }
        }
    }
}
exports.PaymentError = PaymentError;
class InvalidPaymentDataError extends PaymentError {
    constructor(message = 'Invalid payment data') {
        super(message, 400, 'INVALID_PAYMENT_DATA');
        this.name = 'InvalidPaymentDataError';
    }
}
exports.InvalidPaymentDataError = InvalidPaymentDataError;
class PaymentMethodExpiredError extends PaymentError {
    constructor(message = 'Payment method expired') {
        super(message, 400, 'PAYMENT_METHOD_EXPIRED');
        this.name = 'PaymentMethodExpiredError';
    }
}
exports.PaymentMethodExpiredError = PaymentMethodExpiredError;
class PaymentProcessingError extends PaymentError {
    constructor(message = 'Payment processing failed') {
        super(message, 502, 'PAYMENT_PROCESSING_ERROR');
        this.name = 'PaymentProcessingError';
    }
}
exports.PaymentProcessingError = PaymentProcessingError;
class NotFoundPaymentError extends PaymentError {
    constructor(message = 'Resource not found') {
        super(message, 404, 'NOT_FOUND');
        this.name = 'NotFoundPaymentError';
    }
}
exports.NotFoundPaymentError = NotFoundPaymentError;
class IdempotencyConflictError extends PaymentError {
    constructor(message = 'Idempotency key conflict: request differs from previously stored request') {
        super(message, 409, 'IDEMPOTENCY_CONFLICT');
        this.name = 'IdempotencyConflictError';
    }
}
exports.IdempotencyConflictError = IdempotencyConflictError;
exports.default = PaymentError;
