import { Request, Response } from 'express';
import { Account, Job, Bid, OperatorProfile, NotificationLog } from '../database/schemas';
import { getAuthenticatedUser } from '../utils/auth';
import { sendCustomNotification, getUserNotificationToken } from './notifications';

// Helper function to safely compare IDs
function isSameId(id1: any, id2: any): boolean {
  if (!id1 || !id2) return false;
  return String(id1).trim() === String(id2).trim();
}

// Create a bid for a job
export const createBid = async (req: Request, res: Response): Promise<any> => {
  try {
    const user = getAuthenticatedUser(req);
    const userId = user?._id;
    const { jobId } = req.params;
    const { amount, message, estimatedDuration, proposedStartDate, proposedStartTime } = req.body;

    if (!userId) {
      return res.status(401).json({ success: false, error: 'Unauthorized' });
    }

    // Validate required fields
    if (!amount || !message || !estimatedDuration || !proposedStartDate || !proposedStartTime) {
      return res.status(400).json({ 
        success: false, 
        error: 'Missing required fields: amount, message, estimatedDuration, proposedStartDate, proposedStartTime' 
      });
    }

    // Check if user is an operator
    const account = await Account.findById(userId);
    if (!account || !account.booleans.isOperator) {
      return res.status(403).json({ success: false, error: 'Only operators can place bids' });
    }

    // Get job and validate
    const job = await Job.findById(jobId);
    if (!job) {
      return res.status(404).json({ success: false, error: 'Job not found' });
    }

    if (job.status !== 'pending') {
      return res.status(400).json({ success: false, error: 'Job is no longer accepting bids' });
    }

    // Check if operator owns this job (can't bid on own job)
    if (isSameId(job.ownerId, userId)) {
      return res.status(400).json({ success: false, error: 'Cannot bid on your own job' });
    }

    // Check if operator is authorized for this category
    const operatorProfile = await OperatorProfile.findOne({ accountId: userId });
    if (!operatorProfile) {
      return res.status(400).json({ success: false, error: 'Operator profile not found' });
    }

    if (!operatorProfile.authorizedCategories.includes(job.category)) {
      return res.status(403).json({ 
        success: false, 
        error: 'Not authorized to bid on jobs in this category' 
      });
    }

    if (!operatorProfile.isAvailable) {
      return res.status(400).json({ success: false, error: 'Operator is currently unavailable' });
    }

    // Check if operator already has a bid for this job
    const existingBid = await Bid.findOne({ jobId, operatorId: userId });
    if (existingBid) {
      return res.status(400).json({ success: false, error: 'You have already placed a bid for this job' });
    }

    // Create the bid
    const bid = new Bid({
      jobId,
      operatorId: userId,
      amount: Number(amount),
      message,
      estimatedDuration,
      proposedStartDate,
      proposedStartTime
    });

    await bid.save();

    // Update job's total bids count
    job.totalBids += 1;
    await job.save();

    // Update operator's bid statistics
    operatorProfile.totalBidsMade += 1;
    await operatorProfile.save();

    // Notify job owner about new bid
    const jobOwnerNotificationToken = await getUserNotificationToken(job.ownerId);
    if (jobOwnerNotificationToken.success && jobOwnerNotificationToken.notificationToken) {
      await sendCustomNotification(jobOwnerNotificationToken.notificationToken, {
        title: 'New Bid Received',
        message: `You received a new bid of $${amount} for your ${job.category} job`,
        data: { jobId, bidId: bid._id, type: 'bid_placed' },
        soundOn: true
      });
    }

    // Log the notification
    await NotificationLog.create({
      recipientId: job.ownerId,
      type: 'bid_placed',
      title: 'New Bid Received',
      message: `You received a new bid of $${amount} for your ${job.category} job`,
      data: { jobId, bidId: bid._id, operatorId: userId },
      jobId,
      bidId: bid._id
    });

    res.status(201).json({
      success: true,
      message: 'Bid placed successfully',
      bid
    });

  } catch (error) {
    console.error('Error creating bid:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

// Get all bids for a job (for job owners)
export const getJobBids = async (req: Request, res: Response): Promise<any> => {
  try {
    const user = getAuthenticatedUser(req);
    const userId = user?._id;
    const { jobId } = req.params;
    const { page = 1, limit = 10, status } = req.query;

    if (!userId) {
      return res.status(401).json({ success: false, error: 'Unauthorized' });
    }

    // Get job and verify ownership
    const job = await Job.findById(jobId);
    if (!job) {
      return res.status(404).json({ success: false, error: 'Job not found' });
    }

    if (!isSameId(job.ownerId, userId)) {
      return res.status(403).json({ success: false, error: 'Access denied' });
    }

    // Build query
    const query: any = { jobId };
    if (status && ['pending', 'accepted', 'rejected', 'withdrawn'].includes(status as string)) {
      query.status = status;
    }

    // Get bids with operator details
    const bids = await Bid.find(query)
      .populate('operatorId', 'user.name user.surname user.username user.profile_picture')
      .sort({ createdAt: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit));

    // Get operator profiles for additional info
    const bidsWithProfiles = await Promise.all(
      bids.map(async (bid) => {
        const operatorProfile = await OperatorProfile.findOne({ accountId: bid.operatorId });
        return {
          ...bid.toObject(),
          operatorProfile: operatorProfile ? {
            rating: operatorProfile.rating,
            totalJobsCompleted: operatorProfile.totalJobsCompleted,
            skills: operatorProfile.skills,
            description: operatorProfile.description
          } : null
        };
      })
    );

    const total = await Bid.countDocuments(query);

    res.json({
      success: true,
      bids: bidsWithProfiles,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });

  } catch (error) {
    console.error('Error getting job bids:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

// Accept a bid (for job owners)
export const acceptBid = async (req: Request, res: Response): Promise<any> => {
  try {
    const user = getAuthenticatedUser(req);
    const userId = user?._id;
    const { bidId } = req.params;

    if (!userId) {
      return res.status(401).json({ success: false, error: 'Unauthorized' });
    }

    // Get bid and populate job
    const bid = await Bid.findById(bidId);
    if (!bid) {
      return res.status(404).json({ success: false, error: 'Bid not found' });
    }

    const job = await Job.findById(bid.jobId);
    if (!job) {
      return res.status(404).json({ success: false, error: 'Job not found' });
    }

    // Verify job ownership
    if (!isSameId(job.ownerId, userId)) {
      return res.status(403).json({ success: false, error: 'Access denied' });
    }

    // Check if job is still pending
    if (job.status !== 'pending') {
      return res.status(400).json({ success: false, error: 'Job is no longer accepting bids' });
    }

    // Check if bid is still pending
    if (bid.status !== 'pending') {
      return res.status(400).json({ success: false, error: 'Bid is no longer available' });
    }

    // Update bid status to accepted
    bid.status = 'accepted';
    await bid.save();

    // Update job with accepted bid details
    job.status = 'accepted';
    job.acceptedBidId = (bid._id as any).toString();
    job.assignedOperatorId = bid.operatorId;
    job.finalPrice = bid.amount;
    await job.save();
    // Reject all other pending bids for this job
    await Bid.updateMany(
      { jobId: bid.jobId, _id: { $ne: bid._id }, status: 'pending' },
      { status: 'rejected' }
    );

    // Update operator statistics
    const operatorProfile = await OperatorProfile.findOne({ accountId: bid.operatorId });
    if (operatorProfile) {
      operatorProfile.totalBidsWon += 1;
      await operatorProfile.save();
    }

    // Notify the winning operator
    const operatorNotificationToken = await getUserNotificationToken(bid.operatorId);
    if (operatorNotificationToken.success && operatorNotificationToken.notificationToken) {
      await sendCustomNotification(operatorNotificationToken.notificationToken, {
        title: 'Bid Accepted!',
        message: `Congratulations! Your bid of $${bid.amount} has been accepted`,
        data: { jobId: job._id, bidId: bid._id, type: 'bid_accepted' },
        soundOn: true
      });
    }

    // Log notification for winning operator
    await NotificationLog.create({
      recipientId: bid.operatorId,
      type: 'bid_accepted',
      title: 'Bid Accepted!',
      message: `Congratulations! Your bid of $${bid.amount} has been accepted`,
      data: { jobId: job._id, bidId: bid._id },
      jobId: (job._id as any).toString(),
      bidId: (bid._id as any).toString()
    });

    // Notify rejected operators
    const rejectedBids = await Bid.find({ 
      jobId: bid.jobId, 
      _id: { $ne: bid._id }, 
      status: 'rejected' 
    });

    for (const rejectedBid of rejectedBids) {
      const rejectedOperatorToken = await getUserNotificationToken(rejectedBid.operatorId);
      if (rejectedOperatorToken.success && rejectedOperatorToken.notificationToken) {
        await sendCustomNotification(rejectedOperatorToken.notificationToken, {
          title: 'Bid Not Selected',
          message: `Your bid for the ${job.category} job was not selected`,
          data: { jobId: job._id, bidId: rejectedBid._id, type: 'bid_rejected' },
          soundOn: false
        });
      }

      // Log notification for rejected operators
      await NotificationLog.create({
        recipientId: rejectedBid.operatorId,
        type: 'bid_rejected',
        title: 'Bid Not Selected',
        message: `Your bid for the ${job.category} job was not selected`,
        data: { jobId: job._id, bidId: rejectedBid._id },
        jobId: (job._id as any).toString(),
        bidId: (rejectedBid._id as any).toString()
      });
    }

    res.json({
      success: true,
      message: 'Bid accepted successfully',
      job,
      acceptedBid: bid
    });

  } catch (error) {
    console.error('Error accepting bid:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

// Get operator's bids
export const getOperatorBids = async (req: Request, res: Response): Promise<any> => {
  try {
    const user = getAuthenticatedUser(req);
    const userId = user?._id;
    const { page = 1, limit = 10, status } = req.query;

    if (!userId) {
      return res.status(401).json({ success: false, error: 'Unauthorized' });
    }

    // Check if user is an operator
    const account = await Account.findById(userId);
    if (!account || !account.booleans.isOperator) {
      return res.status(403).json({ success: false, error: 'Access denied' });
    }

    // Build query
    const query: any = { operatorId: userId };
    if (status && ['pending', 'accepted', 'rejected', 'withdrawn'].includes(status as string)) {
      query.status = status;
    }

    // Get bids with job details
    const bids = await Bid.find(query)
      .populate('jobId', 'category description price date hour status ownerId')
      .sort({ createdAt: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit));

    const total = await Bid.countDocuments(query);

    res.json({
      success: true,
      bids,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });

  } catch (error) {
    console.error('Error getting operator bids:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

// Withdraw a bid (for operators)
export const withdrawBid = async (req: Request, res: Response): Promise<any> => {
  try {
    const user = getAuthenticatedUser(req);
    const userId = user?._id;
    const { bidId } = req.params;

    if (!userId) {
      return res.status(401).json({ success: false, error: 'Unauthorized' });
    }

    // Get bid and verify ownership
    const bid = await Bid.findById(bidId);
    if (!bid) {
      return res.status(404).json({ success: false, error: 'Bid not found' });
    }

    if (!isSameId(bid.operatorId, userId)) {
      return res.status(403).json({ success: false, error: 'Access denied' });
    }

    // Check if bid can be withdrawn
    if (bid.status !== 'pending') {
      return res.status(400).json({ 
        success: false, 
        error: 'Cannot withdraw bid that is not pending' 
      });
    }

    // Update bid status
    bid.status = 'withdrawn';
    await bid.save();

    // Update job's total bids count
    const job = await Job.findById(bid.jobId);
    if (job) {
      job.totalBids = Math.max(0, job.totalBids - 1);
      await job.save();
    }

    res.json({
      success: true,
      message: 'Bid withdrawn successfully',
      bid
    });

  } catch (error) {
    console.error('Error withdrawing bid:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
};
