import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import CreditCard from '../atoms/cards/CreditCard'
import { Ionicons } from '@expo/vector-icons'


interface CCPanel {
    name: string,
    surname: string,
    last4: string,
    exp: {
        month: number,
        year: number
    },
}

const CreditCardPanel = ({
    name,
    surname,
    last4,
    exp,
}: CCPanel) => {
    return (
        <View style={{ marginBottom: 20 }}>
            <CreditCard
                name={name}
                surname={surname}
                hiddenCardNumber={`**** **** **** ${last4}`}
                cardExp={new Date(exp.year, exp.month)} // October 2025 (month is 0-based)
                cardCVC='***'
            />

            <View style={{ flexDirection: 'row', justifyContent: 'center', gap: 15, marginTop: 15 }}>
                <View style={{ flexDirection: 'column', gap: 5, justifyContent: 'center', alignItems: 'center' }}>
                    <TouchableOpacity style={styles.buttonCircle}>
                        <Ionicons name='radio-button-on-outline' size={24} />
                    </TouchableOpacity>
                    <Text style={{ fontFamily: 'Montserrat', fontSize: 11,  fontWeight: 500 }}>Seleccionar</Text>
                </View>
                <View style={{ flexDirection: 'column', gap: 5, justifyContent: 'center', alignItems: 'center' }}>
                    <TouchableOpacity style={[styles.buttonCircle, { backgroundColor: '#ff634798' }]}>
                        <Ionicons name='trash-bin-outline' size={24} color={'#FF6347'} />
                    </TouchableOpacity>
                    <Text style={{ fontFamily: 'Montserrat', fontSize: 11, color: '#FF6347', fontWeight: 600 }}>Cancelar</Text>
                </View>
            </View>
        </View>
    )
}

export default CreditCardPanel

const styles = StyleSheet.create({
    buttonCircle: {
        height: 50,
        width: 50,

        backgroundColor: "#b6b6b65c",

        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 50
    }
})