import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';
import Toast from 'react-native-toast-message';
import { StatusBarExpo } from '@/components/templates/Statusbar';


export default function RootLayout() {
  const colorScheme = useColorScheme();


  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="notifications" options={{ headerShown: false, animation: 'none' }} />
        <Stack.Screen name="agent" options={{ headerShown: false, animation: 'none' }} />
        <Stack.Screen name="support" options={{ headerShown: false, animation: 'none' }} />
        <Stack.Screen name="offer" options={{ headerShown: false, animation: 'none' }} />
        <Stack.Screen name="map" options={{ headerShown: false, animation: 'none' }} />
      </Stack>
      <StatusBarExpo style="dark" />
    </ThemeProvider>
  );
}
