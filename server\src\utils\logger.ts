// Minimal structured logger for the project

type LogMeta = { [key: string]: any } | undefined;

function safeSerialize(obj: any): any {
  try {
    return JSON.parse(JSON.stringify(obj));
  } catch {
    return String(obj);
  }
};

export const logger = {
  error: (msg: string, meta?: LogMeta) => {
    const out: any = { level: 'error', message: msg };
    if (meta) out.meta = safeSerialize(meta);
    // Console used as a fallback sink; in production this should be replaced with a structured logger
    console.error(JSON.stringify(out));
  },
  warn: (msg: string, meta?: LogMeta) => {
    const out: any = { level: 'warn', message: msg };
    if (meta) out.meta = safeSerialize(meta);
    console.warn(JSON.stringify(out));
  },
  info: (msg: string, meta?: LogMeta) => {
    const out: any = { level: 'info', message: msg };
    if (meta) out.meta = safeSerialize(meta);
    console.info(JSON.stringify(out));
  },
  debug: (msg: string, meta?: LogMeta) => {
    const out: any = { level: 'debug', message: msg };
    if (meta) out.meta = safeSerialize(meta);
    console.log(JSON.stringify(out));
  }
};
