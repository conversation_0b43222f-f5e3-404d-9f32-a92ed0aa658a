import { Router } from 'express';
import { AuthenticateTokenOAuth } from '../middleware/authentication';
import {
  cancelJob,
  getCancellationHistory,
  getCreditBalance,
  getCreditTransactionHistory,
  withdrawCreditBalance
} from '../controllers/cancellation';

const router = Router();

// Job Cancellation Routes
router.post('/jobs/:jobId/cancel', AuthenticateTokenOAuth, cancelJob);
router.get('/history', AuthenticateTokenOAuth, getCancellationHistory);

// Credit Balance Routes
router.get('/credit-balance', AuthenticateTokenOAuth, getCreditBalance);
router.get('/credit-transactions', AuthenticateTokenOAuth, getCreditTransactionHistory);
router.post('/credit-balance/withdraw', AuthenticateTokenOAuth, withdrawCreditBalance);

export default router;
