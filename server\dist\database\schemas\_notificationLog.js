"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NOTIFICATION_TYPES = void 0;
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importStar(require("mongoose"));
// Nnotification types
exports.NOTIFICATION_TYPES = [
    'job_created',
    'bid_placed',
    'bid_accepted',
    'bid_rejected',
    'job_completed',
    'operator_request_submitted',
    'operator_request_approved',
    'operator_request_rejected',
    // New payment-related types
    'payment_escrowed',
    'refund_processed',
    'payout_released',
    'cancellation_penalty',
    'operator_no_show'
];
const notificationLogSchema = new mongoose_1.default.Schema({
    recipientId: {
        type: String,
        required: true,
        ref: 'Account'
    },
    type: {
        type: String,
        enum: exports.NOTIFICATION_TYPES,
        required: true
    },
    title: {
        type: String,
        required: true
    },
    message: {
        type: String,
        required: true
    },
    data: {
        type: mongoose_1.Schema.Types.Mixed,
        default: {}
    },
    jobId: {
        type: String,
        ref: 'Job'
    },
    bidId: {
        type: String,
        ref: 'Bid'
    },
    isRead: {
        type: Boolean,
        default: false
    },
    sentAt: {
        type: Date,
        default: Date.now
    },
    readAt: {
        type: Date
    }
}, {
    timestamps: true
});
// Indexes for efficient querying
notificationLogSchema.index({ recipientId: 1, sentAt: -1 });
notificationLogSchema.index({ recipientId: 1, isRead: 1 });
notificationLogSchema.index({ type: 1 });
notificationLogSchema.index({ jobId: 1 });
exports.default = mongoose_1.default.model('NotificationLog', notificationLogSchema);
