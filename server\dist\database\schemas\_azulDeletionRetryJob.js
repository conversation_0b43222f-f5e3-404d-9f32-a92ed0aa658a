"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const azulDeletionRetryJobSchema = new mongoose_1.default.Schema({
    paymentMethodId: {
        type: String,
        required: true,
        index: true
    },
    dataVaultToken: {
        type: String,
        required: true,
        // Token should be encrypted/hashed for security
        select: false // Exclude by default from queries for security
    },
    userId: {
        type: String,
        required: true,
        index: true
    },
    attemptCount: {
        type: Number,
        default: 0,
        min: 0
    },
    maxAttempts: {
        type: Number,
        default: 5,
        min: 1,
        max: 10
    },
    lastError: {
        type: String,
        maxlength: 1000
    },
    nextRetryAt: {
        type: Date,
        required: true,
        index: true
    },
    baseDelayMs: {
        type: Number,
        default: 5000, // 5 seconds base delay
        min: 1000,
        max: 300000 // Max 5 minutes
    },
    status: {
        type: String,
        enum: ['pending', 'processing', 'succeeded', 'failed'],
        default: 'pending',
        index: true
    },
    completedAt: {
        type: Date
    },
    metadata: {
        originalDeletedAt: Date,
        paymentMethodType: String,
        errorHistory: [{
                attempt: Number,
                error: String,
                timestamp: Date
            }]
    }
}, {
    timestamps: true,
    // TTL index to auto-cleanup completed jobs after 30 days
    expireAfterSeconds: 30 * 24 * 60 * 60 // 30 days
});
// Compound index for efficient retry job processing
azulDeletionRetryJobSchema.index({
    status: 1,
    nextRetryAt: 1
});
// Unique constraint to prevent duplicate jobs for same payment method
azulDeletionRetryJobSchema.index({
    paymentMethodId: 1
}, {
    unique: true,
    partialFilterExpression: {
        status: { $in: ['pending', 'processing'] }
    }
});
const AzulDeletionRetryJob = mongoose_1.default.model('AzulDeletionRetryJob', azulDeletionRetryJobSchema);
exports.default = AzulDeletionRetryJob;
