"use strict";
// Central definition of document requirement categories used across the system, clients can render friendly labels from the map below. Keep this list small and stable.
Object.defineProperty(exports, "__esModule", { value: true });
exports.DOCUMENT_REQUIREMENT_LABELS = exports.DOCUMENT_REQUIREMENT_CATEGORIES = void 0;
exports.isDocumentRequirementCategory = isDocumentRequirementCategory;
exports.DOCUMENT_REQUIREMENT_CATEGORIES = [
    'none',
    'criminal',
    'qualification',
    'identity', // e.g., national ID, passport, driver license, etc.
    'address', // e.g., proof of address / utility bill / bank statement
];
exports.DOCUMENT_REQUIREMENT_LABELS = {
    none: 'Ninguno',
    criminal: 'Antecedentes penales',
    qualification: 'Certificaciones y calificaciones',
    identity: 'Documento de identidad',
    address: 'Comprobante de domicilio',
};
function isDocumentRequirementCategory(value) {
    return typeof value === 'string' && exports.DOCUMENT_REQUIREMENT_CATEGORIES.includes(value);
}
