"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.listOperatorApplications = exports.rejectOperatorApplication = exports.approveOperatorApplication = exports.getApplicationDocumentRequirements = exports.submitOperatorApplication = void 0;
const tslib_1 = require("tslib");
const schemas_1 = require("../database/schemas");
const auth_1 = require("../utils/auth");
const upload_1 = require("../utils/upload");
const uuid_1 = require("uuid");
const notifications_1 = require("./notifications");
const jobCategoryService_1 = tslib_1.__importDefault(require("../services/jobCategoryService"));
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const documentRequirements_1 = require("../types/documentRequirements");
// Helper: notify a single user and log it
function notifyUser(userId_1, _a) {
    return tslib_1.__awaiter(this, arguments, void 0, function* (userId, { title, message, type, data = {}, soundOn = true, badgeCount = 1, }) {
        const tokenResult = yield (0, notifications_1.getUserNotificationToken)(userId);
        const payloadData = Object.assign({ type }, data);
        let notificationSent = false;
        if (tokenResult.success && tokenResult.notificationToken) {
            try {
                yield (0, notifications_1.sendCustomNotification)(tokenResult.notificationToken, {
                    title,
                    message,
                    data: payloadData,
                    soundOn,
                    badgeCount,
                });
                notificationSent = true;
            }
            catch (error) {
                console.error(`Failed to send notification to user ${userId}:`, error);
            }
        }
        try {
            yield schemas_1.NotificationLog.create({
                recipientId: userId,
                type,
                title,
                message,
                data: payloadData,
                notificationSent, // Track whether push notification was successful
            });
        }
        catch (error) {
            console.error(`Failed to log notification for user ${userId}:`, error);
        }
    });
}
// Helper: notify all admins and log per admin
function notifyAdmins(_a) {
    return tslib_1.__awaiter(this, arguments, void 0, function* ({ title, message, type, data = {}, soundOn = true, badgeCount = 1, }) {
        const admins = yield schemas_1.Account.find({ 'booleans.isAdmin': true }).select('_id notifications.expo_push_token user.name');
        yield Promise.all(admins.map((adminDoc) => tslib_1.__awaiter(this, void 0, void 0, function* () {
            var _a, _b;
            const adminIdStr = ((_b = (_a = adminDoc._id) === null || _a === void 0 ? void 0 : _a.toString) === null || _b === void 0 ? void 0 : _b.call(_a)) || String(adminDoc._id);
            yield notifyUser(adminIdStr, { title, message, type, data, soundOn, badgeCount });
        })));
    });
}
// Submit an operator application (existing user)
const submitOperatorApplication = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    try {
        const user = (0, auth_1.getAuthenticatedUser)(req);
        const userId = (_a = user === null || user === void 0 ? void 0 : user._id) === null || _a === void 0 ? void 0 : _a.toString();
        if (!userId) {
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        }
        // Disallow creating a new application if there's already a pending one
        const existingPending = yield schemas_1.OperatorApplication.findOne({ applicantId: userId, status: 'pending' });
        if (existingPending) {
            return res.status(400).json({
                success: false,
                error: 'Ya tienes una solicitud pendiente. Debe ser rechazada antes de enviar otra.'
            });
        }
        // Disallow creating an application if the user already has an operator profile
        const existingProfile = yield schemas_1.OperatorProfile.findOne({ accountId: userId });
        if (existingProfile) {
            return res.status(400).json({
                success: false,
                error: 'Ya tienes un perfil de operador. No puedes enviar otra solicitud.'
            });
        }
        const { authorizedCategories: rawAuthorizedCategories, description = '', skills: rawSkills = [] } = req.body;
        // Normalize fields to correct types (supports multipart/form-data)
        const parseArray = (input) => {
            if (Array.isArray(input))
                return input;
            if (typeof input === 'string') {
                try {
                    const parsed = JSON.parse(input);
                    if (Array.isArray(parsed))
                        return parsed;
                }
                catch (e) {
                    // Invalid JSON, fall through to split logic
                }
                return input.split(',').map((s) => s.trim()).filter(Boolean);
            }
            return [];
        };
        const authorizedCategories = parseArray(rawAuthorizedCategories);
        const skills = parseArray(rawSkills);
        if (!authorizedCategories || !Array.isArray(authorizedCategories) || authorizedCategories.length === 0) {
            return res.status(400).json({ success: false, error: 'Se requiere al menos una categoría autorizada' });
        }
        const normalizedAuthorized = authorizedCategories.map((c) => String(c).trim().toLowerCase());
        // Fetch active categories and validate against them
        const activeCategories = yield jobCategoryService_1.default.getActiveCategories();
        const validCategoryNames = new Set(activeCategories.map((cat) => String(cat.name).trim().toLowerCase()));
        const invalid = normalizedAuthorized.filter((c) => !validCategoryNames.has(c));
        if (invalid.length) {
            return res.status(400).json({ success: false, error: `Categorías inválidas: ${invalid.join(', ')}` });
        }
        // Determine required document categories from selected authorized categories
        const categoriesDocsRequired = yield Promise.all(normalizedAuthorized.map((name) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
            const cat = yield jobCategoryService_1.default.getCategoryByName(name);
            return (cat === null || cat === void 0 ? void 0 : cat.documents_required) || ['none'];
        })));
        const requiredSet = new Set();
        for (const arr of categoriesDocsRequired) {
            for (const c of arr) {
                if (c && c !== 'none' && (0, documentRequirements_1.isDocumentRequirementCategory)(c))
                    requiredSet.add(c);
            }
        }
        const requiredCategories = requiredSet.size > 0 ? Array.from(requiredSet) : ['none'];
        // Upload any provided documents (optional)
        const files = req.files || [];
        const documents = [];
        // Parse document-category mapping from body: supports 'documentsMeta' or 'documentCategories'
        const rawMeta = ((_e = (_c = (_b = req.body) === null || _b === void 0 ? void 0 : _b.documentsMeta) !== null && _c !== void 0 ? _c : (_d = req.body) === null || _d === void 0 ? void 0 : _d.documentCategories) !== null && _e !== void 0 ? _e : (_f = req.body) === null || _f === void 0 ? void 0 : _f.documents_categories);
        const filenameToCategory = new Map();
        const consumeCategory = (v) => {
            if (typeof v !== 'string')
                return undefined;
            const key = v.trim().toLowerCase();
            return (0, documentRequirements_1.isDocumentRequirementCategory)(key) ? key : undefined;
        };
        try {
            if (rawMeta) {
                const parsed = typeof rawMeta === 'string' ? JSON.parse(rawMeta) : rawMeta;
                if (Array.isArray(parsed)) {
                    // Expect array of { filename, category }
                    for (const item of parsed) {
                        const cat = consumeCategory(item === null || item === void 0 ? void 0 : item.category);
                        if (cat && typeof (item === null || item === void 0 ? void 0 : item.filename) === 'string') {
                            filenameToCategory.set(String(item.filename), cat);
                        }
                    }
                }
                else if (parsed && typeof parsed === 'object') {
                    // Expect map of filename -> category
                    for (const [k, v] of Object.entries(parsed)) {
                        const cat = consumeCategory(v);
                        if (cat)
                            filenameToCategory.set(k, cat);
                    }
                }
            }
        }
        catch (e) {
            return res.status(400).json({ success: false, error: 'Formato inválido para documentosMeta/documentCategories' });
        }
        // Validate files before processing
        const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
        const ALLOWED_MIME_TYPES = new Set([
            'application/pdf',
            'image/jpeg',
            'image/png',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ]);
        for (const file of files) {
            if (file.size > MAX_FILE_SIZE) {
                return res.status(400).json({
                    success: false,
                    error: `El archivo '${file.originalname}' excede el tamaño máximo de 10MB`
                });
            }
            if (!ALLOWED_MIME_TYPES.has(file.mimetype)) {
                return res.status(400).json({
                    success: false,
                    error: `Tipo de archivo no permitido para '${file.originalname}'. Solo se permiten PDF, JPEG, PNG, DOC o DOCX`
                });
            }
            try {
                // Sanitize filename to prevent path traversal
                const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9._-]/g, '_');
                const fileName = `operator-applications/${userId}/${(0, uuid_1.v4)()}-${sanitizedName}`;
                const url = yield (0, upload_1.uploadFileToS3)(file.buffer, fileName);
                const category = filenameToCategory.get(file.originalname);
                documents.push({ name: file.originalname, url, type: file.mimetype, category });
            }
            catch (error) {
                console.error(`Failed to upload file ${file.originalname}:`, error);
                return res.status(500).json({
                    success: false,
                    error: `Error al subir el archivo '${file.originalname}'. Por favor, intenta de nuevo.`
                });
            }
        }
        // If some categories are required, ensure at least one document per category has been uploaded and categorized
        if (requiredCategories.length && !(requiredCategories.length === 1 && requiredCategories[0] === 'none')) {
            const uploadedCategories = new Set(documents.map((d) => d.category).filter((c) => !!c));
            const missing = requiredCategories.filter((c) => !uploadedCategories.has(c));
            if (missing.length) {
                return res.status(400).json({
                    success: false,
                    error: `Faltan documentos requeridos para: ${missing.join(', ')}`,
                    requiredCategories,
                    received: Array.from(uploadedCategories),
                });
            }
        }
        const application = yield schemas_1.OperatorApplication.create({
            applicantId: userId,
            authorizedCategories: normalizedAuthorized,
            description,
            skills,
            documents,
            documents_required: requiredCategories,
            status: 'pending',
        });
        // Notify all admins in Spanish
        const title = 'Nueva solicitud de operador';
        const message = `El usuario ha enviado una solicitud para convertirse en operador.`;
        const applicationIdStr = ((_h = (_g = application._id) === null || _g === void 0 ? void 0 : _g.toString) === null || _h === void 0 ? void 0 : _h.call(_g)) || String(application._id);
        yield notifyAdmins({
            title,
            message,
            type: 'operator_request_submitted',
            data: { applicationId: applicationIdStr, applicantId: userId },
            soundOn: true,
            badgeCount: 1,
        });
        // Notify applicant
        yield notifyUser(userId, {
            title: 'Solicitud enviada',
            message: 'Tu solicitud para ser operador ha sido enviada y será revisada por un administrador.',
            type: 'operator_request_submitted',
            data: { applicationId: applicationIdStr },
            soundOn: true,
            badgeCount: 1,
        });
        return res.status(201).json({ success: true, application });
    }
    catch (error) {
        console.error('Error submitting operator application:', error);
        return res.status(500).json({ success: false, error: 'Error interno del servidor' });
    }
});
exports.submitOperatorApplication = submitOperatorApplication;
// Public: compute union of required document categories for provided job categories
const getApplicationDocumentRequirements = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const raw = ((_a = req.query.categories) !== null && _a !== void 0 ? _a : (_b = req.body) === null || _b === void 0 ? void 0 : _b.categories);
        const parseArray = (input) => {
            if (Array.isArray(input))
                return input;
            if (typeof input === 'string') {
                try {
                    const p = JSON.parse(input);
                    if (Array.isArray(p))
                        return p;
                }
                catch (_a) { }
                return input.split(',').map((s) => s.trim()).filter(Boolean);
            }
            return [];
        };
        const categories = parseArray(raw).map((s) => s.toLowerCase());
        if (!categories.length) {
            return res.status(400).json({ success: false, error: 'Debe proporcionar categorías' });
        }
        const active = yield jobCategoryService_1.default.getActiveCategories();
        const activeSet = new Set(active.map((c) => String(c.name).toLowerCase()));
        const invalid = categories.filter((c) => !activeSet.has(c));
        if (invalid.length) {
            return res.status(400).json({ success: false, error: `Categorías inválidas: ${invalid.join(', ')}` });
        }
        const reqs = new Set();
        for (const c of categories) {
            const cat = yield jobCategoryService_1.default.getCategoryByName(c);
            const arr = (cat === null || cat === void 0 ? void 0 : cat.documents_required) || ['none'];
            for (const r of arr) {
                if (r !== 'none' && (0, documentRequirements_1.isDocumentRequirementCategory)(r))
                    reqs.add(r);
            }
        }
        const result = reqs.size ? Array.from(reqs) : ['none'];
        return res.json({ success: true, categories: result });
    }
    catch (error) {
        console.error('Error computing application document requirements:', error);
        return res.status(500).json({ success: false, error: 'Error interno del servidor' });
    }
});
exports.getApplicationDocumentRequirements = getApplicationDocumentRequirements;
// Admin approves an operator application
const approveOperatorApplication = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c;
    try {
        const admin = (0, auth_1.getAuthenticatedUser)(req);
        const adminId = (_a = admin === null || admin === void 0 ? void 0 : admin._id) === null || _a === void 0 ? void 0 : _a.toString();
        if (!adminId)
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        // Enforce admin-only access before proceeding
        if ((admin === null || admin === void 0 ? void 0 : admin.role) !== 'admin') {
            return res.status(403).json({ success: false, error: 'Admin access required' });
        }
        const { applicationId } = req.params;
        if (!mongoose_1.default.Types.ObjectId.isValid(applicationId)) {
            return res.status(400).json({ success: false, error: 'Invalid applicationId' });
        }
        const application = yield schemas_1.OperatorApplication.findById(applicationId);
        if (!application)
            return res.status(404).json({ success: false, error: 'Solicitud no encontrada' });
        if (application.status !== 'pending')
            return res.status(400).json({ success: false, error: 'La solicitud ya fue revisada' });
        application.status = 'approved';
        application.reviewedBy = new mongoose_1.default.Types.ObjectId(adminId);
        application.reviewedAt = new Date();
        yield application.save();
        // Notify applicant - approved
        const applicationIdStr = ((_c = (_b = application._id) === null || _b === void 0 ? void 0 : _b.toString) === null || _c === void 0 ? void 0 : _c.call(_b)) || String(application._id);
        yield notifyUser(String(application.applicantId), {
            title: 'Solicitud aprobada',
            message: '¡Felicidades! Tu solicitud para ser operador ha sido aprobada.',
            type: 'operator_request_approved',
            data: { applicationId: applicationIdStr },
        });
        // Notify all admins - approved
        yield notifyAdmins({
            title: 'Solicitud de operador aprobada',
            message: 'Se ha aprobado una solicitud de operador.',
            type: 'operator_request_approved',
            data: { applicationId: applicationIdStr, applicantId: String(application.applicantId) },
        });
        return res.json({ success: true, application });
    }
    catch (error) {
        console.error('Error approving application:', error);
        return res.status(500).json({ success: false, error: 'Error interno del servidor' });
    }
});
exports.approveOperatorApplication = approveOperatorApplication;
// Admin rejects an operator application
const rejectOperatorApplication = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c;
    try {
        const admin = (0, auth_1.getAuthenticatedUser)(req);
        const adminId = (_a = admin === null || admin === void 0 ? void 0 : admin._id) === null || _a === void 0 ? void 0 : _a.toString();
        if (!adminId)
            return res.status(401).json({ success: false, error: 'Unauthorized' });
        // Enforce admin-only access before proceeding
        if ((admin === null || admin === void 0 ? void 0 : admin.role) !== 'admin') {
            return res.status(403).json({ success: false, error: 'Admin access required' });
        }
        const { applicationId } = req.params;
        if (!mongoose_1.default.Types.ObjectId.isValid(applicationId)) {
            return res.status(400).json({ success: false, error: 'Invalid applicationId' });
        }
        const { note = '' } = req.body;
        const application = yield schemas_1.OperatorApplication.findById(applicationId);
        if (!application)
            return res.status(404).json({ success: false, error: 'Solicitud no encontrada' });
        if (application.status !== 'pending')
            return res.status(400).json({ success: false, error: 'La solicitud ya fue revisada' });
        application.status = 'rejected';
        application.reviewedBy = new mongoose_1.default.Types.ObjectId(adminId);
        application.reviewedAt = new Date();
        application.reviewNote = note;
        yield application.save();
        // Notify applicant - rejected (use helper for consistency)
        const title = 'Solicitud rechazada';
        const message = note
            ? `Tu solicitud para ser operador fue rechazada. Motivo: ${note}`
            : 'Tu solicitud para ser operador fue rechazada.';
        const applicationIdStr = ((_c = (_b = application._id) === null || _b === void 0 ? void 0 : _b.toString) === null || _c === void 0 ? void 0 : _c.call(_b)) || String(application._id);
        yield notifyUser(String(application.applicantId), {
            title,
            message,
            type: 'operator_request_rejected',
            data: { applicationId: applicationIdStr },
            soundOn: true,
            badgeCount: 1,
        });
        // Also notify all admins with an admin-specific type and payload
        yield notifyAdmins({
            title,
            message,
            type: 'operator_request_rejected_admin',
            data: { applicationId: applicationIdStr, note },
            soundOn: true,
            badgeCount: 1,
        });
        return res.json({ success: true, application });
    }
    catch (error) {
        console.error('Error rejecting application:', error);
        return res.status(500).json({ success: false, error: 'Error interno del servidor' });
    }
});
exports.rejectOperatorApplication = rejectOperatorApplication;
// List applications (admin)
const listOperatorApplications = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        // Admin authentication and authorization
        const user = (0, auth_1.getAuthenticatedUser)(req);
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ success: false, error: 'Admin access required' });
        }
        const { page = 1, limit = 20, status } = req.query;
        const pageNum = Math.max(1, parseInt(String(page), 10) || 1);
        const limitNum = Math.min(100, Math.max(1, parseInt(String(limit), 10) || 20)); // cap at 100
        const query = {};
        const validStatuses = ['pending', 'approved', 'rejected'];
        if (status && validStatuses.includes(String(status).toLowerCase())) {
            query.status = String(status).toLowerCase();
        }
        const applications = yield schemas_1.OperatorApplication.find(query)
            .sort({ createdAt: -1 })
            .limit(limitNum)
            .skip((pageNum - 1) * limitNum)
            .populate('applicantId', 'user.name user.surname user.username email');
        const total = yield schemas_1.OperatorApplication.countDocuments(query);
        return res.json({
            success: true,
            applications,
            pagination: {
                page: pageNum,
                limit: limitNum,
                total,
                pages: Math.ceil(total / limitNum),
            },
        });
    }
    catch (error) {
        console.error('Error listing applications:', error);
        return res.status(500).json({ success: false, error: 'Error interno del servidor' });
    }
});
exports.listOperatorApplications = listOperatorApplications;
