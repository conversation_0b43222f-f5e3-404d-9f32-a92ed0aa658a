import ButtonIcon from '@/components/atoms/buttons/ButtonIcon';
import CreditCard from '@/components/atoms/cards/CreditCard';
import { BottomSheetTextInput } from '@gorhom/bottom-sheet';
import React, { useEffect, useState } from 'react';
import { Text, View, StyleSheet } from 'react-native';


interface NewCardProps {
  onConfirm: () => void
}
const NewCardSheet = ({ onConfirm }: NewCardProps) => {
  const [data, setData] = useState({
    name: '',
    cardNumber: '',
    date: '',
    cvc: '',
  });

  const handleChange = (key: keyof typeof data, value: string) => {
    setData((prev) => ({ ...prev, [key]: value }));
  };
  const parseDate = (input: string): Date => {
    const [month, year] = input.split('/');
    if (!month || !year) return new Date();
    const parsedYear = year.length === 2 ? parseInt(`20${year}`) : parseInt(year);
    return new Date(parsedYear, parseInt(month) - 1);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Nueva Tarjeta</Text>

      <CreditCard
        name={data.name}
        surname=""
        cardNumber={data.cardNumber}
        cardExp={data.date.length > 4 ? parseDate(data.date) : null}
        cardCVC={data.cvc}
      />

      <View style={styles.form}>
        <Text style={styles.label}>Nombre</Text>
        <BottomSheetTextInput
          style={styles.input}
          value={data.name}
          onChangeText={(text) => handleChange('name', text)}
          placeholder="Nombre en la tarjeta"
        />

        <Text style={styles.label}>Número</Text>
        <BottomSheetTextInput
          style={styles.input}
          value={data.cardNumber}
          onChangeText={(text) => handleChange('cardNumber', text.replace(/\s/g, ''))}
          placeholder="1234 5678 9012 3456"
          keyboardType="numeric"
        />

        <Text style={styles.label}>Fecha de Expiración (MM/YY)</Text>
        <BottomSheetTextInput
          style={styles.input}
          value={data.date}
          onChangeText={(text) => handleChange('date', text)}
          placeholder="MM/YY"
          keyboardType="numeric"
        />

        <Text style={styles.label}>CVC</Text>
        <BottomSheetTextInput
          style={styles.input}
          value={data.cvc}
          onChangeText={(text) => handleChange('cvc', text.replace(/\D/g, ''))}
          placeholder="123"
          keyboardType="numeric"
          maxLength={4}
        />
      </View>

      <View style={{ width: '100%', marginTop: 20 }}>
        <ButtonIcon text='Añadir Nueva Tarjeta' onPress={onConfirm} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
  },
  title: {
    fontFamily: 'Montserrat',
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 16,
  },
  form: {
    marginTop: 20,
    gap: 12,
  },
  label: {
    fontSize: 12,
    color: '#444',
    marginBottom: 4,
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    paddingVertical: 10,
    paddingHorizontal: 12,
    fontSize: 14,
    color: '#111',
  },
});

export default NewCardSheet;
