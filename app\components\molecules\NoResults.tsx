import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import GhostLoader from '../atoms/loaders/Ghost'


interface NoResultsProps{
    title: string,
    subtitle: string
}
const NoResults = ({title, subtitle}: NoResultsProps) => {
    return (
        <View style={{ alignItems: 'center', justifyContent: 'center', height: 650 }}>
            <GhostLoader />
            <Text style={styles.title}>{title}</Text>
            <Text style={styles.subtitle}>{subtitle}</Text>
        </View>
    )
}

export default NoResults

const styles = StyleSheet.create({
    title: {
        fontFamily: 'Montserrat',
        fontSize: 18,
        fontWeight: 700,
        color: '#000'
    },
    subtitle: {
        marginTop: 8,
        fontFamily: 'Montserrat',
        fontSize: 14,
        fontWeight: 400,

        textAlign: 'center',
        paddingHorizontal: 40,
        lineHeight: 20,
        color: '#000000db'
    },
})