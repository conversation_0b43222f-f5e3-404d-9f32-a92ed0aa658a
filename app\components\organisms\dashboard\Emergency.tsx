import { Alert, Image, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import Map from '@/components/templates/Map'
import ButtonIcon from '@/components/atoms/buttons/ButtonIcon'
import { Ionicons } from '@expo/vector-icons'
import HorizontalScrollableList from '@/components/molecules/HorizontalScrollableList'
import CardTileListSmall from '@/components/atoms/cards/CardTileListSmall'

const Emergency = () => {
    const CATEGORIES = [
        {
            name: 'Limpieza',
            icon: require('@/assets/icons/broom.png')
        },
        {
            name: 'Construccion',
            icon: require('@/assets/icons/warnel.png')
        },
        {
            name: 'Electricidad',
            icon: require('@/assets/icons/electricity.png')
        },
        {
            name: 'Plomeria',
            icon: require('@/assets/icons/tap.png')
        },
        {
            name: 'Jardinaje',
            icon: require('@/assets/icons/wateringcan.png')
        },
        {
            name: '<PERSON><PERSON>',
            icon: ''
        }
    ]


    return (
        <View>
            <Text style={{ fontFamily: 'Montserrat', fontSize: 17, fontWeight: 700, marginBottom: 10, paddingHorizontal: 15 }}>Confirmar posicion</Text>
            <View style={{ height: 250, paddingHorizontal: 15 }}>
                <Map noCenterButton />
            </View>

            {/* Job Categories */}
            <Text style={{ fontFamily: 'Montserrat', fontSize: 17, fontWeight: 700, marginTop: 20,marginBottom: 10, paddingHorizontal: 15 }}>Categoria</Text>
            <HorizontalScrollableList noMargin>
                <View style={{ height: '100%', width: 15 }} />
                {
                    CATEGORIES.map(category => {

                        return (
                            <CardTileListSmall key={category.name}>
                                <Image
                                    source={category.icon}
                                    style={{
                                        width: 50,
                                        height: 50,
                                        resizeMode: 'cover'
                                    }}
                                />
                                <Text style={{ fontFamily: 'Montserrat', fontSize: 10 }}>{category.name}</Text>
                            </CardTileListSmall>
                        )
                    })
                }
            </HorizontalScrollableList>

            <Text style={{ fontFamily: 'Montserrat', fontSize: 17, fontWeight: 700,marginTop: 20, marginBottom: 10, paddingHorizontal: 15 }}>Costo</Text>
            <View style={{
                
                marginBottom: 20,
                paddingHorizontal: 15,
                flexDirection: 'column',
                gap: 10
            }}>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text style={{ fontFamily: 'Montserrat', fontSize: 16, fontWeight: 500 }}>Tarifa Emergencia</Text>
                    <Text style={{ fontFamily: 'Montserrat', fontSize: 13, fontWeight: 500 }}>6'000 DOP</Text>
                </View>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text style={{ fontFamily: 'Montserrat', fontSize: 16, fontWeight: 500 }}>ITBIS (18%)</Text>
                    <Text style={{ fontFamily: 'Montserrat', fontSize: 13, fontWeight: 500 }}>1'080 DOP</Text>
                </View>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text style={{ fontFamily: 'Montserrat', fontSize: 17, fontWeight: 700 }}>Total</Text>
                    <Text style={{ fontFamily: 'Montserrat', fontSize: 15, fontWeight: 700 }}>7'080 DOP</Text>
                </View>
            </View>

            <View style={{ paddingHorizontal: 15 }}>
                <ButtonIcon
                    text='Confirmar y Pagar'
                    icon={<Ionicons name='card-outline' size={24} />}
                    onPress={() => {
                        Alert.alert('Atencion', 'Esa funcionalidad esta en desarrollo')
                    }}
                />
            </View>


        </View>
    )
}

export default Emergency

const styles = StyleSheet.create({})